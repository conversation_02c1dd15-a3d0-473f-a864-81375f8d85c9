import { config } from 'dotenv';
const postgres = require('postgres');
import {
  chat,
  message,
  messageDeprecated,
  vote,
  voteDeprecated,
} from '../schema';
import { drizzle } from 'drizzle-orm/postgres-js';
import { inArray } from 'drizzle-orm';
import type { UIMessage } from 'ai';

config({
  path: '.env.local',
});

if (!process.env.POSTGRES_URL) {
  throw new Error('POSTGRES_URL environment variable is not set');
}

const client = postgres(process.env.POSTGRES_URL);
const db = drizzle(client);

const BATCH_SIZE = 50; // Process 10 chats at a time
const INSERT_BATCH_SIZE = 100; // Insert 100 messages at a time

type NewMessageInsert = {
  id: string;
  chatId: string;
  parts: any[];
  role: string;
  attachments: any[];
  createdAt: Date;
};

type NewVoteInsert = {
  messageId: string;
  chatId: string;
  isUpvoted: boolean;
};

// Valid roles for the database schema
type ValidMessageRole = 'user' | 'assistant' | 'system' | 'data';

// Function to filter and convert UIMessage to valid database message
function isValidMessageRole(role: string): role is ValidMessageRole {
  return ['user', 'assistant', 'system', 'data'].includes(role);
}

// Function to safely convert UIMessage content to string
function getMessageContent(message: any): string {
  if (typeof message.content === 'string') {
    return message.content;
  }
  if (Array.isArray(message.content)) {
    // Handle array content by extracting text parts
    return message.content
      .filter((part: any) => part.type === 'text')
      .map((part: any) => part.text)
      .join(' ');
  }
  return '';
}

async function createNewTable() {
  const chats = await db.select().from(chat);
  let processedCount = 0;

  // Process chats in batches
  for (let i = 0; i < chats.length; i += BATCH_SIZE) {
    const chatBatch = chats.slice(i, i + BATCH_SIZE);
    const chatIds = chatBatch.map((chat) => chat.id);

    // Fetch all messages and votes for the current batch of chats in bulk
    const allMessages = await db
      .select()
      .from(messageDeprecated)
      .where(inArray(messageDeprecated.chatId, chatIds));

    const allVotes = await db
      .select()
      .from(voteDeprecated)
      .where(inArray(voteDeprecated.chatId, chatIds));

    // Prepare batches for insertion
    const newMessagesToInsert: NewMessageInsert[] = [];
    const newVotesToInsert: NewVoteInsert[] = [];

    // Process each chat in the batch
    for (const chat of chatBatch) {
      processedCount++;
      console.info(`Processed ${processedCount}/${chats.length} chats`);

      // Filter messages and votes for this specific chat
      const messages = allMessages.filter((msg) => msg.chatId === chat.id);
      const votes = allVotes.filter((v) => v.chatId === chat.id);

      // Group messages into sections
      const messageSection: Array<UIMessage> = [];
      const messageSections: Array<Array<UIMessage>> = [];

      for (const message of messages) {
        const { role } = message;

        // Only process messages with valid roles
        if (!isValidMessageRole(role)) {
          console.warn(`Skipping message with invalid role: ${role}`);
          continue;
        }

        if (role === 'user' && messageSection.length > 0) {
          messageSections.push([...messageSection]);
          messageSection.length = 0;
        }

        // Convert message to UIMessage format
        const uiMessage: UIMessage = {
          id: message.id,
          role: role as ValidMessageRole,
          content: getMessageContent(message),
          createdAt: message.createdAt,
          parts: [{ type: 'text', text: getMessageContent(message) }],
        };

        messageSection.push(uiMessage);
      }

      if (messageSection.length > 0) {
        messageSections.push([...messageSection]);
      }

      // Process each message section
      for (const section of messageSections) {
        try {
          // Process the section directly without appendResponseMessages
          const projectedUISection: NewMessageInsert[] = [];

          // Process all messages in the section
          for (const message of section) {
            if (!isValidMessageRole(message.role)) {
              continue;
            }

            if (message.role === 'user') {
              projectedUISection.push({
                id: message.id,
                chatId: chat.id,
                parts: [{ type: 'text', text: message.content as string }],
                role: message.role,
                createdAt: message.createdAt || new Date(),
                attachments: [],
              });
            } else if (message.role === 'assistant') {
              // For assistant messages, try to preserve existing parts structure
              let parts: any[] = [];

              if (typeof message.content === 'string') {
                parts = [{ type: 'text', text: message.content }];
              } else if (Array.isArray(message.content)) {
                parts = message.content;
              } else {
                // Fallback for any other content type
                parts = [{ type: 'text', text: String(message.content || '') }];
              }

              projectedUISection.push({
                id: message.id,
                chatId: chat.id,
                parts: parts,
                role: message.role,
                createdAt: message.createdAt || new Date(),
                attachments: [],
              });
            }
          }

          // Add messages to batch
          for (const msg of projectedUISection) {
            newMessagesToInsert.push(msg);

            if (msg.role === 'assistant') {
              const voteByMessage = votes.find((v) => v.messageId === msg.id);
              if (voteByMessage) {
                newVotesToInsert.push({
                  messageId: msg.id,
                  chatId: msg.chatId,
                  isUpvoted: voteByMessage.isUpvoted,
                });
              }
            }
          }
        } catch (error) {
          console.error(`Error processing chat ${chat.id}: ${error}`);
        }
      }
    }

    // Batch insert messages
    for (let j = 0; j < newMessagesToInsert.length; j += INSERT_BATCH_SIZE) {
      const messageBatch = newMessagesToInsert.slice(j, j + INSERT_BATCH_SIZE);
      if (messageBatch.length > 0) {
        // Ensure all required fields are present
        const validMessageBatch = messageBatch.map((msg) => ({
          id: msg.id,
          chatId: msg.chatId,
          parts: msg.parts,
          role: msg.role,
          attachments: msg.attachments,
          createdAt: msg.createdAt,
        }));

        await db.insert(message).values(validMessageBatch);
      }
    }

    // Batch insert votes
    for (let j = 0; j < newVotesToInsert.length; j += INSERT_BATCH_SIZE) {
      const voteBatch = newVotesToInsert.slice(j, j + INSERT_BATCH_SIZE);
      if (voteBatch.length > 0) {
        await db.insert(vote).values(voteBatch);
      }
    }
  }

  console.info(`Migration completed: ${processedCount} chats processed`);
}

createNewTable()
  .then(() => {
    console.info('Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
