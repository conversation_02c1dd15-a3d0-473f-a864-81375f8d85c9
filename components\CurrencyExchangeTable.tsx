import React, { useEffect } from 'react';

interface CurrencyExchangeTableProps {
  className?: string;
}

const CurrencyExchangeTable: React.FC<CurrencyExchangeTableProps> = ({
  className,
}) => {
  useEffect(() => {
    // Charger le script TradingView
    const script = document.createElement('script');
    script.src =
      'https://s3.tradingview.com/external-embedding/embed-widget-forex-cross-rates.js';
    script.async = true;
    script.innerHTML = JSON.stringify({
      width: '100%',
      height: '100%',
      currencies: [
        'EUR',
        'USD',
        'JPY',
        'GBP',
        'CHF',
        'AUD',
        'CAD',
        'NZD',
        'CNY',
      ],
      isTransparent: true,
      colorTheme: 'dark',
      locale: 'en',
      largeChartUrl: '',
    });

    const container = document.getElementById('tradingview_currency_table');
    if (container) {
      container.appendChild(script);
    }

    return () => {
      if (container) {
        container.removeChild(script);
      }
    };
  }, []);

  return (
    <div
      className={`tradingview-widget-container ${className || ''}`}
      style={{ height: '400px' }}
    >
      <div id="tradingview_currency_table" style={{ height: '100%' }} />
    </div>
  );
};

export default CurrencyExchangeTable;
