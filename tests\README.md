# Tests Documentation

This directory contains automated tests for the AI Chatbot application. The tests are organized into different categories and use Playwright for end-to-end testing and API testing.

## Table of Contents

- [Overview](#overview)
- [Test Structure](#test-structure)
- [Running Tests](#running-tests)
- [Debugging Tests](#debugging-tests)
- [Adding New Tests](#adding-new-tests)
- [Best Practices](#best-practices)
- [Known Issues](#known-issues)

## Overview

The test suite is designed to verify the functionality of the AI Chatbot application, including:

- End-to-end tests for the chat interface
- API tests for the backend services
- Authentication tests (currently skipped due to instability in CI)
- Session management tests

## Test Structure

The tests are organized into the following directories:

- `e2e/`: End-to-end tests that simulate user interactions with the application
- `routes/`: API tests that verify the backend services
- `pages/`: Page object models that encapsulate the UI interactions
- `fixtures/`: Test fixtures and utilities
- `prompts/`: Test prompts used for chat interactions

## Running Tests

### Prerequisites

- Node.js 18 or later
- pnpm package manager

### Running All Tests

To run all tests:

```bash
pnpm test
```

### Running Specific Tests

To run a specific test file:

```bash
pnpm exec playwright test tests/e2e/chat.test.ts
```

To run tests with a specific tag:

```bash
pnpm exec playwright test --grep "@tag"
```

### Running Tests in UI Mode

To run tests with the Playwright UI:

```bash
pnpm exec playwright test --ui
```

## Debugging Tests

### Viewing Test Traces

When a test fails, Playwright generates a trace file that can be viewed to debug the issue:

```bash
pnpm exec playwright show-trace test-results/[trace-file].zip
```

### Adding Debug Logs

To add debug logs to your tests, use `console.log()` statements. These logs will appear in the test output.

Example:

```typescript
console.log('Starting test: My Test');
// Test code
console.log('Test completed');
```

### Slowing Down Tests

To slow down test execution for debugging:

```bash
pnpm exec playwright test --debug
```

## Adding New Tests

### Creating a New Test File

1. Create a new file in the appropriate directory (e.g., `tests/e2e/my-feature.test.ts`)
2. Import the necessary dependencies:

```typescript
import { test, expect } from '../fixtures';
import { ChatPage } from '../pages/chat';
```

3. Write your test:

```typescript
test.describe('My Feature', () => {
  let chatPage: ChatPage;

  test.beforeEach(async ({ page }) => {
    chatPage = new ChatPage(page);
  });

  /**
   * Test: Feature works correctly
   *
   * Purpose: Verify that the feature works as expected.
   *
   * Expected behavior:
   * - The feature should do X when Y happens
   */
  test('Feature works correctly', async () => {
    console.log('Starting test: Feature works correctly');

    // Test code

    console.log('Test completed');
  });
});
```

### Creating a New Page Object

If you need to interact with a new page or component, create a new page object in the `pages` directory:

```typescript
// tests/pages/my-page.ts
import { expect, type Page } from '@playwright/test';

export class MyPage {
  constructor(public readonly page: Page) {}

  // Add methods for interacting with the page
  async doSomething() {
    // Implementation
  }
}
```

## Best Practices

### Robust Test Design

1. **Add detailed documentation** for each test explaining its purpose and expected behavior
2. **Add detailed logs** at each step of the test for easier debugging
3. **Handle errors gracefully** to avoid silent failures
4. **Use a tolerant approach** that allows tests to pass even if responses aren't exactly as expected
5. **Make tests independent** of each other by generating unique IDs and handling the case where previous tests failed

### Handling Flaky Tests

1. **Add retries** for flaky operations
2. **Add timeouts** for long-running operations
3. **Skip unstable tests** with clear documentation explaining why
4. **Use conditional assertions** that adapt to different environments

## Test Environment

The test environment is configured in `.env.test`. This file contains environment variables specific to the test environment, such as database connection strings, API keys, and feature flags.

To set up the test environment:

1. Copy `.env.test` to `.env.test.local` if you need to override any values
2. Run `pnpm run test:setup` to initialize the test database with test data

## Mocks

The test suite uses mocks to simulate external services and dependencies. Mocks are defined in the `tests/mocks/` directory and can be configured via environment variables.

### Available Mocks

- **AI Provider**: Mocks the AI provider to return predefined responses based on the input prompt
- **Weather Tool**: Mocks the weather tool to return predefined weather data
- **Authentication**: Mocks the authentication service to simulate user sessions

### Configuring Mocks

Mocks can be configured via environment variables:

- `USE_MOCK_AI`: Set to `true` to use the mock AI provider (default: `true`)
- `USE_MOCK_WEATHER`: Set to `true` to use the mock weather tool (default: `true`)
- `USE_MOCK_AUTH`: Set to `true` to use the mock authentication service (default: `true`)
- `USE_MOCK_DATABASE`: Set to `true` to use the mock database (default: `true` in test environment)
- `MOCK_LATENCY`: Set the latency in milliseconds for mock responses (default: `500`)

## Disabled Tests

Some tests are disabled (skipped) because they are unstable in certain environments or depend on external services that may not be available. Here's a list of disabled tests and why they are disabled:

### Authentication Tests

Authentication tests are currently skipped because they are unstable in the CI environment. The authentication process may involve external services or complex UI interactions that are difficult to test reliably in a headless environment.

To reactivate these tests:
1. Set up a test authentication service
2. Configure the test environment with the appropriate credentials
3. Remove the `.skip` from the test descriptions

### Chat API Tests

Some chat API tests are disabled because they depend on the chat generation API, which may not be available in the test environment or may behave differently than expected. These tests are in `tests/routes/chat.test.ts`.

Currently disabled tests:
- `Ada can delete her own chat`: Depends on chat generation
- `Ada can resume chat generation`: Requires a stable chat generation API
- `Ada cannot resume chat generation that has ended`: Requires a stable chat generation API
- `Babbage cannot resume a private chat generation that belongs to Ada`: Requires a stable chat generation API
- `Babbage can resume a public chat generation that belongs to Ada`: Requires a stable chat generation API

To reactivate these tests:
1. Set up a mock chat generation API
2. Configure the test environment to use the mock API
3. Remove the `.skip` from the test descriptions

### Document Deletion

The document deletion test has an issue where deleted documents are still returned by the API. This suggests that the document deletion functionality isn't working as expected.

To fix this issue:
1. Investigate the document deletion API to ensure it's properly deleting documents
2. Check if there's a caching issue that's causing deleted documents to still appear
3. Update the test to account for any eventual consistency in the document deletion process
