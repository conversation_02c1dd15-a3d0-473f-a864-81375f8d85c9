import type { LanguageModelV1 } from 'ai';
import type { DestinationInfo, TripPlan, PointOfInterest } from '../types';
import { PreferenceAgent, type UserPreferences } from './preference-agent';
import {
  TripType,
  VisualTheme,
  type TripClassification,
} from './classifier-agent';
import {
  LocalExpertAgent,
  type LocalRecommendations,
} from './local-expert-agent';
import {
  ActivityPlannerAgent,
  type ActivityPlan,
} from './activity-planner-agent';
import {
  AccommodationDiningAgent,
  type AccommodationDiningRecommendations,
} from './accommodation-dining-agent';
import { LogisticsAgent, type PracticalInfo } from './logistics-agent';
import { EvaluatorAgent } from './evaluator-agent';
import { ClassifierAgent } from './classifier-agent';
import { ItineraryAgent } from './itinerary-agent';
import { PoiAgent } from './poi-agent';
import {
  VisualTemplateAgent,
  type VisualTemplate,
} from './visual-template-agent';
import {
  SpecializedActivityAgent,
  type SpecializedActivityData,
} from './specialized-activity-agent-new';
import { withTimeout, TIMEOUT_CONFIG } from '../timeout-config';
import { CurrencyAgent } from './currency-agent';

/**
 * Interface for the comprehensive trip plan
 */
export interface ComprehensiveTripPlan extends TripPlan {
  userPreferences: UserPreferences;
  localRecommendations: LocalRecommendations;
  activityPlan: ActivityPlan;
  accommodationDining: AccommodationDiningRecommendations;
  practicalInfo: PracticalInfo;
  evaluationScore: number;
  improvementNotes?: string[];
  tripClassification: TripClassification;
  visualTemplate: VisualTemplate;
  specializedActivityData?: SpecializedActivityData;
  detailedItinerary?: any; // Detailed itinerary with precise timings, local phrases, and budget
  currencyInfo?: {
    code: string;
    symbol: string;
    name: string;
    country: string;
  };
  detailedPOIs?: any; // Detailed POIs with precise coordinates from web search
  heroImage?: string;
}

/**
 * OrchestratorAgent is responsible for coordinating all the specialized agents
 * and creating a comprehensive trip plan.
 */
export class OrchestratorAgent {
  private preferenceAgent: PreferenceAgent;
  private localExpertAgent: LocalExpertAgent;
  private activityPlannerAgent: ActivityPlannerAgent;
  private accommodationDiningAgent: AccommodationDiningAgent;
  private logisticsAgent: LogisticsAgent;
  private evaluatorAgent: EvaluatorAgent;
  private classifierAgent: ClassifierAgent;
  private visualTemplateAgent: VisualTemplateAgent;
  private specializedActivityAgent: SpecializedActivityAgent;
  private itineraryAgent: ItineraryAgent;
  private poiAgent: PoiAgent;
  private currencyAgent: CurrencyAgent;

  constructor(model: LanguageModelV1) {
    this.preferenceAgent = new PreferenceAgent(model);
    this.localExpertAgent = new LocalExpertAgent(model);
    this.activityPlannerAgent = new ActivityPlannerAgent(model);
    this.accommodationDiningAgent = new AccommodationDiningAgent(model);
    this.logisticsAgent = new LogisticsAgent(model);
    this.evaluatorAgent = new EvaluatorAgent(model);
    this.classifierAgent = new ClassifierAgent(model);
    this.visualTemplateAgent = new VisualTemplateAgent();
    this.specializedActivityAgent = new SpecializedActivityAgent(model);
    this.itineraryAgent = new ItineraryAgent(model);
    this.poiAgent = new PoiAgent(model);
    this.currencyAgent = new CurrencyAgent(model);
  }

  /**
   * Orchestrate the trip planning process
   */
  async orchestrateTripPlanning(
    query: string,
    destinationInfo: DestinationInfo,
    sendProgressUpdate: (message: string, progress: number, data: any) => void,
    tripClassification?: TripClassification,
  ): Promise<ComprehensiveTripPlan> {
    try {
      console.log(
        'Orchestrating trip planning for:',
        destinationInfo.destination,
      );

      // 1. Classify the trip request if not already provided
      let finalTripClassification = tripClassification;
      if (!finalTripClassification) {
        sendProgressUpdate('Analyzing trip type and preferences...', 5, {});
        finalTripClassification =
          await this.classifierAgent.classifyTripRequest(query);
      }

      // Get currency information for the destination
      let currencyInfo: {
        code: string;
        symbol: string;
        name: string;
        country: string;
      } | null = null;
      try {
        sendProgressUpdate('Retrieving currency information...', 10, {});
        currencyInfo =
          await this.currencyAgent.getCurrencyForDestination(destinationInfo);
        console.log('✅ [Orchestrator] Retrieved currency info:', currencyInfo);
      } catch (error) {
        console.error('❌ [Orchestrator] Error getting currency info:', error);
        // Ne pas échouer tout le processus si la récupération de la devise échoue
        sendProgressUpdate(
          'Could not retrieve currency information. Continuing without it...',
          10,
          {},
        );
      }

      // 2. Extract user preferences
      sendProgressUpdate('Understanding your travel preferences...', 15, {
        destination: destinationInfo,
        tripType: finalTripClassification.primaryType,
      });

      const userPreferences =
        await this.preferenceAgent.extractPreferences(query);

      // 3. Generate visual template
      sendProgressUpdate('Designing your personalized travel guide...', 20, {
        destination: destinationInfo,
        tripType: finalTripClassification.primaryType,
        visualTheme: finalTripClassification.visualTheme,
      });

      const visualTemplate =
        await this.visualTemplateAgent.generateVisualTemplate(
          finalTripClassification,
          destinationInfo.destination,
        );

      // 4. Get specialized activity data if needed
      let specializedActivityData = undefined;

      if (finalTripClassification.primaryType !== 'general') {
        sendProgressUpdate(
          `Finding the best ${finalTripClassification.primaryType} options...`,
          25,
          {
            destination: destinationInfo,
            tripType: finalTripClassification.primaryType,
            activity: finalTripClassification.primaryActivity,
          },
        );

        specializedActivityData =
          await this.specializedActivityAgent.getSpecializedActivityData(
            destinationInfo,
            userPreferences,
            finalTripClassification,
          );
      }

      // 5. Get local recommendations
      sendProgressUpdate('Discovering local insights and hidden gems...', 35, {
        destination: destinationInfo,
        preferences: userPreferences,
      });

      const localRecommendations =
        await this.localExpertAgent.getLocalRecommendations(
          destinationInfo,
          userPreferences,
        );

      // 6. Create activity plan
      sendProgressUpdate('Creating your personalized activity plan...', 45, {
        destination: destinationInfo,
        preferences: userPreferences,
        tripType: finalTripClassification.primaryType,
      });

      const activityPlan = await this.activityPlannerAgent.createActivityPlan(
        destinationInfo,
        userPreferences,
        localRecommendations,
      );

      // 6.5. Generate detailed itinerary with precise timings
      sendProgressUpdate(
        'Creating detailed itinerary with precise timings...',
        50,
        {
          destination: destinationInfo,
          preferences: userPreferences,
          days: activityPlan.days,
        },
      );

      const detailedItinerary =
        await this.itineraryAgent.generateItinerary(destinationInfo);

      // 6.75. Generate detailed POIs with precise coordinates
      sendProgressUpdate(
        'Generating detailed points of interest with coordinates...',
        52,
        {
          destination: destinationInfo,
          days: activityPlan.days,
        },
      );

      const detailedPOIs =
        await this.poiAgent.generatePointsOfInterest(destinationInfo);

      // 7. Get accommodation and dining recommendations
      sendProgressUpdate('Finding perfect places to stay and eat...', 55, {
        destination: destinationInfo,
        preferences: userPreferences,
      });

      const accommodationDining =
        await this.accommodationDiningAgent.getRecommendations(
          destinationInfo,
          userPreferences,
        );

      // 8. Get practical information (with extended timeout for web search)
      sendProgressUpdate(
        'Gathering essential practical information from web...',
        65,
        {
          destination: destinationInfo,
          preferences: userPreferences,
        },
      );

      const practicalInfo = await withTimeout(
        this.logisticsAgent.getPracticalInfo(destinationInfo, userPreferences),
        TIMEOUT_CONFIG.OPERATIONS.LOGISTICS_AGENT, // Use configured timeout for comprehensive results
        'logistics agent practical information gathering',
      );

      // 9. Combine all information into a comprehensive trip plan
      sendProgressUpdate('Creating your comprehensive trip plan...', 75, {
        destination: destinationInfo,
        days: activityPlan.days,
        tripType: finalTripClassification.primaryType,
      });

      const draftTripPlan = this.combineTripPlan(
        destinationInfo,
        userPreferences,
        localRecommendations,
        activityPlan,
        accommodationDining,
        practicalInfo,
        finalTripClassification,
        visualTemplate,
        specializedActivityData,
        detailedItinerary,
        detailedPOIs,
      ) as ComprehensiveTripPlan;

      // Add currency info to the trip plan
      if (currencyInfo) {
        draftTripPlan.currencyInfo = currencyInfo;
      }

      // 10. Evaluate and improve the trip plan
      sendProgressUpdate('Evaluating and optimizing your trip plan...', 85, {
        destination: destinationInfo,
        days: activityPlan.days,
      });

      const evaluationResult =
        await this.evaluatorAgent.evaluateAndImprove(draftTripPlan);

      // 11. Create the final comprehensive trip plan
      sendProgressUpdate('Finalizing your perfect travel experience...', 95, {
        destination: destinationInfo,
        days: evaluationResult.improvedPlan.days,
        tripType: finalTripClassification.primaryType,
      });

      const finalTripPlan: ComprehensiveTripPlan = {
        ...evaluationResult.improvedPlan,
        userPreferences,
        localRecommendations,
        activityPlan,
        accommodationDining,
        practicalInfo,
        evaluationScore: this.calculateEvaluationScore(
          evaluationResult.evaluation,
        ),
        improvementNotes: evaluationResult.evaluation.suggestedImprovements,
        tripClassification: finalTripClassification,
        visualTemplate,
        specializedActivityData,
        detailedItinerary,
        detailedPOIs,
        // Ensure currencyInfo is included in the final trip plan
        currencyInfo: draftTripPlan.currencyInfo,
      };

      return finalTripPlan;
    } catch (error) {
      console.error('Error orchestrating trip planning:', error);

      // Return a basic trip plan if orchestration fails
      return this.createBasicTripPlan(destinationInfo);
    }
  }

  /**
   * Combine all the information into a comprehensive trip plan
   */
  private combineTripPlan(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
    localRecommendations: LocalRecommendations,
    activityPlan: ActivityPlan,
    accommodationDining: AccommodationDiningRecommendations,
    practicalInfo: PracticalInfo,
    _tripClassification?: TripClassification,
    _visualTemplate?: VisualTemplate,
    specializedActivityData?: SpecializedActivityData,
    detailedItinerary?: any,
    detailedPOIs?: any,
  ): TripPlan {
    // Create POIs from activities, accommodations, and dining options
    const pois = this.createPoisFromAllSources(
      activityPlan,
      accommodationDining,
      localRecommendations,
      specializedActivityData,
      detailedPOIs,
    );

    // Create travel tips from practical info, local recommendations, and detailed itinerary
    const travelTips = this.createTravelTips(
      practicalInfo,
      localRecommendations,
      specializedActivityData,
      detailedItinerary,
    );

    // Create budget information from practical info and detailed itinerary
    const budget = this.createBudgetInfo(
      destinationInfo,
      userPreferences,
      accommodationDining,
      practicalInfo,
      detailedItinerary,
    );

    // Create local phrases from destination info and detailed itinerary
    const localPhrases = this.createLocalPhrases(
      destinationInfo,
      detailedItinerary,
    );

    return {
      destination: destinationInfo,
      days: activityPlan.days,
      pois,
      localPhrases,
      travelTips,
      budget,
    };
  }

  /**
   * Create POIs from all sources
   */
  private createPoisFromAllSources(
    activityPlan: ActivityPlan,
    accommodationDining: AccommodationDiningRecommendations,
    localRecommendations: LocalRecommendations,
    specializedActivityData?: SpecializedActivityData,
    detailedPOIs?: any,
  ): PointOfInterest[] {
    // Define a custom POI type that extends the base PointOfInterest type
    type ExtendedPointOfInterest = {
      name: string;
      lat: string;
      lng: string;
      day: string;
      type: string;
      description: string;
    };

    const pois: ExtendedPointOfInterest[] = [];

    // Add detailed POIs from PoiAgent first (highest priority - precise coordinates)
    if (detailedPOIs?.pois) {
      for (const poi of detailedPOIs.pois) {
        pois.push({
          name: poi.name,
          lat: poi.lat,
          lng: poi.lng,
          day: poi.day,
          type: poi.type,
          description: poi.description,
        });
      }
    }

    // Add POIs from activity plan
    for (const day of activityPlan.days) {
      for (const activity of day.activities) {
        if (
          activity.location &&
          activity.location !== 'Various' &&
          activity.location !== 'Multiple locations'
        ) {
          pois.push({
            name: activity.activity,
            lat: '0', // Will be updated with actual coordinates
            lng: '0', // Will be updated with actual coordinates
            day: `Day ${day.day}`,
            type: 'attraction',
            description: activity.description,
          });
        }
      }
    }

    // Add POIs from accommodations
    for (const accommodation of accommodationDining.accommodations
      .recommended) {
      pois.push({
        name: accommodation.name,
        lat: accommodation.coordinates.lat,
        lng: accommodation.coordinates.lng,
        day: 'All days',
        type: 'hotel',
        description: accommodation.description,
      });
    }

    // Add POIs from dining options
    const allDiningOptions = [
      ...accommodationDining.dining.breakfast,
      ...accommodationDining.dining.lunch,
      ...accommodationDining.dining.dinner,
    ];

    for (const dining of allDiningOptions.slice(0, 10)) {
      // Limit to 10 dining options
      pois.push({
        name: dining.name,
        lat: dining.coordinates.lat,
        lng: dining.coordinates.lng,
        day: 'Various',
        type: 'restaurant',
        description: dining.description,
      });
    }

    // Add POIs from hidden gems
    for (const gem of localRecommendations.hiddenGems) {
      pois.push({
        name: gem.name,
        lat: gem.coordinates.lat,
        lng: gem.coordinates.lng,
        day: 'Flexible',
        type: 'attraction',
        description: gem.description,
      });
    }

    // Add POIs from specialized activity data
    if (specializedActivityData) {
      // Add cycling routes
      if (specializedActivityData.cyclingRoutes) {
        for (const route of specializedActivityData.cyclingRoutes) {
          // Add start point
          pois.push({
            name: `${route.name} - Start`,
            lat: route.startPoint.coordinates.lat,
            lng: route.startPoint.coordinates.lng,
            day: 'Flexible',
            type: 'cycling-start',
            description: `Starting point for ${route.name}: ${route.description}`,
          });

          // Add end point
          pois.push({
            name: `${route.name} - End`,
            lat: route.endPoint.coordinates.lat,
            lng: route.endPoint.coordinates.lng,
            day: 'Flexible',
            type: 'cycling-end',
            description: `End point for ${route.name}`,
          });

          // Add waypoints
          for (const waypoint of route.waypoints) {
            pois.push({
              name: waypoint.name,
              lat: waypoint.coordinates.lat,
              lng: waypoint.coordinates.lng,
              day: 'Flexible',
              type: `cycling-${waypoint.type}`,
              description: waypoint.description,
            });
          }

          // Add bike rental options
          if (route.bikeRentalOptions) {
            for (const rental of route.bikeRentalOptions) {
              pois.push({
                name: rental.name,
                lat: rental.coordinates.lat,
                lng: rental.coordinates.lng,
                day: 'Flexible',
                type: 'bike-rental',
                description: `Bike rental: ${rental.bikeTypes.join(', ')}. Price: ${rental.priceRange}`,
              });
            }
          }
        }
      }

      // Add hiking trails
      if (specializedActivityData.hikingTrails) {
        for (const trail of specializedActivityData.hikingTrails) {
          // Add start point
          pois.push({
            name: `${trail.name} - Start`,
            lat: trail.startPoint.coordinates.lat,
            lng: trail.startPoint.coordinates.lng,
            day: 'Flexible',
            type: 'hiking-start',
            description: `Starting point for ${trail.name}: ${trail.description}`,
          });

          // Add end point if different from start
          if (trail.endPoint) {
            pois.push({
              name: `${trail.name} - End`,
              lat: trail.endPoint.coordinates.lat,
              lng: trail.endPoint.coordinates.lng,
              day: 'Flexible',
              type: 'hiking-end',
              description: `End point for ${trail.name}`,
            });
          }

          // Add waypoints
          for (const waypoint of trail.waypoints) {
            pois.push({
              name: waypoint.name,
              lat: waypoint.coordinates.lat,
              lng: waypoint.coordinates.lng,
              day: 'Flexible',
              type: `hiking-${waypoint.type}`,
              description: waypoint.description,
            });
          }
        }
      }

      // Add beach activities
      if (specializedActivityData.beachActivities) {
        for (const beach of specializedActivityData.beachActivities.beaches) {
          pois.push({
            name: beach.name,
            lat: beach.coordinates.lat,
            lng: beach.coordinates.lng,
            day: 'Flexible',
            type: 'beach',
            description: `${beach.description}. Activities: ${beach.activities.join(', ')}`,
          });
        }

        // Add water activity providers
        for (const activity of specializedActivityData.beachActivities
          .waterActivities) {
          if (activity.providers) {
            for (const provider of activity.providers) {
              pois.push({
                name: `${provider.name} (${activity.name})`,
                lat: provider.coordinates.lat,
                lng: provider.coordinates.lng,
                day: 'Flexible',
                type: 'water-activity',
                description: `${activity.description}. Price: ${provider.priceRange}`,
              });
            }
          }
        }
      }
    }

    // Convert the extended POIs to the required PointOfInterest type
    return pois.map((poi) => {
      // Map each POI to ensure the type is one of the allowed values
      let poiType: 'attraction' | 'restaurant' | 'hotel' = 'attraction';

      if (poi.type === 'restaurant') {
        poiType = 'restaurant';
      } else if (poi.type === 'hotel') {
        poiType = 'hotel';
      }

      return {
        name: poi.name,
        lat: poi.lat,
        lng: poi.lng,
        day: poi.day,
        type: poiType,
        description: poi.description,
      };
    });
  }

  /**
   * Create travel tips from practical info, local recommendations, and detailed itinerary
   */
  private createTravelTips(
    practicalInfo: PracticalInfo,
    localRecommendations: LocalRecommendations,
    specializedActivityData?: SpecializedActivityData,
    detailedItinerary?: any,
  ): { category: string; tips: string[] }[] {
    // Helper function to process tips that might be in different formats
    const processTips = (tips: any): string[] => {
      if (!tips || tips.length === 0) return [];

      // If it's already an array of strings, return it
      if (typeof tips[0] === 'string') {
        return tips as string[];
      }

      // If it's an array of objects with a tips property that is an array of strings
      if (
        typeof tips[0] === 'object' &&
        tips[0].tips &&
        Array.isArray(tips[0].tips)
      ) {
        return (tips as any[]).flatMap((item) => item.tips);
      }

      // If it's an array of objects with a category and tips property
      if (typeof tips[0] === 'object' && tips[0].category && tips[0].tips) {
        return (tips as any[]).flatMap((item) => {
          if (Array.isArray(item.tips)) {
            return item.tips.map((tip: string) => `${item.category}: ${tip}`);
          } else {
            return [`${item.category}: ${item.tips}`];
          }
        });
      }

      // If we can't determine the format, convert objects to strings
      return (tips as any[]).map((item) => {
        if (typeof item === 'string') {
          return item;
        } else if (typeof item === 'object') {
          return JSON.stringify(item);
        } else {
          return String(item);
        }
      });
    };
    const travelTips = [];

    // Add ultra-detailed transportation tips
    const transportationTips = [];
    if (practicalInfo.transportation?.localTransportation) {
      for (const transport of practicalInfo.transportation
        .localTransportation) {
        // Basic info
        transportationTips.push(
          `${transport.type}: ${transport.description} - ${transport.pros?.[0] || 'Convenient'}`,
        );

        // Detailed ticket information
        if (transport.ticketTypes && Array.isArray(transport.ticketTypes)) {
          for (const ticket of transport.ticketTypes) {
            transportationTips.push(
              `💳 ${ticket.name}: ${ticket.price} (${ticket.validity}) - Available at: ${ticket.whereToBuy?.join(', ') || 'Various locations'}`,
            );
          }
        }

        // Mobile app recommendations
        if (transport.mobileApps && Array.isArray(transport.mobileApps)) {
          for (const app of transport.mobileApps) {
            transportationTips.push(
              `📱 ${app.name}: ${app.features?.join(', ') || 'Transportation app'} - Download: ${app.downloadLink || 'App store'} (${app.cost || 'Free'})`,
            );
          }
        }

        // Insider tips
        if (transport.insiderTips && Array.isArray(transport.insiderTips)) {
          transport.insiderTips.forEach((tip: string) =>
            transportationTips.push(`💡 Insider tip: ${tip}`),
          );
        }

        // Rush hour advice
        if (
          transport.rushHourAdvice &&
          Array.isArray(transport.rushHourAdvice)
        ) {
          transport.rushHourAdvice.forEach((advice: string) =>
            transportationTips.push(`⏰ Rush hour: ${advice}`),
          );
        }
      }
    }

    // Fallback if no detailed data
    if (transportationTips.length === 0) {
      transportationTips.push(
        'Public Transport: Buses, metro, and local trains - Cheap',
        'Taxi/Rideshare: Taxis and ride-sharing services - Convenient',
        'Purchase a local transport card for easy travel on trains and buses.',
        'Public transportation is generally efficient, but can be crowded during peak hours.',
      );
    }

    travelTips.push({
      category: 'Transportation',
      tips: transportationTips,
    });

    // Add ultra-detailed safety tips from LocalExpertAgent
    const safetyTips = [];
    if (
      localRecommendations.safetyTips &&
      Array.isArray(localRecommendations.safetyTips)
    ) {
      for (const safety of localRecommendations.safetyTips) {
        // Check if it's the new detailed format (object) or old format (string)
        if (typeof safety === 'object' && safety !== null && 'tip' in safety) {
          const safetyObj = safety as any;
          const severityIcon =
            safetyObj.severity === 'Critical'
              ? '🚨'
              : safetyObj.severity === 'Important'
                ? '⚠️'
                : 'ℹ️';
          safetyTips.push(
            `${severityIcon} ${safetyObj.tip} (${safetyObj.timeOfDay || 'Always'} - ${safetyObj.location || 'Everywhere'})`,
          );
          if (safetyObj.whatToAvoid) {
            safetyTips.push(`❌ Avoid: ${safetyObj.whatToAvoid}`);
          }
          if (
            safetyObj.emergencyContacts &&
            Array.isArray(safetyObj.emergencyContacts) &&
            safetyObj.emergencyContacts.length > 0
          ) {
            safetyTips.push(
              `📞 Emergency: ${safetyObj.emergencyContacts.join(', ')}`,
            );
          }
        } else if (typeof safety === 'string') {
          // Old format - just add the string
          safetyTips.push(safety);
        }
      }
    }

    // Fallback to basic safety tips if no detailed data
    if (safetyTips.length === 0) {
      // Try to get from practicalInfo as fallback
      const basicSafetyTips = practicalInfo.practicalTips?.find(
        (t: any) => t.category === 'Safety',
      )?.tips;

      const processedBasicSafetyTips = basicSafetyTips
        ? processTips(basicSafetyTips)
        : [];

      if (processedBasicSafetyTips.length > 0) {
        safetyTips.push(...processedBasicSafetyTips);
      } else {
        safetyTips.push(
          'Keep copies of important documents',
          'Be aware of your surroundings',
          'Use hotel safes for valuables',
        );
      }
    }

    travelTips.push({
      category: 'Safety',
      tips: safetyTips,
    });

    // Add ultra-detailed cultural etiquette tips from LocalExpertAgent
    const culturalTips = [];
    if (
      localRecommendations.culturalEtiquette &&
      Array.isArray(localRecommendations.culturalEtiquette)
    ) {
      for (const etiquette of localRecommendations.culturalEtiquette) {
        // Check if it's the new detailed format (object with situation) or old format
        if (
          typeof etiquette === 'object' &&
          etiquette !== null &&
          'situation' in etiquette
        ) {
          const etiquetteObj = etiquette as any;
          culturalTips.push(`🎭 ${etiquetteObj.situation}:`);

          // Do's
          if (
            etiquetteObj.doList &&
            Array.isArray(etiquetteObj.doList) &&
            etiquetteObj.doList.length > 0
          ) {
            etiquetteObj.doList.forEach((item: string) =>
              culturalTips.push(`✅ ${item}`),
            );
          }

          // Don'ts
          if (
            etiquetteObj.dontList &&
            Array.isArray(etiquetteObj.dontList) &&
            etiquetteObj.dontList.length > 0
          ) {
            etiquetteObj.dontList.forEach((item: string) =>
              culturalTips.push(`❌ ${item}`),
            );
          }

          // Tipping
          if (etiquetteObj.tippingGuideline?.expected) {
            culturalTips.push(
              `💰 Tipping: ${etiquetteObj.tippingGuideline.amount} (${etiquetteObj.tippingGuideline.method}) - ${etiquetteObj.tippingGuideline.timing}`,
            );
          }

          // Dress code
          if (etiquetteObj.dressCode) {
            culturalTips.push(
              `👔 Dress code - Men: ${etiquetteObj.dressCode.men}, Women: ${etiquetteObj.dressCode.women}`,
            );
          }

          // Insider tips
          if (
            etiquetteObj.insiderTips &&
            Array.isArray(etiquetteObj.insiderTips) &&
            etiquetteObj.insiderTips.length > 0
          ) {
            etiquetteObj.insiderTips.forEach((tip: string) =>
              culturalTips.push(`💡 ${tip}`),
            );
          }
        } else {
          // Old format - process using helper function or add as string
          if (typeof etiquette === 'string') {
            culturalTips.push(etiquette);
          } else if (typeof etiquette === 'object' && etiquette !== null) {
            // Try to extract useful information from old format
            const processed = processTips([etiquette]);
            culturalTips.push(...processed);
          }
        }
      }
    }

    // Fallback if no detailed data
    if (culturalTips.length === 0) {
      // Try to process using helper function as fallback
      const processedCulturalTips =
        Array.isArray(localRecommendations.culturalEtiquette) &&
        localRecommendations.culturalEtiquette.length > 0
          ? processTips(localRecommendations.culturalEtiquette)
          : [];

      if (processedCulturalTips.length > 0) {
        culturalTips.push(...processedCulturalTips);
      } else {
        culturalTips.push(
          'Greet locals with a smile and basic courtesy',
          'A handshake is the standard greeting for first meetings',
          'Learn a few basic phrases in the local language',
          "Locals appreciate the effort even if your pronunciation isn't perfect",
          'Wait to be seated at restaurants',
          'Tipping practices vary - generally 10% is appropriate',
        );
      }
    }

    travelTips.push({
      category: 'Cultural Etiquette',
      tips: culturalTips,
    });

    // Add ultra-detailed weather tips from LogisticsAgent
    const weatherTips = [];
    if (practicalInfo.weather) {
      weatherTips.push(`🌤️ Season: ${practicalInfo.weather.season}`);
      weatherTips.push(
        `🌡️ Temperature: ${practicalInfo.weather.averageTemperature}`,
      );
      weatherTips.push(
        `🌧️ Precipitation: ${practicalInfo.weather.precipitation}`,
      );

      // Detailed packing advice
      if (
        practicalInfo.weather.whatToPack &&
        Array.isArray(practicalInfo.weather.whatToPack)
      ) {
        weatherTips.push('🧳 Packing essentials:');
        practicalInfo.weather.whatToPack.forEach((item: string) =>
          weatherTips.push(`  • ${item}`),
        );
      }

      // Monthly weather details
      if (
        practicalInfo.weather.monthlyDetails &&
        Array.isArray(practicalInfo.weather.monthlyDetails)
      ) {
        weatherTips.push('📅 Monthly weather guide:');
        practicalInfo.weather.monthlyDetails.forEach((month: any) => {
          weatherTips.push(
            `  • ${month.month}: ${month.temperature} - ${month.description}`,
          );
        });
      }

      // Weather apps
      if (
        practicalInfo.weather.recommendedApps &&
        Array.isArray(practicalInfo.weather.recommendedApps)
      ) {
        practicalInfo.weather.recommendedApps.forEach((app: any) => {
          weatherTips.push(
            `📱 Weather app: ${app.name} - ${app.features?.join(', ') || 'Weather forecasts'}`,
          );
        });
      }
    }

    if (weatherTips.length === 0) {
      weatherTips.push(
        'Check weather forecasts before your trip',
        'Pack layers for temperature changes',
        'Bring rain gear during wet seasons',
      );
    }

    travelTips.push({
      category: 'Weather',
      tips: weatherTips,
    });

    // Add ultra-detailed money tips from LogisticsAgent
    const moneyTips = [];
    if (practicalInfo.currencyInfo) {
      // Currency information
      moneyTips.push(
        `💱 Currency: ${practicalInfo.currencyInfo.currency?.name || 'Local currency'} (${practicalInfo.currencyInfo.currency?.code || 'N/A'}) - Symbol: ${practicalInfo.currencyInfo.currency?.symbol || 'N/A'}`,
      );

      // Current exchange rates with trends
      if (practicalInfo.currencyInfo.exchangeRates) {
        moneyTips.push(
          `📈 Current Rates: 1 USD = ${practicalInfo.currencyInfo.exchangeRates.usd || 'N/A'} ${practicalInfo.currencyInfo.currency?.code || ''}, 1 EUR = ${practicalInfo.currencyInfo.exchangeRates.eur || 'N/A'} ${practicalInfo.currencyInfo.currency?.code || ''}`,
        );
        moneyTips.push(
          `📊 Trend: ${practicalInfo.currencyInfo.exchangeRates.trend || 'Stable'} (${practicalInfo.currencyInfo.exchangeRates.monthlyTrend || `Updated: ${practicalInfo.currencyInfo.exchangeRates.lastUpdated || 'Recently'}`})`,
        );

        // Detailed exchange locations
        if (
          practicalInfo.currencyInfo.exchangeRates.bestExchangeLocations &&
          Array.isArray(
            practicalInfo.currencyInfo.exchangeRates.bestExchangeLocations,
          )
        ) {
          moneyTips.push('🏦 Best Exchange Locations:');
          practicalInfo.currencyInfo.exchangeRates.bestExchangeLocations.forEach(
            (location: any) => {
              if (typeof location === 'object') {
                moneyTips.push(`  📍 ${location.name} (${location.type})`);
                moneyTips.push(`     📍 Address: ${location.address}`);
                if (location.phone)
                  moneyTips.push(`     📞 Phone: ${location.phone}`);
                if (location.website)
                  moneyTips.push(`     🌐 Website: ${location.website}`);
                moneyTips.push(`     🕒 Hours: ${location.hours}`);
                moneyTips.push(
                  `     💰 Rate: ${location.exchangeRate}, Fees: ${location.fees}`,
                );
                if (location.notes) moneyTips.push(`     ℹ️ ${location.notes}`);
              } else {
                moneyTips.push(`  • ${location}`);
              }
            },
          );
        }
      }

      // Banking information
      if (practicalInfo.currencyInfo.banking) {
        // ATM information
        if (practicalInfo.currencyInfo.banking.atmInfo) {
          moneyTips.push('🏧 ATM Information:');
          moneyTips.push(
            `  • Networks: ${practicalInfo.currencyInfo.banking.atmInfo.networks?.join(', ') || 'Major networks'}`,
          );
          moneyTips.push(
            `  • Fees: ${practicalInfo.currencyInfo.banking.atmInfo.fees || 'Varies'}`,
          );
          moneyTips.push(
            `  • Daily Limits: ${practicalInfo.currencyInfo.banking.atmInfo.dailyLimits || 'Check with bank'}`,
          );

          // Specific ATM locations
          if (
            practicalInfo.currencyInfo.banking.atmInfo.locations &&
            Array.isArray(practicalInfo.currencyInfo.banking.atmInfo.locations)
          ) {
            practicalInfo.currencyInfo.banking.atmInfo.locations.forEach(
              (atm: any) => {
                if (typeof atm === 'object') {
                  moneyTips.push(`  📍 ${atm.name} - ${atm.address}`);
                  moneyTips.push(
                    `     Network: ${atm.network}, Fees: ${atm.fees}, Hours: ${atm.operatingHours}`,
                  );
                  if (atm.languages?.length > 0)
                    moneyTips.push(
                      `     Languages: ${atm.languages.join(', ')}`,
                    );
                }
              },
            );
          }
        }

        // Bank branches
        if (
          practicalInfo.currencyInfo.banking.bankBranches &&
          Array.isArray(practicalInfo.currencyInfo.banking.bankBranches)
        ) {
          moneyTips.push('🏛️ Bank Branches:');
          practicalInfo.currencyInfo.banking.bankBranches.forEach(
            (bank: any) => {
              moneyTips.push(`  🏦 ${bank.name}`);
              moneyTips.push(`     📍 Address: ${bank.address}`);
              moneyTips.push(`     📞 Phone: ${bank.phone}`);
              if (bank.website)
                moneyTips.push(`     🌐 Website: ${bank.website}`);
              moneyTips.push(`     🕒 Hours: ${bank.hours}`);
              moneyTips.push(
                `     💼 Services: ${bank.services?.join(', ') || 'Banking services'}`,
              );
              if (bank.exchangeServices)
                moneyTips.push(`     💱 Currency exchange available`);
              if (bank.englishSupport)
                moneyTips.push(`     🗣️ English support available`);
              if (bank.fees) {
                moneyTips.push(
                  `     💰 Fees: Exchange ${bank.fees.currencyExchange}, Wire transfer ${bank.fees.wireTransfer}`,
                );
              }
            },
          );
        }
      }

      // Detailed tipping guide
      if (practicalInfo.currencyInfo.tipping) {
        moneyTips.push('💰 Comprehensive Tipping Guide:');
        moneyTips.push(
          `  📖 Culture: ${practicalInfo.currencyInfo.tipping.culture || 'Varies'}`,
        );
        moneyTips.push(
          `  📋 Guidelines: ${practicalInfo.currencyInfo.tipping.generalGuidelines || 'Follow local customs'}`,
        );

        if (practicalInfo.currencyInfo.tipping.byService) {
          const services = practicalInfo.currencyInfo.tipping.byService;

          if (services.restaurants) {
            moneyTips.push(
              `  🍽️ Restaurants: ${services.restaurants.amount} (${services.restaurants.method}) - ${services.restaurants.timing}`,
            );
            if (services.restaurants.notes)
              moneyTips.push(`     ℹ️ ${services.restaurants.notes}`);
          }

          if (services.taxis) {
            moneyTips.push(
              `  🚕 Taxis: ${services.taxis.amount} (${services.taxis.method}) - ${services.taxis.timing}`,
            );
            if (services.taxis.notes)
              moneyTips.push(`     ℹ️ ${services.taxis.notes}`);
          }

          if (services.hotels) {
            moneyTips.push(`  🏨 Hotels:`);
            moneyTips.push(`     🛎️ Bellhop: ${services.hotels.bellhop}`);
            moneyTips.push(
              `     🧹 Housekeeping: ${services.hotels.housekeeping}`,
            );
            moneyTips.push(`     🎩 Concierge: ${services.hotels.concierge}`);
            moneyTips.push(
              `     🍽️ Room Service: ${services.hotels.roomService}`,
            );
            moneyTips.push(`     💳 Method: ${services.hotels.method}`);
            if (services.hotels.notes)
              moneyTips.push(`     ℹ️ ${services.hotels.notes}`);
          }

          if (services.tours) {
            moneyTips.push(
              `  🗺️ Tours: ${services.tours.amount} (${services.tours.method}) - ${services.tours.timing}`,
            );
            if (services.tours.notes)
              moneyTips.push(`     ℹ️ ${services.tours.notes}`);
          }

          if (services.other && Array.isArray(services.other)) {
            services.other.forEach((service: any) => {
              moneyTips.push(
                `  🔧 ${service.service}: ${service.amount} (${service.method}) - ${service.timing}`,
              );
              if (service.notes) moneyTips.push(`     ℹ️ ${service.notes}`);
            });
          }
        }

        if (
          practicalInfo.currencyInfo.tipping.commonMistakes &&
          Array.isArray(practicalInfo.currencyInfo.tipping.commonMistakes)
        ) {
          moneyTips.push(
            `  ⚠️ Common Mistakes: ${practicalInfo.currencyInfo.tipping.commonMistakes.join(', ')}`,
          );
        }
      }

      // Payment methods
      if (practicalInfo.currencyInfo.paymentMethods) {
        // Credit card tips
        if (practicalInfo.currencyInfo.paymentMethods.creditCards) {
          moneyTips.push('💳 Credit Cards:');
          moneyTips.push(
            `  • Acceptance: ${practicalInfo.currencyInfo.paymentMethods.creditCards.acceptance}`,
          );
          moneyTips.push(
            `  • Preferred: ${practicalInfo.currencyInfo.paymentMethods.creditCards.preferredCards?.join(', ') || 'Major cards'}`,
          );
          moneyTips.push(
            `  • Fees: ${practicalInfo.currencyInfo.paymentMethods.creditCards.fees}`,
          );
          if (practicalInfo.currencyInfo.paymentMethods.creditCards.tips) {
            practicalInfo.currencyInfo.paymentMethods.creditCards.tips.forEach(
              (tip: string) => moneyTips.push(`  💡 ${tip}`),
            );
          }
        }

        // Cash information
        if (practicalInfo.currencyInfo.paymentMethods.cash) {
          moneyTips.push('💵 Cash:');
          moneyTips.push(
            `  • Importance: ${practicalInfo.currencyInfo.paymentMethods.cash.importance}`,
          );
          if (practicalInfo.currencyInfo.paymentMethods.cash.denominations) {
            moneyTips.push(
              `  • Denominations: ${practicalInfo.currencyInfo.paymentMethods.cash.denominations.join(', ')}`,
            );
          }
          if (practicalInfo.currencyInfo.paymentMethods.cash.tips) {
            practicalInfo.currencyInfo.paymentMethods.cash.tips.forEach(
              (tip: string) => moneyTips.push(`  💡 ${tip}`),
            );
          }
        }
      }

      // Mobile payment apps
      if (
        practicalInfo.currencyInfo.moneyApps &&
        Array.isArray(practicalInfo.currencyInfo.moneyApps)
      ) {
        moneyTips.push('📱 Mobile Payment Apps:');
        practicalInfo.currencyInfo.moneyApps.forEach((app: any) => {
          moneyTips.push(`  📲 ${app.name}: ${app.description}`);
          if (app.downloadLink)
            moneyTips.push(`     📥 Download: ${app.downloadLink}`);
          if (app.setupInstructions && Array.isArray(app.setupInstructions)) {
            moneyTips.push(
              `     🔧 Setup: ${app.setupInstructions.join(' → ')}`,
            );
          }
          if (app.acceptedAt && Array.isArray(app.acceptedAt)) {
            moneyTips.push(`     🏪 Accepted at: ${app.acceptedAt.join(', ')}`);
          }
          moneyTips.push(`     💰 Fees: ${app.fees || 'Check app'}`);
          if (app.requirements && Array.isArray(app.requirements)) {
            moneyTips.push(
              `     📋 Requirements: ${app.requirements.join(', ')}`,
            );
          }
        });
      }

      // Typical costs
      if (
        practicalInfo.currencyInfo.costs?.specificCosts &&
        Array.isArray(practicalInfo.currencyInfo.costs.specificCosts)
      ) {
        moneyTips.push('💰 Typical Costs:');
        practicalInfo.currencyInfo.costs.specificCosts.forEach((cost: any) => {
          moneyTips.push(
            `  • ${cost.item}: ${cost.cost} ${cost.notes ? `(${cost.notes})` : ''}`,
          );
        });
      }
    }

    if (moneyTips.length === 0) {
      moneyTips.push(
        'Check current exchange rates before traveling',
        'Notify your bank of travel plans',
        'Carry some local cash for small purchases',
        'Keep receipts for currency exchange',
      );
    }

    travelTips.push({
      category: 'Money',
      tips: moneyTips,
    });

    // Add ultra-detailed connectivity tips from LogisticsAgent
    const connectivityTips = [];
    if ((practicalInfo as any).connectivity) {
      const connectivity = (practicalInfo as any).connectivity;

      // Mobile operators
      if (
        connectivity.mobileOperators &&
        Array.isArray(connectivity.mobileOperators)
      ) {
        connectivityTips.push('📱 Mobile operators:');
        connectivity.mobileOperators.forEach((operator: any) => {
          connectivityTips.push(
            `  • ${operator.name}: ${operator.plans?.join(', ') || 'Various plans available'} - Coverage: ${operator.coverage || 'Good'}`,
          );
          if (operator.whereToBuy) {
            connectivityTips.push(
              `    Purchase at: ${operator.whereToBuy.join(', ')}`,
            );
          }
        });
      }

      // WiFi information
      if (connectivity.wifi) {
        if (
          connectivity.wifi.freeLocations &&
          Array.isArray(connectivity.wifi.freeLocations)
        ) {
          connectivityTips.push('📶 Free WiFi locations:');
          connectivity.wifi.freeLocations.forEach((location: any) => {
            connectivityTips.push(
              `  • ${location.name}: ${location.description || 'Free WiFi available'} - Speed: ${location.speed || 'Varies'}`,
            );
          });
        }

        if (connectivity.wifi.tips && Array.isArray(connectivity.wifi.tips)) {
          connectivity.wifi.tips.forEach((tip: string) =>
            connectivityTips.push(`💡 WiFi tip: ${tip}`),
          );
        }
      }

      // Internet cafes
      if (
        connectivity.internetCafes &&
        Array.isArray(connectivity.internetCafes)
      ) {
        connectivityTips.push('☕ Internet cafes:');
        connectivity.internetCafes.forEach((cafe: any) => {
          connectivityTips.push(
            `  • ${cafe.name}: ${cafe.location} - ${cafe.priceRange || 'Affordable'}/hour`,
          );
        });
      }

      // Connectivity apps
      if (
        connectivity.recommendedApps &&
        Array.isArray(connectivity.recommendedApps)
      ) {
        connectivity.recommendedApps.forEach((app: any) => {
          connectivityTips.push(
            `📱 Connectivity app: ${app.name} - ${app.description || 'Helps with connectivity'}`,
          );
        });
      }
    }

    if (connectivityTips.length === 0) {
      connectivityTips.push(
        'Check roaming charges with your mobile provider',
        'Consider purchasing a local SIM card for longer stays',
        'Many hotels and cafes offer free WiFi',
        'Download offline maps before traveling',
      );
    }

    travelTips.push({
      category: 'Connectivity',
      tips: connectivityTips,
    });

    // Add specialized activity tips
    if (specializedActivityData) {
      // Add cycling tips
      if (
        specializedActivityData.cyclingRoutes &&
        specializedActivityData.cyclingRoutes.length > 0
      ) {
        const cyclingTips = new Set<string>();

        // Collect all cycling tips
        for (const route of specializedActivityData.cyclingRoutes) {
          for (const tip of route.tips) {
            cyclingTips.add(tip);
          }
        }

        travelTips.push({
          category: 'Cycling Tips',
          tips: Array.from(cyclingTips),
        });
      }

      // Add hiking tips
      if (
        specializedActivityData.hikingTrails &&
        specializedActivityData.hikingTrails.length > 0
      ) {
        const hikingTips = new Set<string>();

        // Collect all hiking tips
        for (const trail of specializedActivityData.hikingTrails) {
          for (const tip of trail.tips) {
            hikingTips.add(tip);
          }
        }

        travelTips.push({
          category: 'Hiking Tips',
          tips: Array.from(hikingTips),
        });
      }

      // Add beach tips
      if (specializedActivityData.beachActivities) {
        travelTips.push({
          category: 'Beach Tips',
          tips: specializedActivityData.beachActivities.tips,
        });
      }

      // Add custom section tips
      for (const section of specializedActivityData.customSections) {
        if (section.type === 'list' && section.title.includes('Tips')) {
          travelTips.push({
            category: section.title,
            tips: section.content
              .split('\n')
              .map((line) => line.trim())
              .filter((line) => line),
          });
        }
      }
    }

    // Add tips from detailed itinerary
    if (detailedItinerary?.travelTips) {
      for (const tipCategory of detailedItinerary.travelTips) {
        // Check if this category already exists
        const existingCategory = travelTips.find(
          (t) => t.category === tipCategory.category,
        );

        if (existingCategory) {
          // Merge tips, avoiding duplicates
          const newTips = tipCategory.tips.filter(
            (tip: string) => !existingCategory.tips.includes(tip),
          );
          existingCategory.tips.push(...newTips);
        } else {
          // Add new category
          travelTips.push({
            category: tipCategory.category,
            tips: tipCategory.tips,
          });
        }
      }
    }

    return travelTips;
  }

  /**
   * Create budget information
   */
  private createBudgetInfo(
    _destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
    accommodationDining: AccommodationDiningRecommendations,
    practicalInfo: PracticalInfo,
    detailedItinerary?: any,
  ): { category: string; estimatedCost: string; notes: string }[] {
    const budget = [];

    // Accommodation budget
    const accommodationCost =
      accommodationDining.accommodations.recommended[0]?.priceRange || 'Varies';
    budget.push({
      category: 'Accommodation',
      estimatedCost: accommodationCost,
      notes: `Based on ${userPreferences.budget.level} level preferences`,
    });

    // Food budget
    budget.push({
      category: 'Food',
      estimatedCost: 'Varies by restaurant choice',
      notes: 'Budget for 3 meals per day plus snacks',
    });

    // Transportation budget
    const transportationOptions =
      practicalInfo.transportation.localTransportation;
    budget.push({
      category: 'Local Transportation',
      estimatedCost: transportationOptions[0]?.cost || 'Varies',
      notes: `Options include: ${transportationOptions.map((t: any) => t.type).join(', ')}`,
    });

    // Activities budget
    budget.push({
      category: 'Activities & Attractions',
      estimatedCost: 'Varies by activity',
      notes: 'Many attractions have entrance fees',
    });

    // Miscellaneous budget
    budget.push({
      category: 'Miscellaneous',
      estimatedCost: '~10-15% of total budget',
      notes: 'Souvenirs, unexpected expenses, tips, etc.',
    });

    // Add budget information from detailed itinerary
    if (detailedItinerary?.budget) {
      for (const budgetItem of detailedItinerary.budget) {
        // Check if this category already exists
        const existingCategory = budget.find(
          (b) => b.category === budgetItem.category,
        );

        if (existingCategory) {
          // Update with more detailed information from itinerary
          existingCategory.estimatedCost = budgetItem.estimatedCost;
          existingCategory.notes = budgetItem.notes;
        } else {
          // Add new budget category
          budget.push({
            category: budgetItem.category,
            estimatedCost: budgetItem.estimatedCost,
            notes: budgetItem.notes,
          });
        }
      }
    }

    return budget;
  }

  /**
   * Create local phrases
   */
  private createLocalPhrases(
    _destinationInfo: DestinationInfo,
    detailedItinerary?: any,
  ): { phrase: string; translation: string; pronunciation: string }[] {
    // Start with basic phrases
    const localPhrases = [
      {
        phrase: 'Hello',
        translation: 'Hello',
        pronunciation: 'Hello',
      },
      {
        phrase: 'Thank you',
        translation: 'Thank you',
        pronunciation: 'Thank you',
      },
      {
        phrase: 'Yes',
        translation: 'Yes',
        pronunciation: 'Yes',
      },
      {
        phrase: 'No',
        translation: 'No',
        pronunciation: 'No',
      },
      {
        phrase: 'Excuse me',
        translation: 'Excuse me',
        pronunciation: 'Excuse me',
      },
    ];

    // Add phrases from detailed itinerary if available
    if (detailedItinerary?.localPhrases) {
      for (const phrase of detailedItinerary.localPhrases) {
        // Check if this phrase already exists
        const existingPhrase = localPhrases.find(
          (p) => p.phrase === phrase.phrase,
        );

        if (existingPhrase) {
          // Update with more detailed information from itinerary
          existingPhrase.translation = phrase.translation;
          existingPhrase.pronunciation = phrase.pronunciation;
        } else {
          // Add new phrase
          localPhrases.push({
            phrase: phrase.phrase,
            translation: phrase.translation,
            pronunciation: phrase.pronunciation,
          });
        }
      }
    }

    return localPhrases;
  }

  /**
   * Calculate evaluation score
   */
  private calculateEvaluationScore(evaluation: any): number {
    if (!evaluation) return 7.5;

    const scores = [
      evaluation.logicalFlow || 7,
      evaluation.variety || 7,
      evaluation.travelTimes || 7,
      evaluation.completeness || 7,
      evaluation.coordinateAccuracy || 7,
      evaluation.overallQuality || 7,
    ];

    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }

  /**
   * Create a basic trip plan if orchestration fails
   */
  private createBasicTripPlan(
    destinationInfo: DestinationInfo,
  ): ComprehensiveTripPlan {
    // Create a basic trip plan with minimal information
    const days = [];

    // Use a default duration of 3 days if duration is null
    const actualDuration =
      destinationInfo.duration !== null ? destinationInfo.duration : 3;
    console.log(
      `Using fallback duration of ${actualDuration} days for comprehensive trip plan generation`,
    );

    // Create a basic activity plan for each day
    for (let i = 1; i <= actualDuration; i++) {
      days.push({
        day: i,
        activities: [
          {
            time: '09:00',
            activity: 'Breakfast',
            location: 'Hotel',
            description: 'Start the day with breakfast at your hotel.',
          },
          {
            time: '10:00',
            activity: 'Morning Activity',
            location: 'City Center',
            description: 'Explore the main attractions in the city center.',
          },
          {
            time: '13:00',
            activity: 'Lunch',
            location: 'Local Restaurant',
            description: 'Enjoy lunch at a local restaurant.',
          },
          {
            time: '15:00',
            activity: 'Afternoon Activity',
            location: 'Cultural Site',
            description: 'Visit a museum or cultural site.',
          },
          {
            time: '19:00',
            activity: 'Dinner',
            location: 'Restaurant',
            description: 'Have dinner at a recommended restaurant.',
          },
        ],
      });
    }

    // Create basic trip classification
    const tripClassification: TripClassification = {
      primaryType: TripType.GENERAL,
      secondaryTypes: [TripType.CULTURAL, TripType.CITY],
      visualTheme: VisualTheme.CLASSIC,
      primaryActivity: 'sightseeing',
      specialRequirements: [],
      keywords: ['travel', 'vacation', 'trip'],
      customizations: {
        mapFocus: true,
        foodFocus: true,
        accommodationFocus: true,
        budgetFocus: false,
        accessibilityFocus: false,
        familyFocus: false,
        sustainabilityFocus: false,
      },
    };

    // Create basic visual template
    const visualTemplate: VisualTemplate = {
      colors: {
        primary: '#3f51b5',
        secondary: '#f44336',
        accent: '#4caf50',
        background: '#ffffff',
        text: '#333333',
        headings: '#1a1a1a',
      },
      fonts: {
        heading: "'Roboto', sans-serif",
        body: "'Open Sans', sans-serif",
      },
      layout: {
        heroStyle: 'large' as const,
        mapSize: 'medium' as const,
        sectionOrder: [
          'itinerary',
          'map',
          'accommodations',
          'dining',
          'activities',
          'tips',
        ],
        highlightedSections: ['itinerary'],
      },
      icons: {
        type: 'solid',
        set: 'default',
      },
      images: {
        style: 'photography',
        filter: 'none',
        borderRadius: '8px',
      },
      animations: {
        level: 'subtle',
        transitions: 'fade-in',
      },
      specialElements: {
        callouts: true,
        timelines: false,
        cards: true,
        tabs: true,
        accordions: true,
        galleries: true,
        charts: false,
        customElements: [],
      },
      css: {
        headerCSS: `
          header {
            padding: 2rem 0;
            text-align: center;
          }
          h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
          }
        `,
        bodyCSS: `
          body {
            font-family: 'Open Sans', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
          }
        `,
        sectionCSS: `
          section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          h2 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: #1a1a1a;
          }
        `,
        mapCSS: `
          .map-container {
            height: 400px;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 1.5rem;
          }
        `,
        imageCSS: `
          img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            display: block;
          }
        `,
        buttonCSS: `
          button {
            background-color: #3f51b5;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
          }
          button:hover {
            background-color: #303f9f;
          }
        `,
        cardCSS: `
          .card {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
          }
          .card-header {
            padding: 1rem;
            background-color: #f5f5f5;
            font-weight: bold;
          }
          .card-body {
            padding: 1rem;
          }
        `,
        customCSS: '',
      },
      headerImageKeywords: [
        destinationInfo.destination,
        destinationInfo.country,
        'travel',
      ],
    };

    return {
      destination: destinationInfo,
      days,
      pois: [
        {
          name: 'City Center',
          lat: destinationInfo.coordinates.lat,
          lng: destinationInfo.coordinates.lng,
          day: 'All days',
          type: 'attraction',
          description: 'The main city center with various attractions.',
        },
        {
          name: 'Hotel',
          lat: destinationInfo.coordinates.lat,
          lng: destinationInfo.coordinates.lng,
          day: 'All days',
          type: 'hotel',
          description: 'Your accommodation for the trip.',
        },
        {
          name: 'Restaurant',
          lat: destinationInfo.coordinates.lat,
          lng: destinationInfo.coordinates.lng,
          day: 'All days',
          type: 'restaurant',
          description: 'A local restaurant serving traditional cuisine.',
        },
      ],
      localPhrases: this.createLocalPhrases(destinationInfo),
      travelTips: [
        {
          category: 'General',
          tips: [
            'Research the destination before your trip',
            'Keep important documents secure',
            'Stay hydrated and wear sunscreen',
          ],
        },
      ],
      budget: [
        {
          category: 'Accommodation',
          estimatedCost: 'Varies by choice',
          notes: 'Hotels, hostels, and apartments available',
        },
        {
          category: 'Food',
          estimatedCost: 'Varies by restaurant choice',
          notes: 'Budget for 3 meals per day plus snacks',
        },
        {
          category: 'Activities',
          estimatedCost: 'Varies by activity',
          notes: 'Many attractions have entrance fees',
        },
      ],
      userPreferences: {
        travelStyle: ['sightseeing', 'cultural'],
        budget: {
          level: 'moderate',
          currency: 'EUR',
        },
        accommodation: {
          type: ['hotel'],
          preferences: ['central location'],
        },
        dining: {
          cuisinePreferences: ['local cuisine'],
          dietaryRestrictions: [],
          mealPreferences: ['restaurants'],
        },
        activities: {
          interests: ['sightseeing', 'cultural'],
          activityLevel: 'moderate',
          preferredActivities: ['city tours', 'museums'],
          primaryActivityType: 'cultural',
          specificActivities: [
            {
              name: 'city walking tour',
              importance: 'preferred',
              details: 'To get an overview of the main attractions',
            },
          ],
        },
        accessibility: {
          requirements: [],
          mobilityIssues: false,
        },
        transportation: {
          preferredModes: ['walking', 'public transport'],
          publicTransportPreference: true,
          specificTransportRequirements: [],
        },
        specialRequests: [],
        travelWithChildren: false,
        travelWithPets: false,
        sustainabilityPreference: false,
        seasonalPreferences: ['all seasons'],
        timeOfDayPreferences: {
          morningActivities: ['sightseeing'],
          afternoonActivities: ['museums'],
          eveningActivities: ['dining'],
        },
        tripPurpose: 'general sightseeing and cultural exploration',
        visualTheme: 'classic travel guide',
        specialInterests: ['local culture', 'history', 'architecture'],
        mustIncludeKeywords: ['must-see attractions', 'local experiences'],
      },
      localRecommendations: {
        hiddenGems: [],
        localEvents: [],
        seasonalActivities: [],
        localCuisine: [],
        transportationTips: [],
        safetyTips: [],
        culturalEtiquette: [],
        localCustoms: [],
        shoppingDistricts: [],
        nightlifeOptions: [],
        annualEvents: [],
        localEtiquette: [],
        usefulResources: {
          apps: [],
          websites: [],
          books: [],
          localServices: [],
        },
        localPhrases: [],
      },
      activityPlan: {
        days,
        alternativeActivities: [],
        rainyDayOptions: [],
        eveningEntertainment: [],
      },
      accommodationDining: {
        accommodations: {
          recommended: [],
          alternatives: [],
          neighborhoodGuide: [
            {
              name: 'City Center',
              description: 'The main city center area',
              bestFor: ['Tourists', 'First-time visitors'],
            },
          ],
          alternativeOptions: [
            {
              type: 'Hostel',
              description: 'Budget-friendly shared accommodations',
              priceRange: 'Budget',
            },
          ],
        },
        dining: {
          breakfast: [],
          lunch: [],
          dinner: [],
          cafes: [],
          specialOccasion: [],
          localSpecialties: [
            {
              dish: 'Local Specialty',
              description: 'A traditional dish from the region',
              whereToTry: ['Local Restaurant'],
            },
          ],
          streetFood: [
            {
              name: 'Street Food Vendor',
              description: 'Popular street food option',
              location: 'City Center',
              price: 'Budget',
            },
          ],
          markets: [
            {
              name: 'Local Market',
              description: 'Traditional food market',
              location: 'City Center',
              specialties: ['Fresh produce', 'Local delicacies'],
            },
          ],
        },
        culinaryExperiences: {
          cookingClasses: [
            {
              name: 'Local Cooking Class',
              description: 'Learn to cook local dishes',
              duration: '2-3 hours',
              price: 'Moderate',
            },
          ],
          foodTours: [
            {
              name: 'City Food Tour',
              description: 'Sample local specialties',
              duration: '3-4 hours',
              price: 'Moderate',
            },
          ],
          tastings: [
            {
              name: 'Local Tasting',
              description: 'Sample local products',
              price: 'Moderate',
            },
          ],
          diningEvents: [
            {
              name: 'Special Dining Event',
              description: 'Traditional dining experience',
              price: 'Premium',
            },
          ],
        },
      },
      practicalInfo: {
        transportation: {
          gettingThere: [],
          localTransportation: [],
          dayTrips: [],
        },
        weather: {
          season: 'Varies',
          averageTemperature: 'Check current forecasts',
          precipitation: 'Varies by season',
          whatToPack: ['Weather-appropriate clothing'],
          bestTimeToVisit: 'Spring and Fall',
          seasonalInfo: [
            {
              season: 'Summer',
              description: 'Warm and sunny',
              averageTemperature: '25-30°C',
              precipitation: 'Low',
              crowdLevel: 'High',
              recommendation: 'Good for outdoor activities',
            },
            {
              season: 'Winter',
              description: 'Cold and sometimes snowy',
              averageTemperature: '0-5°C',
              precipitation: 'Moderate',
              crowdLevel: 'Low',
              recommendation: 'Good for indoor activities',
            },
          ],
        },
        practicalTips: [
          {
            category: 'Safety',
            tips: [
              'Keep your belongings secure',
              'Be aware of your surroundings',
            ],
          },
        ],
        emergencyInfo: {
          emergencyNumbers: {
            police: 'Check local emergency numbers',
            ambulance: 'Check local emergency numbers',
            fireService: 'Check local emergency numbers',
            touristPolice: 'Check if available',
          },
          hospitals: [
            {
              name: 'City Hospital',
              address: 'City Center',
              phone: 'Check locally',
              coordinates: {
                lat: '0',
                lng: '0',
              },
              languages: ['Local language', 'English'],
              specialties: ['Emergency care', 'General medicine'],
            },
          ],
          embassies: [
            {
              country: 'Various',
              address: 'Check locally',
              phone: 'Check locally',
              email: 'Check locally',
            },
          ],
          medicalServices: [
            {
              type: 'Pharmacy',
              name: 'City Pharmacy',
              address: 'City Center',
              phone: 'Check locally',
              languages: ['Local language', 'English'],
              hours: 'Varies',
            },
          ],
          travelInsuranceRecommendations: [
            'Comprehensive travel insurance recommended',
          ],
        },
        visaRequirements: 'Check official sources',
        currencyInfo: {
          currency: {
            name: 'Local Currency',
            code: 'XXX',
            symbol: '$',
            subunit: 'cents',
          },
          exchangeRates: {
            usd: 1.0,
            eur: 0.85,
            gbp: 0.75,
            lastUpdated: 'Check current rates',
            trend: 'Stable',
            bestExchangeLocations: ['Banks', 'ATMs'],
          },
          costs: {
            budget: {
              dailyBudget: '$30-50 per day',
              accommodation: '$15-25 per night',
              meals: '$10-15 per day',
              transport: '$5-10 per day',
            },
            midRange: {
              dailyBudget: '$50-100 per day',
              accommodation: '$25-60 per night',
              meals: '$15-30 per day',
              transport: '$10-20 per day',
            },
            luxury: {
              dailyBudget: '$100+ per day',
              accommodation: '$60+ per night',
              meals: '$30+ per day',
              transport: '$20+ per day',
            },
            specificCosts: [
              {
                item: 'Coffee',
                cost: '$2-5',
                notes: 'Varies by location',
              },
            ],
          },
          paymentMethods: {
            creditCards: {
              acceptance: 'Widely accepted',
              preferredCards: ['Visa', 'Mastercard'],
              fees: 'Check with your bank',
              tips: ['Always have some cash'],
            },
            cash: {
              importance: 'Essential for small vendors',
              denominations: ['Small bills recommended'],
              tips: ['Keep cash secure'],
            },
            digitalPayments: [
              {
                name: 'Local payment app',
                availability: 'Check locally',
                setupInstructions: 'May require local account',
              },
            ],
          },
          banking: {
            bankingHours: {
              weekdays: '9:00-17:00',
              saturday: '9:00-13:00',
              sunday: 'Closed',
              holidays: 'Closed',
            },
            atmInfo: {
              availability: 'Widely available in urban areas',
              networks: ['Plus', 'Cirrus'],
              fees: 'Check with your bank',
              dailyLimits: 'Varies',
              locations: ['Banks', 'Shopping centers'],
            },
            bankBranches: [
              {
                name: 'Major Local Bank',
                services: ['Currency exchange'],
                locations: 'City center',
                contact: 'Check locally',
              },
            ],
          },
          tipping: {
            culture: 'Varies by country',
            restaurants: '10-15%',
            taxis: '10%',
            hotels: '$1-2 per service',
            tours: '$5-10 per person',
            other: [
              {
                service: 'General service',
                amount: '10%',
              },
            ],
          },
          moneyApps: [
            {
              name: 'XE Currency',
              purpose: 'Exchange rates',
              availability: 'Global',
              url: 'https://xe.com',
            },
          ],
        },
        internetConnectivity: {
          wifiAvailability: 'Check locally',
          simCardInfo: 'Available at airports and shops',
          internetCafes: ['Available in tourist areas'],
          publicWifiSpots: ['Some cafes and public spaces'],
          typicalSpeeds: 'Varies by location',
          recommendedApps: [
            {
              name: 'Local Maps App',
              purpose: 'Navigation',
              platform: 'iOS, Android',
              url: 'app store',
            },
          ],
        },
        languageInfo: {
          officialLanguage: 'Local language',
          languageLevel: 'Basic phrases helpful',
          usefulPhrases: [
            {
              phrase: 'Hello',
              translation: 'Local translation',
              pronunciation: 'Local pronunciation',
              situation: 'Greeting',
            },
          ],
          languageApps: [
            {
              name: 'Translation App',
              features: ['Offline translation', 'Voice recognition'],
              url: 'app store',
            },
          ],
          englishAvailability: 'Common in tourist areas',
        },
        electricityInfo: {
          voltage: '220-240V',
          frequency: '50Hz',
          plugType: 'Type C/F',
          adapterNeeded: true,
          adapterType: 'European adapter',
          images: [
            {
              description: 'Type C plug',
              url: 'image url',
            },
          ],
        },
        businessHours: [
          {
            category: 'Shops',
            weekdayHours: '9:00-18:00',
            weekendHours: '10:00-16:00',
            holidays: 'Closed on major holidays',
            notes: 'Some shops close for lunch',
          },
        ],
        packingChecklist: ['Passport', 'Appropriate clothing', 'Medications'],
        customsRegulations: ['Check specific restrictions'],
        travelInsurance: 'Recommended',
        healthInfo: {
          vaccinations: ['Check with travel clinic'],
          healthPrecautions: ['Drink bottled water'],
          covid19Info: 'Check current requirements',
          localMedication: [
            {
              internationalName: 'Paracetamol',
              localName: 'Local name',
              availability: 'Widely available',
            },
          ],
          waterSafety: 'Varies by region',
          foodSafety: 'Generally good in restaurants',
        },
        accessibilityInfo: {
          overview: 'Varies by location',
          publicTransport: 'Limited accessibility',
          attractions: 'Major attractions have some accessibility',
          hotels: 'International chains typically have accessible rooms',
          restaurants: 'Varies widely',
          specialServices: [
            {
              name: 'Accessibility Service',
              description: 'Assistance for travelers with mobility issues',
              contact: 'Check locally',
              website: 'website url',
            },
          ],
        },
        familyInfo: {
          overview: 'Generally family-friendly',
          kidFriendlyAttractions: [
            {
              name: 'City Park',
              description: 'Open space with playground',
              ageRange: 'All ages',
              address: 'City Center',
              hours: 'Always open',
              price: 'Free',
            },
          ],
          childcareServices: [
            {
              name: 'Hotel Babysitting',
              description: 'Available at major hotels',
              contact: 'Ask at hotel',
              languages: ['Local language', 'English'],
            },
          ],
          familyFriendlyRestaurants: ['Many restaurants welcome children'],
          tips: ['Plan activities suitable for children'],
        },
        sustainabilityInfo: {
          overview: 'Growing focus on sustainability',
          ecoFriendlyTransport: ['Public transportation', 'Bike rentals'],
          sustainableAccommodations: ['Some eco-certified hotels available'],
          localInitiatives: [
            {
              name: 'Local Recycling Program',
              description: 'City-wide recycling initiative',
              contact: 'City website',
            },
          ],
          responsibleTourismTips: [
            'Respect local environment',
            'Minimize waste',
          ],
        },
        resources: {
          usefulApps: [
            {
              name: 'City Guide App',
              category: 'Travel',
              description: 'Comprehensive city information',
              platform: 'iOS, Android',
              url: 'app store',
            },
          ],
          recommendedBooks: [
            {
              title: 'City Travel Guide',
              author: 'Travel Writer',
              description: 'Comprehensive guide to the city',
              type: 'Travel guide',
            },
          ],
          officialWebsites: [
            {
              name: 'Official Tourism Website',
              url: 'website url',
              description: 'Official information for tourists',
            },
          ],
        },
      },
      evaluationScore: 7.0,
      improvementNotes: ['Basic plan created due to orchestration failure'],
      tripClassification,
      visualTemplate,
      specializedActivityData: {
        customSections: [
          {
            title: 'Getting Around',
            content: 'Information about getting around in the destination.',
            type: 'text',
          },
          {
            title: 'Local Tips',
            content: 'Tips from locals about the destination.',
            type: 'list',
          },
        ],
      },
    };
  }
}
