'use client';

import { useArtifactSelector } from './use-artifact';
import { useEffect, useState } from 'react';

/**
 * Hook qui expose l'état de visibilité des artifacts
 * Utile pour les composants qui doivent réagir à l'ouverture/fermeture des artifacts
 */
export function useArtifactVisibility() {
  // Récupérer l'état de visibilité et le type de l'artifact actuel
  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);
  const artifactKind = useArtifactSelector((state) => state.kind);
  const artifactTitle = useArtifactSelector((state) => state.title || '');

  // État local pour suivre si un artifact HTML est visible
  const [isHtmlArtifactVisible, setIsHtmlArtifactVisible] = useState(false);

  // État pour déterminer si l'artifact visible est un itinéraire de voyage
  const [isTravelItineraryVisible, setIsTravelItineraryVisible] =
    useState(false);

  // Mettre à jour l'état local lorsque l'état global change
  useEffect(() => {
    if (artifactKind === 'html' && isArtifactVisible) {
      setIsHtmlArtifactVisible(true);

      // Vérifier si c'est un itinéraire de voyage basé sur le titre
      const isTravelTitle =
        artifactTitle.toLowerCase().includes('voyage') ||
        artifactTitle.toLowerCase().includes('trip') ||
        artifactTitle.toLowerCase().includes('travel') ||
        artifactTitle.toLowerCase().includes('itinerary') ||
        artifactTitle.toLowerCase().includes('itinéraire') ||
        artifactTitle.toLowerCase().includes('visit') ||
        artifactTitle.toLowerCase().includes('visiter');

      setIsTravelItineraryVisible(isTravelTitle);
    } else if (!isArtifactVisible) {
      // Ajouter un petit délai pour éviter les clignotements lors de la fermeture
      const timer = setTimeout(() => {
        setIsHtmlArtifactVisible(false);
        setIsTravelItineraryVisible(false);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [isArtifactVisible, artifactKind, artifactTitle]);

  return {
    isArtifactVisible,
    isHtmlArtifactVisible,
    isTravelItineraryVisible,
    artifactKind,
  };
}
