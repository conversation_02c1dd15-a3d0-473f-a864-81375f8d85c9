// ExampleExtremeSearchPage.tsx
// -----------------------------------
// Exemple de page React intégrant Extreme Search via le hook useChat.
// Copie-colle ce composant dans ton application (Next.js, Create React App, etc.)
// et ajuste le chemin d'import de ExtremeSearch ainsi que l'URL API selon ton projet.

'use client';

import { useChat } from '@ai-sdk/react';
import ExtremeSearch from './components/extreme-search';

export default function ExampleExtremeSearchPage() {
  // Initialise le hook useChat qui gère le streaming SSE + state message
  const {
    messages,
    setMessages,
    append,    // pour envoyer un message utilisateur
    status,
    error,
  } = useChat({
    api: '/api/extreme-search', // route serveur qui appelle extremeSearchTool
  });

  // Dernier message de l'assistant contenant la timeline / annotations
  const lastAssistantMessage = messages.findLast((m) => m.role === 'assistant');
  const annotations = lastAssistantMessage?.annotations ?? [];

  return (
    <div className="p-6 space-y-4">
      {/* Formulaire utilisateur minimal */}
      <form
        onSubmit={async (e) => {
          e.preventDefault();
          const formData = new FormData(e.currentTarget);
          const prompt = formData.get('prompt') as string;
          if (!prompt) return;
          await append({ role: 'user', content: prompt });
          e.currentTarget.reset();
        }}
        className="flex gap-2"
      >
        <input
          name="prompt"
          placeholder="Entrez votre sujet de recherche…"
          className="flex-1 border p-2 rounded-md"
        />
        <button type="submit" className="bg-blue-600 text-white px-4 rounded-md">
          Lancer
        </button>
      </form>

      {/* Affichage de la timeline lorsqu'elle existe */}
      {annotations.length > 0 && (
        <ExtremeSearch
          annotations={annotations as any[]}
          toolInvocation={{ id: 'extreme-1', name: 'extreme_search' } as any}
        />
      )}

      {status === 'streaming' && <p>Recherche en cours…</p>}
      {error && <p className="text-red-500">Erreur : {error.message}</p>}
    </div>
  );
}
