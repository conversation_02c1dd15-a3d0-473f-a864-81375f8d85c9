/**
 * Complete carousel templates for travel itineraries
 * These are the working carousel implementations that were functioning before
 */

export const CAROUSEL_CSS = `
/* Carousel Wrapper Styles */
.restaurant-carousel-wrapper, .hotel-carousel-wrapper, .poi-carousel-wrapper {
  position: relative;
  overflow: hidden;
  margin: 20px 0;
}

/* Carousel Track Styles */
.restaurant-carousel-track, .hotel-carousel-track, .poi-carousel-track {
  display: flex;
  transition: transform 0.3s ease;
}

/* Slide Styles */
.restaurant-slide, .hotel-slide, .poi-slide {
  flex: 0 0 100%;
  padding: 0 10px;
  box-sizing: border-box;
}

/* Navigation Button Styles */
.restaurant-carousel-prev, .restaurant-carousel-next,
.hotel-carousel-prev, .hotel-carousel-next,
.poi-carousel-prev, .poi-carousel-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0,0,0,0.7);
  color: white;
  border: none;
  padding: 12px 16px;
  cursor: pointer;
  font-size: 20px;
  z-index: 10;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.restaurant-carousel-prev:hover, .restaurant-carousel-next:hover,
.hotel-carousel-prev:hover, .hotel-carousel-next:hover,
.poi-carousel-prev:hover, .poi-carousel-next:hover {
  background: rgba(0,0,0,0.9);
}

.restaurant-carousel-prev, .hotel-carousel-prev, .poi-carousel-prev {
  left: 10px;
}

.restaurant-carousel-next, .hotel-carousel-next, .poi-carousel-next {
  right: 10px;
}

/* Indicator Styles */
.restaurant-carousel-indicators, .hotel-carousel-indicators, .poi-carousel-indicators {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  gap: 8px;
}

.restaurant-carousel-indicator, .hotel-carousel-indicator, .poi-carousel-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: #ccc;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.restaurant-carousel-indicator:hover, .hotel-carousel-indicator:hover, .poi-carousel-indicator:hover {
  background: #999;
}

.restaurant-carousel-indicator.active, .hotel-carousel-indicator.active, .poi-carousel-indicator.active {
  background: #007bff;
}

/* Card Styles for Carousel Items */
.restaurant-slide .card, .hotel-slide .card, .poi-slide .card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.restaurant-slide .card:hover, .hotel-slide .card:hover, .poi-slide .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0,0,0,0.15);
}

.restaurant-slide .card img, .hotel-slide .card img, .poi-slide .card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.restaurant-slide .card .card-content, .hotel-slide .card .card-content, .poi-slide .card .card-content {
  padding: 20px;
}

.restaurant-slide .card h3, .hotel-slide .card h3, .poi-slide .card h3 {
  margin: 0 0 10px 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

.restaurant-slide .card p, .hotel-slide .card p, .poi-slide .card p {
  margin: 0 0 10px 0;
  color: #666;
  line-height: 1.5;
}

.restaurant-slide .card .rating, .hotel-slide .card .rating, .poi-slide .card .rating {
  color: #f39c12;
  font-weight: 500;
}

.restaurant-slide .card .price, .hotel-slide .card .price, .poi-slide .card .price {
  color: #27ae60;
  font-weight: 600;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .restaurant-carousel-prev, .restaurant-carousel-next,
  .hotel-carousel-prev, .hotel-carousel-next,
  .poi-carousel-prev, .poi-carousel-next {
    padding: 8px 12px;
    font-size: 16px;
  }

  .restaurant-slide, .hotel-slide, .poi-slide {
    padding: 0 5px;
  }
}
`;

export const CAROUSEL_JAVASCRIPT = `
/**
 * Complete Restaurant Carousel Component
 * This is the working implementation that was functioning before
 */
class RestaurantCarousel {
  constructor(containerId, options = {}) {
    this.container = document.getElementById(containerId);
    if (!this.container) {
      console.error(\`Container with ID "\${containerId}" not found.\`);
      return;
    }

    // Default options
    this.options = {
      slidesToShow: 1,
      slidesToScroll: 1,
      autoplay: false,
      autoplaySpeed: 5000,
      infinite: true,
      responsive: [
        {
          breakpoint: 1024,
          settings: {
            slidesToShow: 1,
            slidesToScroll: 1
          }
        }
      ],
      ...options
    };

    this.currentSlide = 0;
    this.totalSlides = 0;
    this.isAnimating = false;
    this.touchStartX = 0;
    this.touchEndX = 0;
    this.autoplayInterval = null;

    this.init();
  }

  init() {
    // Create carousel structure
    this.createCarouselStructure();

    // Initialize slides
    this.slides = this.container.querySelectorAll('.restaurant-slide');
    this.totalSlides = this.slides.length;

    if (this.totalSlides === 0) {
      console.warn('No restaurant slides found');
      return;
    }

    // Set initial state
    this.updateCarousel();

    // Add event listeners
    this.addEventListeners();

    // Start autoplay if enabled
    if (this.options.autoplay) {
      this.startAutoplay();
    }
  }

  createCarouselStructure() {
    // Create wrapper for the carousel
    const wrapper = document.createElement('div');
    wrapper.className = 'restaurant-carousel-wrapper';

    // Create track for slides
    const track = document.createElement('div');
    track.className = 'restaurant-carousel-track';

    // Move existing content into the track
    while (this.container.firstChild) {
      track.appendChild(this.container.firstChild);
    }

    // Create navigation buttons
    const prevButton = document.createElement('button');
    prevButton.className = 'restaurant-carousel-prev';
    prevButton.setAttribute('aria-label', 'Previous restaurant');
    prevButton.innerHTML = '‹';

    const nextButton = document.createElement('button');
    nextButton.className = 'restaurant-carousel-next';
    nextButton.setAttribute('aria-label', 'Next restaurant');
    nextButton.innerHTML = '›';

    // Create indicators
    const indicators = document.createElement('div');
    indicators.className = 'restaurant-carousel-indicators';

    // Append all elements
    wrapper.appendChild(track);
    wrapper.appendChild(prevButton);
    wrapper.appendChild(nextButton);
    wrapper.appendChild(indicators);
    this.container.appendChild(wrapper);

    // Save references
    this.track = track;
    this.prevButton = prevButton;
    this.nextButton = nextButton;
    this.indicators = indicators;
  }

  addEventListeners() {
    // Button navigation
    this.prevButton.addEventListener('click', () => this.prevSlide());
    this.nextButton.addEventListener('click', () => this.nextSlide());

    // Touch events for swipe
    this.track.addEventListener('touchstart', (e) => {
      this.touchStartX = e.changedTouches[0].screenX;
    }, { passive: true });

    this.track.addEventListener('touchend', (e) => {
      this.touchEndX = e.changedTouches[0].screenX;
      this.handleSwipe();
    }, { passive: true });

    // Keyboard navigation
    this.container.addEventListener('keydown', (e) => {
      if (e.key === 'ArrowLeft') {
        this.prevSlide();
      } else if (e.key === 'ArrowRight') {
        this.nextSlide();
      }
    });

    // Pause autoplay on hover
    if (this.options.autoplay) {
      this.container.addEventListener('mouseenter', () => this.stopAutoplay());
      this.container.addEventListener('mouseleave', () => this.startAutoplay());
      this.container.addEventListener('touchstart', () => this.stopAutoplay(), { passive: true });
      this.container.addEventListener('touchend', () => this.startAutoplay(), { passive: true });
    }

    // Update on window resize
    window.addEventListener('resize', () => {
      this.updateCarousel();
    });
  }

  handleSwipe() {
    const swipeThreshold = 50;
    const diff = this.touchStartX - this.touchEndX;

    if (diff > swipeThreshold) {
      // Swipe left, go to next slide
      this.nextSlide();
    } else if (diff < -swipeThreshold) {
      // Swipe right, go to previous slide
      this.prevSlide();
    }
  }

  prevSlide() {
    if (this.isAnimating) return;

    this.isAnimating = true;
    this.currentSlide = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
    this.updateCarousel();

    setTimeout(() => {
      this.isAnimating = false;
    }, 300);
  }

  nextSlide() {
    if (this.isAnimating) return;

    this.isAnimating = true;
    this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
    this.updateCarousel();

    setTimeout(() => {
      this.isAnimating = false;
    }, 300);
  }

  updateCarousel() {
    if (!this.slides || this.slides.length === 0) return;

    // Update slide positions
    const slideWidth = this.slides[0].offsetWidth;
    this.track.style.transform = \`translateX(-\${this.currentSlide * slideWidth}px)\`;

    // Update active state for slides
    this.slides.forEach((slide, index) => {
      if (index === this.currentSlide) {
        slide.classList.add('active');
        slide.setAttribute('aria-hidden', 'false');
      } else {
        slide.classList.remove('active');
        slide.setAttribute('aria-hidden', 'true');
      }
    });

    // Update indicators
    this.updateIndicators();

    // Update button states
    if (!this.options.infinite) {
      this.prevButton.disabled = this.currentSlide === 0;
      this.nextButton.disabled = this.currentSlide === this.totalSlides - 1;
    }
  }

  updateIndicators() {
    // Clear existing indicators
    this.indicators.innerHTML = '';

    // Create new indicators
    for (let i = 0; i < this.totalSlides; i++) {
      const indicator = document.createElement('button');
      indicator.className = 'restaurant-carousel-indicator';
      indicator.setAttribute('aria-label', \`Go to restaurant \${i + 1}\`);

      if (i === this.currentSlide) {
        indicator.classList.add('active');
        indicator.setAttribute('aria-current', 'true');
      }

      indicator.addEventListener('click', () => {
        this.goToSlide(i);
      });

      this.indicators.appendChild(indicator);
    }
  }

  goToSlide(index) {
    if (this.isAnimating || index === this.currentSlide) return;

    this.isAnimating = true;
    this.currentSlide = index;
    this.updateCarousel();

    setTimeout(() => {
      this.isAnimating = false;
    }, 300);
  }

  startAutoplay() {
    if (!this.options.autoplay) return;

    this.autoplayInterval = setInterval(() => {
      this.nextSlide();
    }, this.options.autoplaySpeed);
  }

  stopAutoplay() {
    if (this.autoplayInterval) {
      clearInterval(this.autoplayInterval);
      this.autoplayInterval = null;
    }
  }
}

/**
 * Hotel Carousel - Extends RestaurantCarousel with hotel-specific functionality
 */
class HotelCarousel extends RestaurantCarousel {
  init() {
    // Create carousel structure
    this.createCarouselStructure();

    // Initialize slides with hotel-specific selector
    this.slides = this.container.querySelectorAll('.hotel-slide');
    this.totalSlides = this.slides.length;

    if (this.totalSlides === 0) {
      console.warn('No hotel slides found');
      return;
    }

    // Set initial state
    this.updateCarousel();

    // Add event listeners
    this.addEventListeners();

    // Start autoplay if enabled
    if (this.options.autoplay) {
      this.startAutoplay();
    }
  }

  createCarouselStructure() {
    // Create wrapper for the carousel
    const wrapper = document.createElement('div');
    wrapper.className = 'hotel-carousel-wrapper';

    // Create track for slides
    const track = document.createElement('div');
    track.className = 'hotel-carousel-track';

    // Move existing content into the track
    while (this.container.firstChild) {
      track.appendChild(this.container.firstChild);
    }

    // Create navigation buttons
    const prevButton = document.createElement('button');
    prevButton.className = 'hotel-carousel-prev';
    prevButton.setAttribute('aria-label', 'Previous hotel');
    prevButton.innerHTML = '‹';

    const nextButton = document.createElement('button');
    nextButton.className = 'hotel-carousel-next';
    nextButton.setAttribute('aria-label', 'Next hotel');
    nextButton.innerHTML = '›';

    // Create indicators
    const indicators = document.createElement('div');
    indicators.className = 'hotel-carousel-indicators';

    // Append all elements
    wrapper.appendChild(track);
    wrapper.appendChild(prevButton);
    wrapper.appendChild(nextButton);
    wrapper.appendChild(indicators);
    this.container.appendChild(wrapper);

    // Save references
    this.track = track;
    this.prevButton = prevButton;
    this.nextButton = nextButton;
    this.indicators = indicators;
  }

  updateIndicators() {
    // Clear existing indicators
    this.indicators.innerHTML = '';

    // Create new indicators
    for (let i = 0; i < this.totalSlides; i++) {
      const indicator = document.createElement('button');
      indicator.className = 'hotel-carousel-indicator';
      indicator.setAttribute('aria-label', \`Go to hotel \${i + 1}\`);

      if (i === this.currentSlide) {
        indicator.classList.add('active');
        indicator.setAttribute('aria-current', 'true');
      }

      indicator.addEventListener('click', () => {
        this.goToSlide(i);
      });

      this.indicators.appendChild(indicator);
    }
  }
}

/**
 * POI Carousel - Extends RestaurantCarousel with POI-specific functionality
 */
class PoiCarousel extends RestaurantCarousel {
  init() {
    // Create carousel structure
    this.createCarouselStructure();

    // Initialize slides with POI-specific selector
    this.slides = this.container.querySelectorAll('.poi-slide');
    this.totalSlides = this.slides.length;

    if (this.totalSlides === 0) {
      console.warn('No POI slides found');
      return;
    }

    // Set initial state
    this.updateCarousel();

    // Add event listeners
    this.addEventListeners();

    // Start autoplay if enabled
    if (this.options.autoplay) {
      this.startAutoplay();
    }
  }

  createCarouselStructure() {
    // Create wrapper for the carousel
    const wrapper = document.createElement('div');
    wrapper.className = 'poi-carousel-wrapper';

    // Create track for slides
    const track = document.createElement('div');
    track.className = 'poi-carousel-track';

    // Move existing content into the track
    while (this.container.firstChild) {
      track.appendChild(this.container.firstChild);
    }

    // Create navigation buttons
    const prevButton = document.createElement('button');
    prevButton.className = 'poi-carousel-prev';
    prevButton.setAttribute('aria-label', 'Previous attraction');
    prevButton.innerHTML = '‹';

    const nextButton = document.createElement('button');
    nextButton.className = 'poi-carousel-next';
    nextButton.setAttribute('aria-label', 'Next attraction');
    nextButton.innerHTML = '›';

    // Create indicators
    const indicators = document.createElement('div');
    indicators.className = 'poi-carousel-indicators';

    // Append all elements
    wrapper.appendChild(track);
    wrapper.appendChild(prevButton);
    wrapper.appendChild(nextButton);
    wrapper.appendChild(indicators);
    this.container.appendChild(wrapper);

    // Save references
    this.track = track;
    this.prevButton = prevButton;
    this.nextButton = nextButton;
    this.indicators = indicators;
  }

  updateIndicators() {
    // Clear existing indicators
    this.indicators.innerHTML = '';

    // Create new indicators
    for (let i = 0; i < this.totalSlides; i++) {
      const indicator = document.createElement('button');
      indicator.className = 'poi-carousel-indicator';
      indicator.setAttribute('aria-label', \`Go to attraction \${i + 1}\`);

      if (i === this.currentSlide) {
        indicator.classList.add('active');
        indicator.setAttribute('aria-current', 'true');
      }

      indicator.addEventListener('click', () => {
        this.goToSlide(i);
      });

      this.indicators.appendChild(indicator);
    }
  }
}

// Initialize all carousels when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  console.log('Initializing carousels...');

  // Initialize restaurant carousel
  if (document.getElementById('restaurant-carousel')) {
    console.log('Initializing restaurant carousel');
    new RestaurantCarousel('restaurant-carousel', {
      autoplay: false,
      infinite: true
    });
  }

  // Initialize hotel carousel
  if (document.getElementById('hotel-carousel')) {
    console.log('Initializing hotel carousel');
    new HotelCarousel('hotel-carousel', {
      autoplay: false,
      infinite: true
    });
  }

  // Initialize accommodation carousel (alias for hotel carousel)
  if (document.getElementById('accommodation-carousel')) {
    console.log('Initializing accommodation carousel');
    new HotelCarousel('accommodation-carousel', {
      autoplay: false,
      infinite: true
    });
  }

  // Initialize POI carousel
  if (document.getElementById('poi-carousel')) {
    console.log('Initializing POI carousel');
    new PoiCarousel('poi-carousel', {
      autoplay: false,
      infinite: true
    });
  }
});

// Export for use in HTML artifacts
if (typeof window !== 'undefined') {
  window.RestaurantCarousel = RestaurantCarousel;
  window.HotelCarousel = HotelCarousel;
  window.PoiCarousel = PoiCarousel;
}
`;
