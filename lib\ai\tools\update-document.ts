import { tool } from 'ai';
import { z } from 'zod';
import type { Session } from 'next-auth';
import type { DataStreamWriter } from 'ai';
import { getDocumentById } from '@/lib/db/queries';
import { documentHandlersByArtifactKind } from '@/lib/artifacts/server';
import { enqueueMemory } from '@/lib/memoryBatcher';

// Fonction pour sauvegarder la demande de mise à jour dans la mémoire
async function saveUpdateRequestToMemory({
  id,
  title,
  kind,
  description,
  userId,
}: {
  id: string;
  title: string;
  kind: string;
  description: string;
  userId: string;
}) {
  const memoryContent = `LLM update request for document: "${title}" (${kind})
Update description: ${description}`;

  // Utiliser le système de batch pour améliorer les performances
  enqueueMemory({
    data: memoryContent,
    userId,
    metadata: {
      documentId: id,
      documentTitle: title,
      documentKind: kind,
      action: 'update_request',
      timestamp: new Date().toISOString(),
    },
  });

  console.log(`Update request queued for document:`, id);
}

// Fonction pour sauvegarder le contenu mis à jour dans la mémoire
async function saveUpdatedContentToMemory({
  id,
  title,
  kind,
  content,
  userId,
}: {
  id: string;
  title: string;
  kind: string;
  content: string;
  userId: string;
}) {
  // Utiliser le système de batch pour améliorer les performances
  enqueueMemory({
    data: content,
    userId,
    metadata: {
      documentId: id,
      documentTitle: title,
      documentKind: kind,
      action: 'content',
      isDocumentContent: true,
      timestamp: new Date().toISOString(),
    },
  });

  console.log(`Updated content queued for document:`, id);
}

interface UpdateDocumentProps {
  session: Session;
  dataStream: DataStreamWriter;
}

export const updateDocument = ({ session, dataStream }: UpdateDocumentProps) =>
  tool({
    description:
      'Update an existing document with new content based on the description. You can also update the title.',
    parameters: z.object({
      id: z.string(),
      description: z.string(),
      newTitle: z.string().optional(),
    }),
    execute: async ({ id, description, newTitle }) => {
      // Indiquer que le processus commence
      dataStream.writeData({
        type: 'status',
        content: 'Récupération du document...',
      });

      const document = await getDocumentById({ id });

      if (!document) {
        throw new Error(`Document with id ${id} not found`);
      }

      dataStream.writeData({
        type: 'id',
        content: id,
      });

      // Si un nouveau titre est fourni, l'utiliser, sinon garder l'ancien
      const title = newTitle || document.title;

      dataStream.writeData({
        type: 'title',
        content: title,
      });

      dataStream.writeData({
        type: 'kind',
        content: document.kind,
      });

      dataStream.writeData({
        type: 'clear',
        content: '',
      });

      // Indiquer l'étape actuelle
      dataStream.writeData({
        type: 'status',
        content: 'Préparation de la mise à jour...',
      });

      // Sauvegarder la demande de mise à jour dans la mémoire en arrière-plan
      if (session.user?.id) {
        saveUpdateRequestToMemory({
          id,
          title,
          kind: document.kind,
          description,
          userId: session.user.id,
        });
      }

      const documentHandler = documentHandlersByArtifactKind.find(
        (documentHandlerByArtifactKind) =>
          documentHandlerByArtifactKind.kind === document.kind,
      );

      if (!documentHandler) {
        throw new Error(`No document handler found for kind: ${document.kind}`);
      }

      // Indiquer l'étape actuelle
      dataStream.writeData({
        type: 'status',
        content: 'Génération du contenu mis à jour...',
      });

      const updatedContent = await documentHandler.onUpdateDocument({
        document: {
          ...document,
          title, // Utiliser le nouveau titre si fourni
        },
        description,
        dataStream,
        session,
      });

      // Indiquer l'étape actuelle
      dataStream.writeData({
        type: 'status',
        content: 'Sauvegarde du contenu...',
      });

      // Sauvegarder le contenu mis à jour dans la mémoire en arrière-plan
      if (session.user?.id && updatedContent) {
        saveUpdatedContentToMemory({
          id,
          title,
          kind: document.kind,
          content: updatedContent,
          userId: session.user.id,
        });
      }

      dataStream.writeData({
        type: 'status',
        content: 'Mise à jour terminée !',
      });

      dataStream.writeData({ type: 'finish', content: '' });

      return {
        id,
        title,
        kind: document.kind,
        content: 'The document was updated and is now visible to the user.',
      };
    },
  });
