import * as cheerio from 'cheerio';

interface IconInfo {
  url: string;
  type: string;
  size: number;
}

/**
 * Extract the best favicon from a URL using a Node.js compatible approach
 */
export async function extractFavicon(url: string): Promise<string> {
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
    });

    if (!response.ok) {
      return getFallbackFavicon(url);
    }

    const html = await response.text();
    const icons = parseIconsWithCheerio(html, url);

    if (icons.length === 0) {
      return getFallbackFavicon(url);
    }

    // Sort icons by priority and size
    const sortedIcons = icons.sort((a, b) => {
      // Priority: PNG > JPEG > ICO > others
      const priorityA = getPriority(a.type);
      const priorityB = getPriority(b.type);

      if (priorityA !== priorityB) {
        return priorityB - priorityA;
      }

      // Then by size (larger is better)
      return b.size - a.size;
    });

    return sortedIcons[0].url;
  } catch (error) {
    console.warn(`Failed to extract favicon for ${url}:`, error);
    return getFallbackFavicon(url);
  }
}

/**
 * Récupère tous les favicons d'un site donné.
 */
export async function extractAllFavicons(url: string): Promise<string[]> {
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
    });

    if (!response.ok) {
      return [getFallbackFavicon(url)];
    }

    const html = await response.text();
    const icons = parseIconsWithCheerio(html, url);

    const uniqueUrls = Array.from(new Set(icons.map((icon) => icon.url)));

    // Ajoute le favicon de fallback si absent
    const fallback = getFallbackFavicon(url);
    if (!uniqueUrls.includes(fallback)) {
      uniqueUrls.push(fallback);
    }

    return uniqueUrls;
  } catch (error) {
    console.warn(
      `Erreur lors de l'extraction des favicons pour ${url}:`,
      error,
    );
    return [getFallbackFavicon(url)];
  }
}

/**
 * Parse le HTML et extrait toutes les balises <link rel="...icon..."> avec Cheerio
 */
function parseIconsWithCheerio(html: string, baseUrl: string): IconInfo[] {
  const $ = cheerio.load(html);
  const icons: IconInfo[] = [];

  // Sélectionner tous les liens avec des attributs rel contenant "icon"
  $('link[rel]').each((_, element) => {
    const $element = $(element);
    const rel = ($element.attr('rel') || '').toLowerCase();
    const href = $element.attr('href');
    const sizes = $element.attr('sizes') || '';
    const type = $element.attr('type') || '';

    // Vérifier si c'est un type d'icône que nous voulons
    const isIcon =
      rel.includes('icon') ||
      rel.includes('apple-touch-icon') ||
      rel.includes('fluid-icon') ||
      rel.includes('mask-icon');

    if (isIcon && href) {
      try {
        const absoluteUrl = href.startsWith('http')
          ? href
          : new URL(href, baseUrl).href;

        const iconType = type || extractTypeFromUrl(absoluteUrl);
        const iconSize = parseSize(sizes, rel);

        icons.push({
          url: absoluteUrl,
          type: iconType,
          size: iconSize,
        });
      } catch (error) {
        // Ignorer les URLs malformées
        console.warn(`Invalid URL: ${href} for base ${baseUrl}`);
      }
    }
  });

  // Ajouter le favicon.ico par défaut s'il n'y a pas d'icônes trouvées
  if (icons.length === 0) {
    try {
      const faviconUrl = new URL('/favicon.ico', baseUrl).href;
      icons.push({
        url: faviconUrl,
        type: 'image/x-icon',
        size: 16,
      });
    } catch (error) {
      // Ignorer les erreurs de parsing d'URL
    }
  }

  return icons;
}

/**
 * Déduit la taille d’une icône depuis l’attribut sizes
 */
function parseSize(sizeAttr: string, rel: string): number {
  if (sizeAttr === 'any') return 999;

  const match = sizeAttr.match(/(\d+)x(\d+)/);
  if (match) {
    return Number.parseInt(match[1]) * Number.parseInt(match[2]);
  }

  if (rel.includes('apple')) return 180 * 180;
  return 32 * 32;
}

/**
 * Déduit le type MIME en fonction de l'extension du fichier
 */
function extractTypeFromUrl(url: string): string {
  const ext = url.split('.').pop()?.toLowerCase();
  switch (ext) {
    case 'png':
      return 'image/png';
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'svg':
      return 'image/svg+xml';
    case 'ico':
      return 'image/x-icon';
    default:
      return 'image/x-icon';
  }
}

/**
 * Get priority score for icon type (higher is better)
 */
function getPriority(type: string): number {
  switch (type) {
    case 'image/png':
      return 3;
    case 'image/jpeg':
      return 2;
    case 'image/x-icon':
      return 1;
    default:
      return 0;
  }
}

/**
 * Favicon de secours via Google (utile si aucun trouvé dans le HTML)
 */
function getFallbackFavicon(url: string): string {
  try {
    const domain = new URL(url).hostname;
    return `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(domain)}`;
  } catch {
    return `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(url)}`;
  }
}
