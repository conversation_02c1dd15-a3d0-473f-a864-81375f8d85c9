import { z } from 'zod';
import { tool } from 'ai';

// Define and export the tool configuration
export const getCryptoCoinsHeatmap = tool({
  description:
    'Display a cryptocurrency heatmap with market cap visualization and performance',
  parameters: z.object({
    // No parameters needed for the heatmap as it shows a predefined view
  }),
  execute: async () => {
    // Return a simple object that will be used by the frontend to render the component
    return {
      type: 'crypto_coins_heatmap',
      data: {},
    };
  },
});
