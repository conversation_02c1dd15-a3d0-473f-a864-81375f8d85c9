import { z } from 'zod';
import { generateObject } from 'ai';
import type { LanguageModel } from 'ai';

export interface ValidationResult {
  confidence: number; // 0-1 score
  hasDestination: boolean;
  hasTimeframe: boolean;
  extractedInfo: {
    destination?: string;
    country?: string;
    duration?: number;
    timeframe?: string;
  };
  missingCritical: string[];
  reasoning: string;
  shouldProceed: boolean;
}

export interface ClarifyingQuestion {
  question: string;
  type: 'destination' | 'duration' | 'both';
  suggestions?: string[];
}

export class ValidationAgent {
  private static validationCache = new Map<string, ValidationResult>();
  private static isValidating = new Set<string>();
  private static createdItineraries = new Set<string>(); // Track created itineraries per session

  constructor(private model: LanguageModel) {}

  /**
   * Mark that an itinerary has been created for a destination
   */
  static markItineraryCreated(destination: string) {
    const key = destination.toLowerCase().trim();
    ValidationAgent.createdItineraries.add(key);
    console.log('🎯 Marked itinerary as created for:', destination);
  }

  /**
   * Check if an itinerary has already been created for a destination
   */
  static hasItineraryBeenCreated(destination: string): boolean {
    const key = destination.toLowerCase().trim();
    return ValidationAgent.createdItineraries.has(key);
  }

  /**
   * Clear the created itineraries cache (for new sessions)
   */
  static clearCreatedItineraries() {
    ValidationAgent.createdItineraries.clear();
    console.log('🧹 Cleared created itineraries cache');
  }

  /**
   * Intelligent validation using LLM with loop protection
   */
  async validateTravelRequest(query: string): Promise<ValidationResult> {
    // Protection contre les boucles : cache et verrous
    const cacheKey = query.toLowerCase().trim();

    // 🧠 INTELLIGENT DETECTION: Check for completion/status messages
    const completionPatterns = [
      /^created\s+.+\s+itinerary$/i,
      /^creating\s+.+\s+itinerary$/i,
      /^generated\s+.+\s+travel/i,
      /^finished\s+.+\s+plan/i,
      /^completed\s+.+\s+trip/i,
      /^your\s+.+\s+is\s+ready/i,
      /travel\s+adventure\s+is\s+ready/i,
      /itinerary\s+complete/i,
    ];

    const isCompletionMessage = completionPatterns.some((pattern) =>
      pattern.test(query.trim()),
    );

    if (isCompletionMessage) {
      console.log('🚫 Detected completion message, blocking:', query);
      return {
        confidence: 0.0,
        hasDestination: false,
        hasTimeframe: false,
        extractedInfo: {},
        missingCritical: [],
        reasoning: 'This is a completion/status message, not a travel request',
        shouldProceed: false,
      };
    }

    // 🧠 INTELLIGENT DETECTION: Check if destination already has an itinerary
    const destinationPatterns = [
      /\b(tulum|paris|rome|tokyo|london|new york|barcelona|madrid|berlin|amsterdam|prague|vienna|budapest|lisbon|dublin|stockholm|copenhagen|oslo|helsinki|warsaw|krakow|athens|istanbul|moscow|st petersburg|venice|florence|milan|naples|sicily|sardinia|corsica|nice|cannes|monaco|marseille|lyon|bordeaux|toulouse|strasbourg|lille|nantes|rennes|brest|caen|rouen|reims|dijon|grenoble|montpellier|perpignan|avignon|aix|annecy|chamonix|courchevel|val thorens|megeve|tignes|val disere|alpe huez|les gets|morzine|verbier|zermatt|st moritz|davos|interlaken|lucerne|geneva|zurich|basel|bern|salzburg|innsbruck|graz|linz|hallstatt|vienna|bratislava|budapest|prague|brno|krakow|gdansk|wroclaw|poznan|lublin|vilnius|riga|tallinn|helsinki|turku|tampere|oulu|rovaniemi|tromso|bergen|trondheim|stavanger|kristiansand|gothenburg|malmo|linkoping|uppsala|vasteras|orebro|jonkoping|boras|gavle|sundsvall|umea|lulea|kiruna|copenhagen|aarhus|odense|aalborg|esbjerg|roskilde|helsingore|kolding|vejle|horsens|randers|silkeborg|herning|viborg|holstebro|fredericia|sonderborg|naestved|hillerod|helsingor|roskilde|stockholm|goteborg|malmo|uppsala|vasteras|orebro|linkoping|norrkoping|helsingborg|jonkoping|boras|gavle|sandviken|sodertalje|eskilstuna|karlstad|vaxjo|halmstad|sundsvall|umea|lulea|kiruna|gallivare|haparanda|tornio|kemi|oulu|kajaani|kuopio|joensuu|lappeenranta|kotka|porvoo|espoo|vantaa|tampere|turku|pori|rauma|vaasa|seinajoki|kokkola|ylivieska|kajaani|kuusamo|rovaniemi|kittila|ivalo|utsjoki|inari|sodankyla|kemijärvi|tornio|kemi|oulu|raahe|ylivieska|kokkola|pietarsaari|vaasa|kristiinankaupunki|pori|rauma|turku|salo|lohja|espoo|helsinki|vantaa|porvoo|kotka|hamina|lappeenranta|imatra|joensuu|nurmes|lieksa|ilomantsi|kuopio|iisalmi|kajaani|kuhmo|sotkamo|vuokatti|kuusamo|taivalkoski|pudasjarvi|ii|kempele|liminka|tyrnava|muhos|utajärvi|vaala|pyhanta|kiuruvesi|pielavesi|rautalampi|suonenjoki|leppävirta|varkaus|heinävesi|savonlinna|mikkeli|pieksämäki|jyväskylä|äänekoski|viitasaari|saarijärvi|kannonkoski|kyyjärvi|kinnula|kivijärvi|keuruu|multia|petäjävesi|jämsä|jämsänkoski|kuhmoinen|sysmä|hartola|heinola|lahti|hollola|kärkölä|nastola|orimattila|myrskylä|pukkila|askola|porvoo|sipoo|vantaa|tuusula|järvenpää|kerava|sipoo|kirkkonummi|siuntio|inkoo|karjaa|tammisaari|hanko|raasepori|lohja|vihti|nurmijärvi|hyvinkää|riihimäki|hausjärvi|loppi|kärkölä|nastola|orimattila|artjärvi|pukkila|askola|myrskylä|lapinjärvi|loviisa|ruotsinpyhtää|pyhtää|kotka|hamina|virolahti|miehikkälä|vaalimaa|nuijamaa|imatra|ruokolahti|rautjärvi|parikkala|saari|uukuniemi|taipalsaari|lappeenranta|lemi|luumäki|savitaipale|suomenniemi|puumala|sulkava|sääminki|kerimäki|punkaharju|savonlinna|rantasalmi|joroinen|heinävesi|varkaus|leppävirta|suonenjoki|rautalampi|vesanto|karttula|tervo|kuopio|siilinjärvi|lapinlahti|varpaisjärvi|maaninka|pielavesi|keitele|kiuruvesi|vieremä|sonkajärvi|rautavaara|tuusniemi|outokumpu|liperi|kontiolahti|joensuu|eno|ilomantsi|tohmajärvi|värtsilä|kiihtelysvaara|pyhäselkä|rääkkylä|kitee|kesälahti|rautjärvi|parikkala|saari|uukuniemi|taipalsaari|lappeenranta|lemi|luumäki|savitaipale|suomenniemi|puumala|sulkava|sääminki|kerimäki|punkaharju|savonlinna|rantasalmi|joroinen|heinävesi|varkaus|leppävirta|suonenjoki|rautalampi|vesanto|karttula|tervo|kuopio|siilinjärvi|lapinlahti|varpaisjärvi|maaninka|pielavesi|keitele|kiuruvesi|vieremä|sonkajärvi|rautavaara|tuusniemi|outokumpu|liperi|kontiolahti|joensuu|eno|ilomantsi|tohmajärvi|värtsilä|kiihtelysvaara|pyhäselkä|rääkkylä|kitee|kesälahti|cancun|playa del carmen|cozumel|merida|chichen itza|uxmal|palenque|oaxaca|puerto vallarta|guadalajara|mexico city|acapulco|mazatlan|cabo san lucas|la paz|tijuana|ensenada|rosarito|tecate|mexicali|hermosillo|guaymas|ciudad obregon|los mochis|culiacan|mazatlan|tepic|puerto vallarta|guadalajara|leon|aguascalientes|zacatecas|san luis potosi|queretaro|pachuca|toluca|cuernavaca|puebla|tlaxcala|xalapa|veracruz|villahermosa|campeche|merida|chetumal|cancun|cozumel|playa del carmen|tulum|akumal|xel ha|xcaret|chichen itza|uxmal|kabah|sayil|labna|ek balam|coba|tulum|xel ha|xcaret|akumal|playa del carmen|cancun|isla mujeres|cozumel|holbox|contoy|mujeres|cozumel|chetumal|bacalar|mahahual|xcalak|banco chinchorro|sian kaan|coba|tulum|akumal|xel ha|xcaret|playa del carmen|cancun|isla mujeres|cozumel|holbox|contoy|mujeres|cozumel|chetumal|bacalar|mahahual|xcalak|banco chinchorro|sian kaan)\b/i,
    ];

    for (const pattern of destinationPatterns) {
      const match = query.match(pattern);
      if (match) {
        const destination = match[0];
        if (ValidationAgent.hasItineraryBeenCreated(destination)) {
          console.log('🚫 Itinerary already exists for:', destination);
          return {
            confidence: 0.0,
            hasDestination: false,
            hasTimeframe: false,
            extractedInfo: {},
            missingCritical: [],
            reasoning: `An itinerary for ${destination} has already been created in this session`,
            shouldProceed: false,
          };
        }
      }
    }

    // Si déjà en cours de validation, retourner un résultat optimiste
    if (ValidationAgent.isValidating.has(cacheKey)) {
      console.log(
        'Validation already in progress, returning optimistic result',
      );
      return {
        confidence: 0.8,
        hasDestination: true,
        hasTimeframe: false,
        extractedInfo: {},
        missingCritical: [],
        reasoning: 'Validation in progress - optimistic fallback',
        shouldProceed: true,
      };
    }

    // Vérifier le cache
    const cachedResult = ValidationAgent.validationCache.get(cacheKey);
    if (cachedResult) {
      console.log('Returning cached validation result');
      return cachedResult;
    }

    // Marquer comme en cours de validation
    ValidationAgent.isValidating.add(cacheKey);

    try {
      const { object: validation } = await generateObject({
        model: this.model,
        system: `You are an expert travel request validator. Analyze travel requests to determine if there's enough information to create a quality travel guide.

STRICT VALIDATION RULES:
- Only give high confidence (0.7+) if the destination is SPECIFIC and IDENTIFIABLE
- REJECT vague terms: "ville", "city", "montagne", "beach", "Europe" (too vague)
- ACCEPT specific places: "Paris", "Tokyo", "New York", "Côte d'Azur", "Alpes"

CONFIDENCE SCORING:
- 0.9-1.0: Complete info (specific destination + timeframe)
- 0.7-0.8: Good info (specific destination)
- 0.3-0.6: Partial info (vague destination)
- 0.0-0.2: Insufficient info (no clear destination)`,
        prompt: `Analyze this travel request: "${query}"

Be STRICT: Only give confidence ≥ 0.7 if destination is specific and identifiable.
Reject vague terms like "ville", "city", "montagne", "Europe".
Accept specific places like "Paris", "Tokyo", "Rome".`,
        schema: z.object({
          confidence: z.number().min(0).max(1).describe('Confidence score 0-1'),
          hasDestination: z
            .boolean()
            .describe('Is destination clearly specified?'),
          hasTimeframe: z
            .boolean()
            .describe('Is timeframe/duration specified or implied?'),
          extractedInfo: z.object({
            destination: z
              .string()
              .optional()
              .describe('Extracted destination if found'),
            country: z
              .string()
              .optional()
              .describe('Extracted country if found'),
            duration: z
              .number()
              .optional()
              .describe('Extracted duration in days if found'),
            timeframe: z
              .string()
              .optional()
              .describe('Extracted timeframe description'),
          }),
          missingCritical: z
            .array(z.string())
            .describe('List of critical missing information'),
          reasoning: z.string().describe('Explanation of the confidence score'),
          shouldProceed: z
            .boolean()
            .describe('Should we proceed with trip planning?'),
        }),
        temperature: 0.1, // Low temperature for consistent validation
        maxTokens: 500, // Limite pour éviter les réponses trop longues
      });

      // Mettre en cache le résultat
      ValidationAgent.validationCache.set(cacheKey, validation);

      // Nettoyer le cache si trop grand (garder les 100 derniers)
      if (ValidationAgent.validationCache.size > 100) {
        const firstKey = ValidationAgent.validationCache.keys().next().value;
        if (firstKey) {
          ValidationAgent.validationCache.delete(firstKey);
        }
      }

      return validation;
    } catch (error) {
      console.error('Error validating travel request:', error);

      // Fallback validation optimiste
      const fallbackResult = {
        confidence: 0.7, // Optimiste mais pas trop
        hasDestination: true,
        hasTimeframe: false,
        extractedInfo: {},
        missingCritical: [],
        reasoning: 'Validation failed, using optimistic approach',
        shouldProceed: true,
      };

      // Mettre en cache même le fallback
      ValidationAgent.validationCache.set(cacheKey, fallbackResult);
      return fallbackResult;
    } finally {
      // Toujours nettoyer le verrou
      ValidationAgent.isValidating.delete(cacheKey);
    }
  }

  /**
   * Generate clarifying questions based on missing information using intelligent LLM
   */
  async generateClarifyingQuestions(
    query: string,
    missingInfo: string[],
  ): Promise<ClarifyingQuestion> {
    try {
      const { object: question } = await generateObject({
        model: this.model,
        system: `You are a helpful travel assistant. Generate natural, friendly clarifying questions to gather missing travel information. Always respond in the SAME language as the original query.`,
        prompt: `Original query: "${query}"
Missing information: ${missingInfo.join(', ')}

Generate a helpful clarifying question to gather the missing information.`,
        schema: z.object({
          question: z.string().describe('The clarifying question to ask'),
          type: z
            .enum(['destination', 'duration', 'both'])
            .describe('Type of information being requested'),
          suggestions: z
            .array(z.string())
            .optional()
            .describe('Optional helpful suggestions for the user'),
        }),
        temperature: 0.3, // Un peu de créativité pour les questions
        maxTokens: 300, // Limite pour les questions
      });

      return question;
    } catch (error) {
      console.error('Error generating clarifying questions:', error);

      // Fallback intelligent multilingue simple
      const lowerQuery = query.toLowerCase();

      // Détection de langue pour le fallback
      if (/\b(je|veux|partir|aller|visiter|voyage)\b/i.test(lowerQuery)) {
        // Français
        return {
          question:
            'Pour vous aider à créer le meilleur guide de voyage, pouvez-vous me dire où vous souhaitez aller et pour combien de temps ?',
          type: 'both' as const,
          suggestions: ['Exemple: "Paris 3 jours" ou "Tokyo 2 semaines"'],
        };
      } else if (/\b(quiero|viajar|ir|visitar)\b/i.test(lowerQuery)) {
        // Espagnol
        return {
          question:
            '¿Podrías decirme a dónde quieres viajar y por cuánto tiempo?',
          type: 'both' as const,
          suggestions: ['Ejemplo: "París 3 días" o "Tokio 2 semanas"'],
        };
      } else {
        // Anglais par défaut
        return {
          question:
            'To help create the best travel guide for you, could you tell me where you want to go and for how long?',
          type: 'both' as const,
          suggestions: ['Example: "Paris 3 days" or "Tokyo 2 weeks"'],
        };
      }
    }
  }
}
