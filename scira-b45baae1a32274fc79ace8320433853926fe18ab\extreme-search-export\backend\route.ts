// route.ts
// -----------------------------------
// Exemple de route API Next.js (app/api/extreme-search/route.ts) qui utilise extremeSearchTool.
// Copie ce fichier dans ton dossier `app/api/extreme-search/route.ts` de ton projet Next.js.

import { NextResponse } from 'next/server';
import { extremeSearchTool } from './extreme-search';

export const runtime = 'nodejs';

export async function POST(req: Request) {
  const { prompt } = await req.json();

  // Crée un TransformStream pour le streaming
  const stream = new TransformStream();
  const writer = stream.writable.getWriter();
  const encoder = new TextEncoder();

  // Envoie une réponse immédiate avec le stream
  const response = new NextResponse(stream.readable, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  });

  // Exécute extremeSearchTool en arrière-plan
  (async () => {
    try {
      const dataStream = {
        writeMessageAnnotation: async (annotation: any) => {
          // Envoie chaque annotation au client via SSE
          await writer.write(
            encoder.write(
              new TextEncoder().encode(`data: ${JSON.stringify(annotation)}\n\n`)
            )
          );
        },
      };

      // Lance la recherche
      await extremeSearchTool(dataStream).execute({ prompt });
    } catch (error) {
      console.error('Erreur dans extremeSearchTool:', error);
    } finally {
      await writer.close();
    }
  })();

  return response;
}
