import ReactMarkdown from 'react-markdown';
import { exampleSetup } from 'prosemirror-example-setup';
import { inputRules } from 'prosemirror-inputrules';
import { EditorState } from 'prosemirror-state';
import { EditorView } from 'prosemirror-view';
import React, { memo, useEffect, useRef } from 'react';

import type { Suggestion } from '@/lib/db/schema';
import {
  documentSchema,
  handleTransaction,
  headingRule,
} from '@/lib/editor/config';
import {
  buildContentFromDocument,
  buildDocumentFromContent,
  createDecorations,
} from '@/lib/editor/functions';
import {
  projectWithPositions,
  suggestionsPlugin,
  suggestionsPluginKey,
} from '@/lib/editor/suggestions';
import { MarkdownRenderer } from './markdown-renderer';

type EditorProps = {
  content: string;
  onSaveContent: (updatedContent: string, debounce: boolean) => void;
  status: 'streaming' | 'idle';
  isCurrentVersion: boolean;
  currentVersionIndex: number;
  suggestions: Array<Suggestion>;
};

function PureEditor({
  content,
  onSaveContent,
  suggestions,
  status,
  isCurrentVersion,
  currentVersionIndex,
  isEditing = false, // Par défaut, mode affichage
}: EditorProps & { isEditing?: boolean }) {
  const containerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<EditorView | null>(null);

  // Initialiser l'éditeur ProseMirror uniquement en mode édition
  useEffect(() => {
    if (isEditing && containerRef.current && !editorRef.current) {
      console.log("Initialisation de l'éditeur en mode édition");
      const state = EditorState.create({
        doc: buildDocumentFromContent(content),
        plugins: [
          ...exampleSetup({ schema: documentSchema, menuBar: false }),
          inputRules({
            rules: [
              headingRule(1),
              headingRule(2),
              headingRule(3),
              headingRule(4),
              headingRule(5),
              headingRule(6),
            ],
          }),
          suggestionsPlugin,
        ],
      });

      editorRef.current = new EditorView(containerRef.current, {
        state,
        dispatchTransaction: (transaction) => {
          handleTransaction({
            transaction,
            editorRef,
            onSaveContent,
          });
        },
      });
    }

    return () => {
      if (editorRef.current) {
        editorRef.current.destroy();
        editorRef.current = null;
      }
    };
  }, [isEditing, content, onSaveContent]);

  // Mettre à jour le contenu de l'éditeur quand le contenu change
  useEffect(() => {
    if (isEditing && editorRef.current && content) {
      const currentContent = buildContentFromDocument(
        editorRef.current.state.doc,
      );

      if (currentContent !== content && status !== 'streaming') {
        const newState = EditorState.create({
          doc: buildDocumentFromContent(content),
          plugins: editorRef.current.state.plugins,
        });

        editorRef.current.updateState(newState);
      }
    }
  }, [content, status, isEditing, onSaveContent]);

  // Appliquer les suggestions
  useEffect(() => {
    if (
      isEditing &&
      editorRef.current?.state.doc &&
      suggestions &&
      suggestions.length > 0
    ) {
      console.log('Application des suggestions:', suggestions);

      try {
        const projectedSuggestions = projectWithPositions(
          editorRef.current.state.doc,
          suggestions,
        ).filter(
          (suggestion) => suggestion.selectionStart && suggestion.selectionEnd,
        );

        console.log('Suggestions projetées:', projectedSuggestions);

        const decorations = createDecorations(
          projectedSuggestions,
          editorRef.current,
        );

        const transaction = editorRef.current.state.tr;
        transaction.setMeta(suggestionsPluginKey, { decorations });
        editorRef.current.dispatch(transaction);
      } catch (error) {
        console.error("Erreur lors de l'application des suggestions:", error);
      }
    }
  }, [suggestions, content, isEditing]);

  // Fonction pour rendre les URLs cliquables
  const renderMarkdownWithClickableLinks = (text: string) => {
    return (
      <ReactMarkdown
        components={{
          a: ({ node, ...props }) => (
            <a
              {...props}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:underline"
            />
          ),
        }}
      >
        {text}
      </ReactMarkdown>
    );
  };

  return (
    <div className="prose dark:prose-invert max-w-none">
      {isEditing ? (
        <div ref={containerRef} className="editor-container" />
      ) : (
        <MarkdownRenderer content={content} />
      )}
    </div>
  );
}

function areEqual(prevProps: EditorProps, nextProps: EditorProps) {
  return (
    prevProps.suggestions === nextProps.suggestions &&
    prevProps.currentVersionIndex === nextProps.currentVersionIndex &&
    prevProps.isCurrentVersion === nextProps.isCurrentVersion &&
    !(prevProps.status === 'streaming' && nextProps.status === 'streaming') &&
    prevProps.content === nextProps.content &&
    prevProps.onSaveContent === nextProps.onSaveContent
  );
}

export const Editor = memo(PureEditor, areEqual);
