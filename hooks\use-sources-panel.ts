'use client';

import useSWR from 'swr';
import { useCallback, useMemo } from 'react';

export interface Source {
  url: string;
  title: string;
  content?: string;
}

interface SourcesPanelState {
  isOpen: boolean;
  sources: Source[];
}

const initialState: SourcesPanelState = {
  isOpen: false,
  sources: [],
};

export function useSourcesPanel() {
  const { data: state, mutate: setState } = useSWR<SourcesPanelState>(
    'sources-panel',
    null,
    {
      fallbackData: initialState,
    },
  );

  // Create a safe state that always has a value
  const safeState = useMemo(() => {
    return state || initialState;
  }, [state]);

  const setIsOpen = useCallback(
    (isOpen: boolean) => {
      setState((currentState) => ({
        ...(currentState || initialState),
        isOpen,
      }));
    },
    [setState],
  );

  const togglePanel = useCallback(() => {
    setState((currentState) => {
      const current = currentState || initialState;
      return {
        ...current,
        isOpen: !current.isOpen,
      };
    });
  }, [setState]);

  const setSources = useCallback(
    (sources: Source[]) => {
      setState((currentState) => ({
        ...(currentState || initialState),
        sources,
      }));
    },
    [setState],
  );

  const clearSources = useCallback(() => {
    setState((currentState) => ({
      ...(currentState || initialState),
      sources: [],
    }));
  }, [setState]);

  return useMemo(
    () => ({
      isOpen: safeState.isOpen,
      sources: safeState.sources,
      setIsOpen,
      togglePanel,
      setSources,
      clearSources,
    }),
    [safeState, setIsOpen, togglePanel, setSources, clearSources],
  );
}
