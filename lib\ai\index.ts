// Import tools
import { getDocument } from './tools/get-document';
import { showMarketTrending } from './tools/get-market-trending';
import { getStockFinancials } from './tools/get-stock-financials';
import { getStockChart } from './tools/get-stock-chart';
import { getStockNews } from './tools/get-stock-news';
import { getStockScreener } from './tools/get-stock-screener';
import { getStockPrice } from './tools/get-stock-price';
import { getHeatmapsMarket } from './tools/get-heatmaps-market';
import { getCryptoCoinsHeatmap } from './tools/get-crypto-coins-heatmap';
import { getETFHeatmap } from './tools/get-etf-heatmap';
import { getForexCrossRates } from './tools/get-forex-cross-rates';
import { getForexHeatmap } from './tools/get-forex-heatmap';
import { getCryptocurrencyMarket } from './tools/get-cryptocurrency-market';
import { getSymbolInfo } from './tools/get-symbol-info';
import { getTechnicalAnalysis } from './tools/get-technical-analysis';
import { getCompanyProfile } from './tools/get-company-profile';
import { getEconomicCalendar } from './tools/get-economic-calendar';
import { getWeather } from './tools/get-weather';

// Import workflows
import * as workflows from './workflows';

// Add tools to the available tools list
export const tools = [
  getDocument,
  showMarketTrending,
  getStockChart,
  getStockNews,
  getStockScreener,
  getStockPrice,
  getHeatmapsMarket,
  getCryptoCoinsHeatmap,
  getETFHeatmap,
  getForexCrossRates,
  getForexHeatmap,
  getCryptocurrencyMarket,
  getSymbolInfo,
  getTechnicalAnalysis,
  getCompanyProfile,
  getEconomicCalendar,
  getWeather,
  getStockFinancials,
];

// Export workflows
export { workflows };
