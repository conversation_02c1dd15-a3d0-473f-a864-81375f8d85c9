// Modifier le composant CodeBlock pour qu'il ne soit pas rendu à l'intérieur d'un <p>
const CodeBlock = ({
  language,
  value,
}: { language: string; value: string }) => {
  return (
    <div className="not-prose relative my-4">
      <pre className="overflow-x-auto rounded-lg bg-zinc-950 py-4 dark:bg-zinc-900">
        <code className={`language-${language}`}>{value}</code>
      </pre>
    </div>
  );
};

// Dans les composants Markdown, assurez-vous que code est traité correctement
const components = {
  code({ node, inline, className, children, ...props }: any) {
    const match = /language-(\w+)/.exec(className || '');

    if (!inline && match) {
      // Render code blocks directly, not inside paragraphs
      return (
        <div className="not-prose relative my-4">
          <pre className="overflow-x-auto rounded-lg bg-zinc-950 py-4 dark:bg-zinc-900">
            <code className={`language-${match[1]}`}>
              {String(children).replace(/\n$/, '')}
            </code>
          </pre>
        </div>
      );
    }

    // Pour le code inline, on garde le comportement normal
    return (
      <code className={className} {...props}>
        {children}
      </code>
    );
  },
  // Completely override the paragraph component to handle code blocks
  p({ children, ...props }: any) {
    // Check if children contains any pre elements or CodeBlock components
    const childArray = React.Children.toArray(children);

    for (let i = 0; i < childArray.length; i++) {
      const child = childArray[i];
      // If we find a pre tag or a div that might contain a pre tag, render children without p wrapper
      if (
        React.isValidElement(child) &&
        (child.type === 'pre' ||
          child.type === CodeBlock ||
          (React.isValidElement(child) &&
            child.props?.className?.includes('not-prose')))
      ) {
        return <>{children}</>;
      }
    }

    // No block elements found, render as normal paragraph
    return (
      <p className="mb-4 last:mb-0" {...props}>
        {children}
      </p>
    );
  },
  // Add a pre component handler to ensure it's never inside a p
  pre(props: any) {
    // Ensure pre is rendered at the root level
    return <pre {...props} />;
  },
  link({ node, href, children, ...props }: any) {
    return (
      <a
        href={href}
        className="text-blue-500 hover:underline"
        target="_blank"
        rel="noopener noreferrer"
        {...props}
      >
        {children}
      </a>
    );
  },
};
