'use client';

import React from 'react';
import { useFloatingWidgets, generateWidgetId, WIDGET_CONFIGS } from '@/components/FloatingWidgetManager';
import ETFHeatmap from '@/components/ETFHeatmap';
import ForexCrossRates from '@/components/ForexCrossRates';
import CryptocurrencyMarket from '@/components/CryptocurrencyMarket';
import EconomicCalendar from '@/components/EconomicCalendar';
import SymbolInfo from '@/components/SymbolInfo';
import TechnicalAnalysis from '@/components/TechnicalAnalysis';

export default function TestFloatingWidgets() {
  const { openWidget } = useFloatingWidgets();

  const openETFHeatmap = () => {
    openWidget({
      id: generateWidgetId('etf-heatmap'),
      title: 'ETF Heatmap',
      component: <ETFHeatmap />,
      position: WIDGET_CONFIGS.etfHeatmap.position,
      size: WIDGET_CONFIGS.etfHeatmap.size,
    });
  };

  const openForexCrossRates = () => {
    openWidget({
      id: generateWidgetId('forex-cross-rates'),
      title: 'Forex Cross Rates',
      component: <ForexCrossRates />,
      position: WIDGET_CONFIGS.forexCrossRates.position,
      size: WIDGET_CONFIGS.forexCrossRates.size,
    });
  };

  const openCryptoMarket = () => {
    openWidget({
      id: generateWidgetId('crypto-market'),
      title: 'Cryptocurrency Market',
      component: <CryptocurrencyMarket />,
      position: WIDGET_CONFIGS.cryptoMarket.position,
      size: WIDGET_CONFIGS.cryptoMarket.size,
    });
  };

  const openEconomicCalendar = () => {
    openWidget({
      id: generateWidgetId('economic-calendar'),
      title: 'Economic Calendar',
      component: <EconomicCalendar />,
      position: WIDGET_CONFIGS.economicCalendar.position,
      size: WIDGET_CONFIGS.economicCalendar.size,
    });
  };

  const openSymbolInfo = () => {
    openWidget({
      id: generateWidgetId('symbol-info', 'AAPL'),
      title: 'Symbol Info - AAPL',
      component: <SymbolInfo symbol="NASDAQ:AAPL" />,
      position: WIDGET_CONFIGS.symbolInfo.position,
      size: WIDGET_CONFIGS.symbolInfo.size,
    });
  };

  const openTechnicalAnalysis = () => {
    openWidget({
      id: generateWidgetId('technical-analysis', 'AAPL'),
      title: 'Technical Analysis - AAPL',
      component: <TechnicalAnalysis symbol="NASDAQ:AAPL" interval="1D" />,
      position: WIDGET_CONFIGS.technicalAnalysis.position,
      size: WIDGET_CONFIGS.technicalAnalysis.size,
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Test des Widgets Flottants TradingView
        </h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Widgets Statiques
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button
              onClick={openETFHeatmap}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              ETF Heatmap
            </button>
            
            <button
              onClick={openForexCrossRates}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            >
              Forex Cross Rates
            </button>
            
            <button
              onClick={openCryptoMarket}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
            >
              Crypto Market
            </button>
            
            <button
              onClick={openEconomicCalendar}
              className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
            >
              Economic Calendar
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Widgets Paramétrables
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={openSymbolInfo}
              className="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 transition-colors"
            >
              Symbol Info (AAPL)
            </button>
            
            <button
              onClick={openTechnicalAnalysis}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
            >
              Technical Analysis (AAPL)
            </button>
          </div>
        </div>

        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-800 mb-2">
            Instructions d'utilisation
          </h3>
          <ul className="text-blue-700 space-y-1">
            <li>• Cliquez sur les boutons pour ouvrir des widgets flottants</li>
            <li>• Glissez-déposez les widgets par leur barre de titre</li>
            <li>• Redimensionnez en tirant le coin inférieur droit</li>
            <li>• Minimisez, maximisez ou fermez avec les boutons de contrôle</li>
            <li>• Cliquez sur un widget pour le mettre au premier plan</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
