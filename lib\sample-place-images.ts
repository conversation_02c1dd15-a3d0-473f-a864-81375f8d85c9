import type { PlaceImage } from './types';

// Sample images for different types of places
export const generateSampleImages = (placeTitle: string, category: string): PlaceImage[] => {
  const baseImages: Record<string, PlaceImage[]> = {
    restaurant: [
      {
        url: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=800&h=600&fit=crop',
        description: 'Restaurant interior',
        thumbnail: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=150&h=150&fit=crop',
        width: 800,
        height: 600
      },
      {
        url: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=800&h=600&fit=crop',
        description: 'Delicious food',
        thumbnail: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=150&h=150&fit=crop',
        width: 800,
        height: 600
      },
      {
        url: 'https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=800&h=600&fit=crop',
        description: 'Restaurant atmosphere',
        thumbnail: 'https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=150&h=150&fit=crop',
        width: 800,
        height: 600
      }
    ],
    hotel: [
      {
        url: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&h=600&fit=crop',
        description: 'Hotel lobby',
        thumbnail: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=150&h=150&fit=crop',
        width: 800,
        height: 600
      },
      {
        url: 'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop',
        description: 'Hotel room',
        thumbnail: 'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=150&h=150&fit=crop',
        width: 800,
        height: 600
      }
    ],
    attraction: [
      {
        url: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800&h=600&fit=crop',
        description: 'Tourist attraction',
        thumbnail: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=150&h=150&fit=crop',
        width: 800,
        height: 600
      },
      {
        url: 'https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?w=800&h=600&fit=crop',
        description: 'Scenic view',
        thumbnail: 'https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?w=150&h=150&fit=crop',
        width: 800,
        height: 600
      }
    ],
    shop: [
      {
        url: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop',
        description: 'Shop interior',
        thumbnail: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=150&h=150&fit=crop',
        width: 800,
        height: 600
      }
    ],
    default: [
      {
        url: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop',
        description: 'Building exterior',
        thumbnail: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=150&h=150&fit=crop',
        width: 800,
        height: 600
      },
      {
        url: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop',
        description: 'City view',
        thumbnail: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=150&h=150&fit=crop',
        width: 800,
        height: 600
      }
    ]
  };

  // Determine category key
  const categoryKey = category.toLowerCase();
  let selectedImages: PlaceImage[] = [];

  if (categoryKey.includes('restaurant') || categoryKey.includes('food') || categoryKey.includes('cafe')) {
    selectedImages = baseImages.restaurant;
  } else if (categoryKey.includes('hotel') || categoryKey.includes('accommodation')) {
    selectedImages = baseImages.hotel;
  } else if (categoryKey.includes('attraction') || categoryKey.includes('museum') || categoryKey.includes('park')) {
    selectedImages = baseImages.attraction;
  } else if (categoryKey.includes('shop') || categoryKey.includes('store') || categoryKey.includes('mall')) {
    selectedImages = baseImages.shop;
  } else {
    selectedImages = baseImages.default;
  }

  // Add place-specific description
  return selectedImages.map((image, index) => ({
    ...image,
    description: `${placeTitle} - ${image.description} ${index + 1}`
  }));
};

// Function to add sample images to places that don't have them
export const addSampleImagesToPlaces = (places: any[]): any[] => {
  return places.map(place => ({
    ...place,
    images: place.images || generateSampleImages(place.title, place.category)
  }));
};
