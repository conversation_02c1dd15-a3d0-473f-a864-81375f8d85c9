import React from 'react';
import { Card, CardContent, CardHeader } from './ui/card';
import { ExternalLink } from 'lucide-react';
import { Button } from './ui/button';

export type Source = {
  title: string;
  url: string;
  favicon?: string | null;
  published_date?: string;
};

interface ExtremeSourceCardProps {
  source: Omit<Source, 'content'> & {
    favicon?: string | null;
    published_date?: string;
  };
  content?: string;
  className?: string;
  onClick?: () => void;
}

export function ExtremeSourceCard({ source, content, className = '' }: ExtremeSourceCardProps) {
  const favicon = source.favicon || `https://www.google.com/s2/favicons?sz=64&domain=${new URL(source.url).hostname}`;
  
  return (
    <Card className={`overflow-hidden hover:shadow-md transition-shadow ${className}`}>
      <CardHeader className="p-4 pb-2">
        <div className="flex items-center gap-3">
          {favicon && (
            <div className="w-5 h-5 flex-shrink-0">
              <img 
                src={favicon} 
                alt="" 
                className="w-full h-full object-contain"
                onError={(e) => {
                  // Fallback to a generic icon if favicon fails to load
                  const target = e.target as HTMLImageElement;
                  target.onerror = null;
                  target.src = '/icons/globe.svg';
                }}
              />
            </div>
          )}
          <h3 className="text-sm font-medium leading-tight line-clamp-2">
            {source.title || new URL(source.url).hostname.replace('www.', '')}
          </h3>
        </div>
      </CardHeader>
      
      {content && (
        <CardContent className="p-4 pt-0">
          <p className="text-sm text-muted-foreground line-clamp-3">
            {content}
          </p>
        </CardContent>
      )}
      
      <div className="px-4 pb-3 pt-1">
        <div className="flex items-center justify-between">
          <div className="flex flex-col">
            <span className="text-xs text-muted-foreground truncate max-w-[80%]">
              {new URL(source.url).hostname}
            </span>
            {source.published_date && (
              <span className="text-[10px] text-muted-foreground/70">
                {new Date(source.published_date).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric',
                })}
              </span>
            )}
          </div>
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-7 w-7" 
            asChild
            onClick={(e) => {
              e.stopPropagation();
              window.open(source.url, '_blank', 'noopener,noreferrer');
            }}
          >
            <span className="cursor-pointer">
              <ExternalLink className="h-3.5 w-3.5" />
              <span className="sr-only">Open in new tab</span>
            </span>
          </Button>
        </div>
      </div>
    </Card>
  );
}

export default ExtremeSourceCard;
