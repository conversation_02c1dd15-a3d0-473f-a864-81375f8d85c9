'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import FloatingWidget from './FloatingWidget';

interface FloatingWidgetData {
  id: string;
  title: string;
  component: ReactNode;
  position: { x: number; y: number };
  size: { width: number; height: number };
  zIndex: number;
}

interface FloatingWidgetContextType {
  widgets: FloatingWidgetData[];
  openWidget: (widget: Omit<FloatingWidgetData, 'zIndex'>) => void;
  closeWidget: (id: string) => void;
  focusWidget: (id: string) => void;
  minimizeWidget: (id: string) => void;
}

const FloatingWidgetContext = createContext<
  FloatingWidgetContextType | undefined
>(undefined);

export const useFloatingWidgets = () => {
  const context = useContext(FloatingWidgetContext);
  if (!context) {
    throw new Error(
      'useFloatingWidgets must be used within a FloatingWidgetProvider',
    );
  }
  return context;
};

interface FloatingWidgetProviderProps {
  children: ReactNode;
}

export function FloatingWidgetProvider({
  children,
}: FloatingWidgetProviderProps) {
  const [widgets, setWidgets] = useState<FloatingWidgetData[]>([]);
  const [nextZIndex, setNextZIndex] = useState(1000);

  const openWidget = (widget: Omit<FloatingWidgetData, 'zIndex'>) => {
    // Check if widget with same ID already exists
    const existingWidget = widgets.find((w) => w.id === widget.id);
    if (existingWidget) {
      // Focus existing widget instead of creating new one
      focusWidget(widget.id);
      return;
    }

    // Calculer la position intelligente basée sur les widgets existants
    const smartPosition = calculateSmartPosition(widgets);

    const newWidget: FloatingWidgetData = {
      ...widget,
      position: smartPosition, // Utiliser la position calculée intelligemment
      zIndex: nextZIndex,
    };

    setWidgets((prev) => [...prev, newWidget]);
    setNextZIndex((prev) => prev + 1);
  };

  const closeWidget = (id: string) => {
    setWidgets((prev) => prev.filter((widget) => widget.id !== id));
  };

  const focusWidget = (id: string) => {
    setWidgets((prev) =>
      prev.map((widget) =>
        widget.id === id ? { ...widget, zIndex: nextZIndex } : widget,
      ),
    );
    setNextZIndex((prev) => prev + 1);
  };

  const minimizeWidget = (id: string) => {
    // For now, just focus the widget. Could add minimize state later
    focusWidget(id);
  };

  return (
    <FloatingWidgetContext.Provider
      value={{
        widgets,
        openWidget,
        closeWidget,
        focusWidget,
        minimizeWidget,
      }}
    >
      {children}

      {/* Render floating widgets */}
      {widgets.map((widget) => (
        <FloatingWidget
          key={widget.id}
          id={widget.id}
          title={widget.title}
          initialPosition={widget.position}
          initialSize={widget.size}
          zIndex={widget.zIndex}
          onClose={() => closeWidget(widget.id)}
          onMinimize={() => minimizeWidget(widget.id)}
          onFocus={() => focusWidget(widget.id)}
        >
          {widget.component}
        </FloatingWidget>
      ))}
    </FloatingWidgetContext.Provider>
  );
}

// Helper function to generate widget IDs
export const generateWidgetId = (type: string, symbol?: string) => {
  const timestamp = Date.now();
  return symbol ? `${type}-${symbol}-${timestamp}` : `${type}-${timestamp}`;
};

// Tailles compactes similaires à YouTube (plus petites)
const COMPACT_SIZES = {
  small: { width: 320, height: 240 }, // Comme YouTube mobile
  medium: { width: 400, height: 300 }, // Taille intermédiaire
  large: { width: 480, height: 360 }, // Plus grand mais compact
};

// Fonction pour calculer la position intelligente
export const calculateSmartPosition = (
  existingWidgets: FloatingWidgetData[],
): { x: number; y: number } => {
  const MARGIN = 20;
  const WIDGET_WIDTH = 400; // Largeur moyenne
  const WIDGET_HEIGHT = 300; // Hauteur moyenne

  // Positions préférées (côtés de l'écran)
  const preferredPositions = [
    // Côté droit (positions principales)
    { x: window.innerWidth - WIDGET_WIDTH - MARGIN, y: MARGIN },
    {
      x: window.innerWidth - WIDGET_WIDTH - MARGIN,
      y: MARGIN + WIDGET_HEIGHT + 20,
    },
    {
      x: window.innerWidth - WIDGET_WIDTH - MARGIN,
      y: MARGIN + (WIDGET_HEIGHT + 20) * 2,
    },

    // Côté gauche (positions secondaires)
    { x: MARGIN, y: MARGIN },
    { x: MARGIN, y: MARGIN + WIDGET_HEIGHT + 20 },
    { x: MARGIN, y: MARGIN + (WIDGET_HEIGHT + 20) * 2 },

    // Centre-droit (positions tertiaires)
    { x: window.innerWidth - WIDGET_WIDTH * 2 - MARGIN * 2, y: MARGIN },
    {
      x: window.innerWidth - WIDGET_WIDTH * 2 - MARGIN * 2,
      y: MARGIN + WIDGET_HEIGHT + 20,
    },
  ];

  // Vérifier quelle position est libre
  for (const pos of preferredPositions) {
    const isOccupied = existingWidgets.some((widget) => {
      const distance = Math.sqrt(
        Math.pow(widget.position.x - pos.x, 2) +
          Math.pow(widget.position.y - pos.y, 2),
      );
      return distance < 100; // Seuil de proximité
    });

    if (!isOccupied) {
      return pos;
    }
  }

  // Si toutes les positions sont occupées, utiliser une position en cascade
  const cascadeOffset = existingWidgets.length * 30;
  return {
    x: Math.min(
      window.innerWidth - WIDGET_WIDTH - MARGIN,
      MARGIN + cascadeOffset,
    ),
    y: Math.min(
      window.innerHeight - WIDGET_HEIGHT - MARGIN,
      MARGIN + cascadeOffset,
    ),
  };
};

// Predefined widget configurations avec tailles compactes
export const WIDGET_CONFIGS = {
  etfHeatmap: {
    title: 'ETF Heatmap',
    size: COMPACT_SIZES.large,
    position: { x: 100, y: 100 }, // Sera recalculé dynamiquement
  },
  forexCrossRates: {
    title: 'Forex Cross Rates',
    size: COMPACT_SIZES.medium,
    position: { x: 150, y: 150 },
  },
  forexHeatmap: {
    title: 'Forex Heatmap',
    size: COMPACT_SIZES.medium,
    position: { x: 200, y: 200 },
  },
  cryptoMarket: {
    title: 'Cryptocurrency Market',
    size: COMPACT_SIZES.large,
    position: { x: 120, y: 120 },
  },
  economicCalendar: {
    title: 'Economic Calendar',
    size: COMPACT_SIZES.large,
    position: { x: 180, y: 180 },
  },
  symbolInfo: {
    title: 'Symbol Information',
    size: COMPACT_SIZES.small,
    position: { x: 250, y: 250 },
  },
  technicalAnalysis: {
    title: 'Technical Analysis',
    size: COMPACT_SIZES.medium,
    position: { x: 300, y: 300 },
  },
  companyProfile: {
    title: 'Company Profile',
    size: COMPACT_SIZES.medium,
    position: { x: 350, y: 350 },
  },
  stockScreener: {
    title: 'Stock Screener',
    size: COMPACT_SIZES.large,
    position: { x: 400, y: 400 },
  },
  stockFinancials: {
    title: 'Stock Financials',
    size: COMPACT_SIZES.medium,
    position: { x: 450, y: 450 },
  },
  stockPrice: {
    title: 'Stock Price',
    size: COMPACT_SIZES.medium,
    position: { x: 500, y: 500 },
  },
  marketTrending: {
    title: 'Market Trending',
    size: COMPACT_SIZES.large,
    position: { x: 550, y: 550 },
  },
  financialChart: {
    title: 'Financial Chart',
    size: COMPACT_SIZES.large,
    position: { x: 600, y: 600 },
  },
  stockNews: {
    title: 'Stock News',
    size: COMPACT_SIZES.medium,
    position: { x: 650, y: 650 },
  },
};
