import { MemoryClient } from 'mem0ai';

// Fonction pour récupérer le contenu du document depuis la mémoire
export async function getDocumentContentFromMemory(documentId: string) {
  const apiKey = process.env.MEM0_API_KEY;
  if (!apiKey) return null;

  try {
    const memoryClient = new MemoryClient({ apiKey });

    // Ajouter des tentatives de récupération avec backoff exponentiel
    let attempts = 0;
    const maxAttempts = 3;

    while (attempts < maxAttempts) {
      try {
        // Rechercher les souvenirs liés à ce document avec le flag isDocumentContent
        const memories = await memoryClient.search(
          `documentId:${documentId} AND isDocumentContent:true`,
          {
            org_id: process.env.MEM0_ORG_ID,
            project_id: process.env.MEM0_PROJECT_ID,
            user_id: 'system', // Utiliser 'system' comme ID utilisateur par défaut
            limit: 1,
          },
        );

        if (memories && memories.length > 0 && memories[0].memory) {
          console.log(`Document content retrieved from memory: ${documentId}`);
          return memories[0].memory;
        }

        attempts++;
        if (attempts < maxAttempts) {
          // Attendre avant de réessayer (backoff exponentiel)
          const delay = Math.min(1000 * Math.pow(2, attempts), 10000);
          console.log(
            `Document not found in memory, retrying in ${delay}ms...`,
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      } catch (error) {
        console.error(`Error retrieving document from memory:`, error);
        attempts++;
        if (attempts < maxAttempts) {
          const delay = Math.min(1000 * Math.pow(2, attempts), 10000);
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }

    console.error(
      `Failed to retrieve document from memory after ${maxAttempts} attempts`,
    );
    return null;
  } catch (error) {
    console.error(`Failed to retrieve document from memory:`, error);
    return null;
  }
}
