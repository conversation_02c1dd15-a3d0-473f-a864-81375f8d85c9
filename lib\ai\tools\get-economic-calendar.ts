import { z } from 'zod';
import { tool } from 'ai';

// Define and export the tool configuration
export const getEconomicCalendar = tool({
  description:
    'Display an economic calendar showing upcoming economic events, announcements, and data releases from major countries including US, EU, UK, Japan, China, and others. Shows importance levels and impact on financial markets.',
  parameters: z.object({
    // No parameters needed for the economic calendar as it shows a predefined view
  }),
  execute: async () => {
    // Return a simple object that will be used by the frontend to render the component
    return {
      type: 'economic_calendar',
      data: {},
    };
  },
});
