.clip-yt-img {
  object-fit: cover;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.yt-thumbnail-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.yt-thumbnail-container:hover .clip-yt-img {
  transform: scale(1.05);
}

.yt-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.yt-play-button:hover {
  background-color: rgba(255, 0, 0, 0.8);
  transform: translate(-50%, -50%) scale(1.1);
}

.yt-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 4px;
  font-size: 9px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  transition: background-color 0.3s ease;
  max-height: 30%;
}

.yt-thumbnail-container:hover .yt-title {
  background-color: rgba(0, 0, 0, 0.85);
}

/* Styles pour le carrousel */
.videos-grid-container {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 8px;
}

/* Styles pour la grille de vidéos */
.video-item {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  transition: transform 0.2s ease;
}

.video-item:hover {
  transform: scale(1.02);
}

/* Styles pour les cartes vidéo */
.video-card {
  transition: all 0.3s ease;
  transform-origin: center;
}

.video-card:hover {
  transform: translateY(-2px);
}

/* Styles pour le lecteur vidéo déplaçable */
.draggable-video-player {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  overflow: hidden;
  touch-action: none;
  transition: box-shadow 0.2s ease;
  transform: translate3d(0, 0, 0);
  will-change: transform;
}

.draggable-video-player:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.drag-handle {
  cursor: grab;
  z-index: 10;
  padding-top: 2px;
}

.drag-handle:active {
  cursor: grabbing;
}

.drag-handle button {
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  transition: background-color 0.2s ease;
}

.drag-handle button:hover {
  background-color: rgba(255, 0, 0, 0.5);
}

@media (max-width: 768px) {
  .draggable-video-player {
    width: 180px !important;
    height: 100px !important;
  }
}
