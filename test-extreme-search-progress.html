<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Extreme Search Progress</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .progress-demo {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e5e5e5;
            border-radius: 8px;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e5e5;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(to right, #059669, #0891b2);
            transition: width 0.5s ease;
        }
        .stage-icons {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
        }
        .stage {
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            min-width: 80px;
        }
        .stage.active {
            background: #dcfce7;
            color: #166534;
        }
        .stage.inactive {
            background: #f3f4f6;
            color: #6b7280;
        }
        .message {
            font-size: 18px;
            font-weight: 500;
            color: #374151;
            text-align: center;
            margin: 15px 0;
        }
        .details {
            background: #f0fdf4;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .details h4 {
            color: #166534;
            margin: 0 0 10px 0;
        }
        .details p {
            color: #15803d;
            margin: 5px 0;
            font-size: 14px;
        }
        button {
            background: #059669;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #047857;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Extreme Search Progress</h1>
        <p>Démonstration du système de progression pour Extreme Search</p>

        <div class="progress-demo">
            <div class="message" id="message">Initializing extreme search...</div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 5%"></div>
            </div>
            
            <div style="text-align: center; margin: 10px 0;">
                <span id="progressText">5%</span>
            </div>

            <div class="stage-icons">
                <div class="stage" id="stage1">
                    <div style="font-size: 24px;">🔍</div>
                    <div>Planning</div>
                </div>
                <div class="stage inactive" id="stage2">
                    <div style="font-size: 24px;">🌐</div>
                    <div>Web Search</div>
                </div>
                <div class="stage inactive" id="stage3">
                    <div style="font-size: 24px;">📊</div>
                    <div>Analysis</div>
                </div>
                <div class="stage inactive" id="stage4">
                    <div style="font-size: 24px;">📋</div>
                    <div>Report</div>
                </div>
            </div>

            <div class="details" id="details">
                <h4>Search Details</h4>
                <p id="detailsContent">Preparing search strategy...</p>
            </div>
        </div>

        <div style="text-align: center;">
            <button onclick="simulateProgress()">Simuler Progression</button>
            <button onclick="resetProgress()">Reset</button>
        </div>
    </div>

    <script>
        const progressSteps = [
            { progress: 5, message: "Initializing extreme search...", stage: "initialization", details: "Preparing search strategy..." },
            { progress: 10, message: "Planning comprehensive research strategy...", stage: "planning", details: "Creating research plan..." },
            { progress: 15, message: "Generating detailed research plan...", stage: "plan_generation", details: "Analyzing query requirements..." },
            { progress: 20, message: "Research plan created successfully!", stage: "plan_complete", details: "Plan sections: 4" },
            { progress: 25, message: "Starting research execution...", stage: "research_start", details: "Total queries: 16, Sections: 4" },
            { progress: 35, message: "Researching: Technical Analysis", stage: "section_research", details: "Section 1/4: Technical Analysis" },
            { progress: 45, message: "Searching: \"machine learning algorithms\"", stage: "query_search", details: "Query 3/16: machine learning algorithms" },
            { progress: 55, message: "Researching: Data Processing", stage: "section_research", details: "Section 2/4: Data Processing" },
            { progress: 65, message: "Searching: \"data visualization techniques\"", stage: "query_search", details: "Query 8/16: data visualization techniques" },
            { progress: 75, message: "Analyzing research data...", stage: "data_analysis", details: "Sources found: 24" },
            { progress: 85, message: "Generating comprehensive research report...", stage: "report_generation", details: "Sections completed: 4" },
            { progress: 90, message: "Performing quality review...", stage: "quality_review", details: "Validating sources and citations..." },
            { progress: 100, message: "Research completed successfully!", stage: "complete", details: "Sources: 24, Sections: 4, Report generated: true" }
        ];

        let currentStep = 0;

        function updateProgress(step) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const message = document.getElementById('message');
            const detailsContent = document.getElementById('detailsContent');

            progressFill.style.width = step.progress + '%';
            progressText.textContent = step.progress + '%';
            message.textContent = step.message;
            detailsContent.textContent = step.details;

            // Update stage indicators
            const stages = ['stage1', 'stage2', 'stage3', 'stage4'];
            stages.forEach((stageId, index) => {
                const stage = document.getElementById(stageId);
                const thresholds = [20, 40, 70, 90];
                
                if (step.progress >= thresholds[index]) {
                    stage.className = 'stage active';
                    stage.querySelector('div').textContent = '✓';
                } else {
                    stage.className = 'stage inactive';
                    const icons = ['🔍', '🌐', '📊', '📋'];
                    stage.querySelector('div').textContent = icons[index];
                }
            });
        }

        function simulateProgress() {
            currentStep = 0;
            
            const interval = setInterval(() => {
                if (currentStep < progressSteps.length) {
                    updateProgress(progressSteps[currentStep]);
                    currentStep++;
                } else {
                    clearInterval(interval);
                }
            }, 1000);
        }

        function resetProgress() {
            currentStep = 0;
            updateProgress(progressSteps[0]);
        }

        // Initialize with first step
        updateProgress(progressSteps[0]);
    </script>
</body>
</html>