'use client';

import React, { useState, useEffect } from 'react';
import type { Place } from '@/lib/types';
import MapComponent from './map-component';

interface MapSearchProps {
  query: string;
}

const MapSearch: React.FC<MapSearchProps> = ({ query }) => {
  const [places, setPlaces] = useState<Place[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPlaces = async () => {
      if (!query) {
        console.log('No query provided to MapSearch');
        return;
      }

      console.log('MapSearch: Fetching places for query:', query);
      setLoading(true);
      setError(null);

      try {
        const apiUrl = `/api/map-search?query=${encodeURIComponent(query)}`;
        console.log('MapSearch: Calling API:', apiUrl);

        const response = await fetch(apiUrl);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('MapSearch: API error response:', errorText);
          throw new Error(`Error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('MapSearch: API response data:', data);

        if (data.places && data.places.length > 0) {
          console.log(`MapSearch: Found ${data.places.length} places`);
          setPlaces(data.places);
        } else {
          console.log('MapSearch: No places found in API response');
          // Si aucun lieu n'est trouvé, essayons avec une requête plus simple
          if (query.split(' ').length > 1) {
            const mainKeyword = query.split(' ')[0];
            console.log(
              'MapSearch: Trying with simplified query:',
              mainKeyword,
            );

            const fallbackResponse = await fetch(
              `/api/map-search?query=${encodeURIComponent(mainKeyword)}`,
            );
            if (fallbackResponse.ok) {
              const fallbackData = await fallbackResponse.json();
              if (fallbackData.places && fallbackData.places.length > 0) {
                console.log(
                  `MapSearch: Found ${fallbackData.places.length} places with simplified query`,
                );
                setPlaces(fallbackData.places);
              } else {
                console.log('MapSearch: No places found with simplified query');
                setPlaces([]);
              }
            }
          } else {
            setPlaces([]);
          }
        }
      } catch (err) {
        console.error('MapSearch: Error fetching map data:', err);
        setError('Failed to load map data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchPlaces();
  }, [query]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full size-8 border-y-2 border-blue-500" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded relative mt-4">
        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline"> {error}</span>
      </div>
    );
  }

  if (places.length === 0) {
    return null;
  }

  return <MapComponent places={places} query={query} />;
};

export default MapSearch;
