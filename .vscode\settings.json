{"editor.formatOnSave": true, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "typescript.tsdk": "node_modules/typescript/lib", "eslint.workingDirectories": [{"pattern": "app/*"}, {"pattern": "packages/*"}], "zencoder.enableRepoIndexing": true, "css.validate": false, "scss.validate": false, "less.validate": false, "css.lint.unknownAtRules": "ignore", "typescript.autoClosingTags": false}