'use client';

import { useSourcesPanel } from '../hooks/use-sources-panel';
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { ExternalLink, ChevronDown, ChevronUp, Globe } from 'lucide-react';
import { MagnifyingGlass } from '@phosphor-icons/react';
import { useState } from 'react';

export function SourcesPanel() {
  const { isOpen, sources, setIsOpen } = useSourcesPanel();
  const [expanded, setExpanded] = useState(false);

  // Si pas de sources, ne pas afficher le panneau
  if (sources.length === 0) {
    return null;
  }

  // Limiter à 5 sources quand le panneau est réduit
  const displayedSources = expanded ? sources : sources.slice(0, 5);
  const hasMoreSources = sources.length > 5;

  // Fonction pour extraire le domaine d'une URL
  const extractDomain = (url: string): string => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (e) {
      return url;
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetContent
        side="right"
        className="w-[350px] sm:w-[450px] p-0 overflow-y-auto"
      >
        <SheetHeader className="p-4 border-b">
          <div className="flex items-center gap-2">
            <div className="size-5 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
              <MagnifyingGlass className="size-3 text-blue-500" weight="bold" />
            </div>
            <SheetTitle>Sources</SheetTitle>
          </div>
        </SheetHeader>

        <div className="p-4 space-y-4">
          <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">
            {sources.length} {sources.length > 1 ? 'sources' : 'source'}{' '}
            utilisée{sources.length > 1 ? 's' : ''} pour générer cette réponse
          </div>

          {displayedSources.map((source) => (
            <div
              key={`source-${source.url}`}
              className="flex flex-col gap-2 p-3 rounded-lg border border-gray-100 dark:border-gray-800 bg-gray-50 dark:bg-gray-900/50 hover:bg-gray-100 dark:hover:bg-gray-900 transition-colors"
            >
              <div className="min-w-0 flex-1">
                <a
                  href={source.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium flex items-center gap-1"
                >
                  {source.title || source.url}
                  <ExternalLink size={12} className="inline-block" />
                </a>

                <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 mt-1">
                  <Globe size={12} />
                  <span>{extractDomain(source.url)}</span>
                </div>

                {source.content && (
                  <div className="text-xs text-gray-600 dark:text-gray-400 mt-2 bg-white dark:bg-gray-800 p-2 rounded border border-gray-100 dark:border-gray-700">
                    {source.content}
                  </div>
                )}
              </div>
            </div>
          ))}

          {hasMoreSources && (
            <button
              type="button"
              onClick={() => setExpanded(!expanded)}
              className="flex items-center justify-center gap-1 text-xs text-blue-600 dark:text-blue-400 hover:underline mt-2 w-full py-2 border border-gray-100 dark:border-gray-800 rounded-md"
            >
              {expanded ? (
                <>
                  <ChevronUp size={14} />
                  Afficher moins
                </>
              ) : (
                <>
                  <ChevronDown size={14} />
                  Afficher {sources.length - 5} sources supplémentaires
                </>
              )}
            </button>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
