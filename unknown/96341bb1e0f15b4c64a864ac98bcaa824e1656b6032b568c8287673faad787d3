import { NextResponse } from 'next/server';
import { Groq } from 'groq-sdk';

const createClientPool = (size = 3) => {
  const pool: Groq[] = [];
  for (let i = 0; i < size; i++) {
    pool.push(new Groq({ apiKey: process.env.GROQ_API_KEY }));
  }
  return pool;
};

const clientPool = createClientPool();
let currentClientIndex = 0;

function getNextClient() {
  const client = clientPool[currentClientIndex];
  currentClientIndex = (currentClientIndex + 1) % clientPool.length;
  return client;
}

async function transcribeAudio(
  audioBlob: Blob,
): Promise<Groq.Audio.Transcriptions.Transcription> {
  try {
    const arrayBuffer = await audioBlob.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    const file = new File([buffer], 'audio.wav', { type: 'audio/wav' }); // ⚡ Correction ici

    const client = getNextClient();
    const transcription = await client.audio.transcriptions.create({
      file,
      model: 'whisper-large-v3-turbo',
      response_format: 'text',
      language: 'fr',
      temperature: 0.2,
      prompt:
        "Transcription d'une conversation en français. Inclure la ponctuation appropriée.",
    });

    return transcription;
  } catch (error) {
    console.error('Error transcribing audio:', error);
    throw error;
  }
}

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const audio = formData.get('audio') as Blob;

    if (!audio) {
      return NextResponse.json(
        { error: 'No audio file provided' },
        { status: 400 },
      );
    }

    if (audio.size > 20 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'Audio file too large' },
        { status: 413 },
      );
    }

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 30000);

    try {
      const transcription = await transcribeAudio(audio);
      clearTimeout(timeout);

      return NextResponse.json({ transcription, status: 'success' });
    } catch (error: any) {
      clearTimeout(timeout);
      console.error('Transcription error:', error);

      return NextResponse.json(
        {
          error: 'Failed to transcribe audio',
          details: error.message || 'Unknown error',
        },
        { status: 500 },
      );
    }
  } catch (error: any) {
    console.error('Error in transcribe route:', error);

    if (error.name === 'AbortError') {
      return NextResponse.json(
        { error: 'Transcription timeout' },
        { status: 504 },
      );
    }

    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 },
    );
  }
}
