import { Badge } from '@/components/ui/badge';
import { Tag } from 'lucide-react';
import { Memory } from '@phosphor-icons/react';
import { formatDistanceToNow } from 'date-fns';
import { useState } from 'react';

import { getUserId } from '@/lib/utils';
import { toast } from 'sonner';

interface MemoryAddResponse {
  id: string;
  data: {
    memory: string;
  };
  event: 'ADD';
}

interface MemorySearchResponse {
  id: string;
  memory: string;
  user_id: string;
  metadata: Record<string, any> | null;
  categories: string[];
  immutable: boolean;
  created_at: string;
  updated_at: string;
  message: string | null;
}

interface MemoryItem {
  id: string;
  content: string;
  created_at: string;
  tags: string[];
  role?: string;
  timestamp?: string;
}

interface MemoryManagerProps {
  result: {
    success: boolean;
    action: 'add' | 'search';
    memory?: MemoryAddResponse;
    results?: MemorySearchResponse | MemorySearchResponse[];
    message?: string;
  };
}

export const MemoryManager = ({ result }: MemoryManagerProps) => {
  const [copied, setCopied] = useState(false);
  const userId = getUserId();

  const handleCopyUserId = async () => {
    await navigator.clipboard.writeText(userId);
    setCopied(true);
    toast.success('User ID copied to clipboard');
    setTimeout(() => setCopied(false), 2000);
  };

  const getActionTitle = (action: string) => {
    switch (action) {
      case 'add':
        return 'Memory Updated';
      case 'search':
        return 'Memory Search Results';
      default:
        return 'Memory Operation';
    }
  };

  const getMemories = (): MemoryItem[] => {
    if (result.action === 'add' && result.memory) {
      return [
        {
          id: result.memory.id,
          content: result.memory.data.memory,
          created_at: new Date().toISOString(),
          tags: [],
          role: 'system', // Valeur par défaut
          timestamp: new Date().toISOString(),
        },
      ];
    }

    if (result.action === 'search') {
      // Vérifier si nous avons des résultats
      if (
        !result.results ||
        (Array.isArray(result.results) && result.results.length === 0)
      ) {
        return []; // Retourner un tableau vide si aucun résultat
      }

      // Handle both single result and array of results
      const searchResults = Array.isArray(result.results)
        ? result.results
        : [result.results];

      return searchResults.map((item) => ({
        id: item.id,
        content: item.memory,
        created_at: item.created_at,
        tags: item.categories || [],
        // Ajouter des métadonnées si disponibles
        role: item.metadata?.role || 'unknown',
        timestamp: item.metadata?.timestamp || item.created_at,
      }));
    }

    return [];
  };

  const memories = getMemories();
  const actionTitle = getActionTitle(result.action);
  const noResultsMessage =
    result.action === 'search' && memories.length === 0
      ? 'Aucun souvenir trouvé pour cette recherche.'
      : null;

  const MemoryCard = () => (
    <div className="rounded-lg border border-neutral-200 dark:border-neutral-800 overflow-hidden">
      <div className="bg-neutral-50 dark:bg-neutral-900 px-4 py-2 border-b border-neutral-200 dark:border-neutral-800 flex items-center">
        <div className="size-6 rounded-full bg-violet-100 dark:bg-violet-900/30 flex items-center justify-center mr-2">
          <Memory className="size-3 text-violet-500" weight="duotone" />
        </div>
        <h3 className="text-sm font-medium">{actionTitle}</h3>
      </div>
      <div className="p-4">
        {noResultsMessage ? (
          <p className="text-sm text-neutral-600 dark:text-neutral-400">
            {noResultsMessage}
          </p>
        ) : (
          memories.map((memory) => (
            <div key={memory.id} className="mb-4 last:mb-0">
              <div className="flex items-center mb-1">
                <span
                  className={`text-xs font-medium px-2 py-1 rounded-full ${
                    memory.role === 'user'
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                      : memory.role === 'assistant'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
                  }`}
                >
                  {memory.role === 'user'
                    ? 'Vous'
                    : memory.role === 'assistant'
                      ? 'Assistant'
                      : 'Système'}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                  {formatDistanceToNow(new Date(memory.created_at), {
                    addSuffix: true,
                  })}
                </span>
              </div>
              <p className="text-sm whitespace-pre-wrap break-words overflow-x-auto max-w-full">
                {memory.content}
              </p>
              {memory.tags && memory.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-1">
                  {memory.tags.map((tag) => (
                    <Badge
                      key={`${memory.id}-tag-${tag}`}
                      variant="outline"
                      className="text-xs"
                    >
                      <Tag className="size-3 mr-1" /> {tag}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );

  return (
    <div className="w-full my-4">
      <MemoryCard />
    </div>
  );
};

export default MemoryManager;
