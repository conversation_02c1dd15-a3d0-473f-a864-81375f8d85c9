import { Artifact } from '@/components/create-artifact';
import { DiffView } from '@/components/diffview';
import { DocumentSkeleton } from '@/components/document-skeleton';
import { Editor } from '@/components/text-editor';
import { MarkdownRenderer } from '@/components/markdown-renderer';
import {
  ClockRewind,
  CopyIcon,
  MessageIcon,
  PenIcon,
  RedoIcon,
  UndoIcon,
} from '@/components/icons';
import type { Suggestion } from '@/lib/db/schema';
import { toast } from 'sonner';
import { getSuggestions } from '../actions';
import { useRef, useEffect } from 'react';

interface TextArtifactMetadata {
  suggestions: Array<Suggestion>;
}

export const textArtifact = new Artifact<'text', TextArtifactMetadata>({
  kind: 'text',
  description: 'Useful for text content, like drafting essays and emails.',
  initialize: async ({ documentId, setMetadata }) => {
    const suggestions = await getSuggestions({ documentId });

    setMetadata({
      suggestions,
    });
  },
  onStreamPart: ({ streamPart, setMetadata, setArtifact }) => {
    if (streamPart.type === 'suggestion') {
      setMetadata((metadata) => {
        return {
          suggestions: [
            ...metadata.suggestions,
            streamPart.content as Suggestion,
          ],
        };
      });
    }

    if (streamPart.type === 'text-delta') {
      setArtifact((draftArtifact) => {
        return {
          ...draftArtifact,
          content: draftArtifact.content + (streamPart.content as string),
          isVisible:
            draftArtifact.status === 'streaming' &&
            draftArtifact.content.length > 400 &&
            draftArtifact.content.length < 450
              ? true
              : draftArtifact.isVisible,
          status: 'streaming',
        };
      });
    }
  },
  content: ({
    mode,
    status,
    content,
    isCurrentVersion,
    currentVersionIndex,
    onSaveContent,
    getDocumentContentById,
    isLoading,
    metadata,
  }) => {
    // Always declare all hooks at the top level, before any conditional logic
    const hasRenderedRef = useRef(false);
    // Create a stable key for rendering
    const renderKeyRef = useRef(`text-artifact-${currentVersionIndex}`);
    const renderKey = renderKeyRef.current;

    // Effet pour marquer le contenu comme rendu
    useEffect(() => {
      hasRenderedRef.current = true;

      // Nettoyage lors du démontage du composant
      return () => {
        hasRenderedRef.current = false;
      };
    }, []);

    if (isLoading) {
      return <DocumentSkeleton artifactKind="text" />;
    }

    if (mode === 'diff' && getDocumentContentById) {
      const oldContent = getDocumentContentById(currentVersionIndex - 1);
      const newContent = getDocumentContentById(currentVersionIndex);

      return <DiffView oldContent={oldContent} newContent={newContent} />;
    }

    return (
      <>
        <div className="flex flex-row py-8 md:p-20 px-4" key={renderKey}>
          {isCurrentVersion && status !== 'streaming' ? (
            <Editor
              content={content}
              suggestions={metadata?.suggestions || []}
              isCurrentVersion={isCurrentVersion}
              currentVersionIndex={currentVersionIndex}
              status={status}
              onSaveContent={onSaveContent}
              isEditing={false} // Changer à false pour utiliser le rendu Markdown
            />
          ) : (
            <MarkdownRenderer
              content={content}
              className="w-full markdown-persistent"
            />
          )}

          {metadata?.suggestions && metadata.suggestions.length > 0 ? (
            <div className="md:hidden h-dvh w-12 shrink-0" />
          ) : null}
        </div>
      </>
    );
  },
  actions: [
    {
      icon: <ClockRewind size={18} />,
      description: 'View changes',
      onClick: ({ handleVersionChange }) => {
        handleVersionChange('toggle');
      },
      isDisabled: ({ currentVersionIndex }) => {
        if (currentVersionIndex === 0) {
          return true;
        }

        return false;
      },
    },
    {
      icon: <UndoIcon size={18} />,
      description: 'View Previous version',
      onClick: ({ handleVersionChange }) => {
        handleVersionChange('prev');
      },
      isDisabled: ({ currentVersionIndex }) => {
        if (currentVersionIndex === 0) {
          return true;
        }

        return false;
      },
    },
    {
      icon: <RedoIcon size={18} />,
      description: 'View Next version',
      onClick: ({ handleVersionChange }) => {
        handleVersionChange('next');
      },
      isDisabled: ({ isCurrentVersion }) => {
        if (isCurrentVersion) {
          return true;
        }

        return false;
      },
    },
    {
      icon: <CopyIcon size={18} />,
      description: 'Copy to clipboard',
      onClick: ({ content }) => {
        // Ne pas essayer de parser le contenu, l'utiliser directement
        try {
          // Vérifier si le contenu est un JSON valide
          if (
            typeof content === 'string' &&
            (content.trim().startsWith('{') || content.trim().startsWith('['))
          ) {
            try {
              // Si c'est un JSON valide, essayer de le parser
              const parsedContent = JSON.parse(content);
              // Si c'est un objet avec une propriété htmlContent, utiliser cette propriété
              if (
                parsedContent &&
                typeof parsedContent === 'object' &&
                parsedContent.htmlContent
              ) {
                navigator.clipboard.writeText(parsedContent.htmlContent);
              } else {
                // Sinon, utiliser le contenu JSON stringifié
                navigator.clipboard.writeText(
                  JSON.stringify(parsedContent, null, 2),
                );
              }
            } catch (e) {
              // Si le parsing échoue, utiliser le contenu brut
              navigator.clipboard.writeText(content);
            }
          } else {
            // Si ce n'est pas du JSON, utiliser le contenu brut
            navigator.clipboard.writeText(content);
          }
          toast.success('Copied to clipboard!');
        } catch (e) {
          console.error('Error copying to clipboard:', e);
          toast.error('Failed to copy to clipboard');
        }
      },
    },
  ],
  toolbar: [
    {
      icon: <PenIcon />,
      description: 'Add final polish',
      onClick: ({ appendMessage }) => {
        appendMessage({
          role: 'user',
          content:
            'Please add final polish and check for grammar, add section titles for better structure, and ensure everything reads smoothly.',
        });
      },
    },
    {
      icon: <MessageIcon />,
      description: 'Request suggestions',
      onClick: ({ appendMessage }) => {
        appendMessage({
          role: 'user',
          content:
            'Please add suggestions you have that could improve the writing.',
        });
      },
    },
  ],
});
