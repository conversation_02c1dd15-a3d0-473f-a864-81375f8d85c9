import type { FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('Running global setup...');

  try {
    // Import and initialize mocks
    const { initializeMocks } = require('./mocks');
    initializeMocks();
    console.log('Mocks initialized successfully');
  } catch (error) {
    console.error('Failed to initialize mocks:', error);
    // Don't fail the setup if mocks can't be initialized
    // This allows tests to run even if mock setup fails
  }

  console.log('Global setup completed');
}

export default globalSetup;
