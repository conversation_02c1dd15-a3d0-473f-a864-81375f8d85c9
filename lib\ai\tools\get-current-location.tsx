import { z } from 'zod';
import { createStreamableUI } from 'ai/rsc';

export const getCurrentLocationToolDefinition = {
  name: 'getCurrentLocation',
  description: "Obtenir la position géographique actuelle de l'utilisateur",
  parameters: z.object({
    // Aucun paramètre nécessaire pour cet outil
  }),
} as const;

export async function getCurrentLocationTool() {
  // Cette fonction est appelée côté client via le mécanisme de streaming
  // Elle renvoie un composant React qui sera affiché à l'utilisateur
  const ui = createStreamableUI(
    <div className="p-4 bg-muted/50 rounded-lg">
      <p className="mb-2">
        Pour vous localiser, j'ai besoin de votre autorisation pour accéder à
        votre position.
      </p>
      <p className="text-sm text-muted-foreground mb-4">
        Cette information ne sera utilisée que pour vous fournir des réponses
        pertinentes et ne sera pas stockée.
      </p>
      <div id="location-detector-container">
        {/* Le composant LocationDetector sera rendu ici par le client */}
      </div>
    </div>,
  );

  return {
    ui: ui.value,
    // Ces données seront disponibles côté client pour le composant LocationDetector
    location: null as {
      latitude: number;
      longitude: number;
      accuracy: number;
    } | null,
  };
}
