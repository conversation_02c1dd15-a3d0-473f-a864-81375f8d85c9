import { useEffect, useState } from 'react';
import type { MessageAnnotation } from '../../types/ai-extensions';
import VideosComponent from '../videos/VideosComponent';

interface Video {
  link: string;
  title?: string;
  thumbnail?: string;
}

interface YouTubeSearchAnnotationProps {
  annotation: MessageAnnotation<{
    query: string;
    status: string;
    resultsCount: number;
  }>;
  videos: Video[];
}

export function YouTubeSearchAnnotation({
  annotation,
  videos,
}: YouTubeSearchAnnotationProps) {
  const [isVisible, setIsVisible] = useState(true);

  // Hide the annotation after 5 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  if (!isVisible) {
    return null;
  }

  return (
    <div className="flex flex-col space-y-2 text-sm text-muted-foreground">
      <div className="flex items-center space-x-2">
        <span className="text-xs">
          Recherche YouTube: {annotation.data.query}
        </span>
        <span className="text-xs">
          {annotation.data.status === 'completed'
            ? `${annotation.data.resultsCount} résultats trouvés`
            : 'Recherche en cours...'}
        </span>
      </div>
    </div>
  );
}

interface YouTubeVideosDisplayProps {
  videos: Video[];
}

export function YouTubeVideosDisplay({ videos }: YouTubeVideosDisplayProps) {
  if (!videos || videos.length === 0) {
    return null;
  }

  return <VideosComponent videos={videos} />;
}
