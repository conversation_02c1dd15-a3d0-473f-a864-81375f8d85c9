# Timeline Streaming avec createUIMessageStream

Ce système remplace et étend le rôle de `DataStreamWriter` en utilisant `createUIMessageStream` et ses outils associés pour créer une timeline en streaming avec annotations.

## Vue d'ensemble

Le système permet de :
- ✅ Streamer une timeline avec annotations en temps réel
- ✅ Utiliser `data-timeline` avec un id pour chaque événement
- ✅ Le client écoute avec `onData()` et fait la mise à jour en direct
- ✅ C'est beaucoup plus puissant qu'un simple flux de texte

## Architecture

### 1. Types de Timeline (`types/timeline.ts`)

```typescript
export interface TimelineEvent {
  id: string;
  type: 'search' | 'analysis' | 'code' | 'synthesis' | 'status';
  title: string;
  description?: string;
  status: 'loading' | 'success' | 'error' | 'warning';
  timestamp: number;
  // Données spécifiques selon le type
  searchData?: { query: string; results?: Array<...> };
  analysisData?: { topic: string; insights: string[]; confidence: number };
  // ...
}

export type TimelineUIMessage = UIMessage<never, {
  'timeline-event': TimelineEvent;
  'timeline-status': { phase: string; progress: number; message: string };
  'timeline-annotation': { eventId: string; annotation: string; type: string };
}>;
```

### 2. Stream de Timeline (`lib/ai/timeline-stream.ts`)

```typescript
export function createTimelineStream<T extends TimelineUIMessage = TimelineUIMessage>(options: {
  execute: (writer: TimelineWriter) => Promise<void> | void;
}) {
  return createUIMessageStream<T>({
    execute: ({ writer: baseWriter }) => {
      const timelineWriter: TimelineWriter = {
        writeEvent: (event) => {
          baseWriter.write({
            type: 'data-timeline-event',
            id: event.id,
            data: { ...event, timestamp: Date.now() },
          });
        },
        updateEvent: (id, updates) => {
          baseWriter.write({
            type: 'data-timeline-event',
            id, // Même ID = mise à jour automatique
            data: { ...updates, timestamp: Date.now() },
          });
        },
        writeStatus: (status) => {
          baseWriter.write({
            type: 'data-timeline-status',
            data: status,
            transient: true, // Pas dans l'historique
          });
        },
        writeAnnotation: (eventId, annotation, type = 'note') => {
          baseWriter.write({
            type: 'data-timeline-annotation',
            data: { eventId, annotation, type },
            transient: true,
          });
        },
      };
      return options.execute(timelineWriter);
    },
  });
}
```

### 3. Composant Timeline (`components/timeline-view.tsx`)

Le composant `TimelineView` affiche :
- Une timeline verticale avec icônes et statuts
- Des événements expandables avec détails
- Des annotations contextuelles
- Un système de scroll automatique
- Des statistiques en temps réel

### 4. Intégration dans les Outils

Exemple avec `extreme-search` :

```typescript
export const extremeSearchTool = () =>
  tool({
    description: 'Recherche avec timeline en temps réel',
    parameters: z.object({
      prompt: z.string(),
    }),
    execute: async ({ prompt }) => {
      const timelineStream = createTimelineStream({
        execute: async (timelineWriter) => {
          // 1. Événement de démarrage
          timelineWriter.writeEvent({
            id: 'planning',
            type: 'analysis',
            title: 'Planification',
            status: 'loading',
          });

          // 2. Mise à jour du statut global
          timelineWriter.writeStatus({
            phase: 'Recherche en cours',
            progress: 25,
            message: 'Analyse des sources...'
          });

          // 3. Événement de recherche
          const searchId = 'search-1';
          timelineWriter.writeEvent(TimelineEvents.searchStart(searchId, query));
          
          const results = await searchWeb(query);
          
          // 4. Mise à jour de l'événement (réconciliation)
          timelineWriter.updateEvent(searchId, 
            TimelineEvents.searchComplete(searchId, query, results)
          );

          // 5. Annotation
          timelineWriter.writeAnnotation(
            searchId, 
            `${results.length} sources trouvées`, 
            'success'
          );

          return research;
        }
      });

      return { stream: timelineStream };
    },
  });
```

## Utilisation Côté Client

### 1. Hook useChat avec Timeline

```typescript
const { messages } = useChat<TimelineUIMessage>({
  api: '/api/chat',
  onData: (dataPart) => {
    switch (dataPart.type) {
      case 'data-timeline-event':
        // Mise à jour des événements (avec réconciliation automatique)
        setEvents(prev => {
          const existingIndex = prev.findIndex(e => e.id === dataPart.data.id);
          if (existingIndex >= 0) {
            const updated = [...prev];
            updated[existingIndex] = { ...updated[existingIndex], ...dataPart.data };
            return updated;
          }
          return [...prev, dataPart.data];
        });
        break;
        
      case 'data-timeline-status':
        setStatus(dataPart.data);
        break;
        
      case 'data-timeline-annotation':
        setAnnotations(prev => [...prev, dataPart.data]);
        break;
    }
  },
});
```

### 2. Composant d'Affichage

```typescript
<TimelineView
  events={events}
  status={status}
  annotations={annotations}
  className="h-96"
/>
```

## Avantages par rapport à DataStreamWriter

### 1. Réconciliation Automatique
- Les événements peuvent être mis à jour en temps réel
- Même ID = mise à jour automatique côté client
- Parfait pour les états de chargement → succès

### 2. Types Stricts
- TypeScript complet pour tous les data parts
- Validation automatique des données
- Meilleure expérience développeur

### 3. Données Transientes
- Status et annotations temporaires
- Pas stockés dans l'historique des messages
- Parfait pour les notifications en temps réel

### 4. Flexibilité
- Différents types d'événements (search, analysis, code, synthesis)
- Données spécifiques par type
- Extensible facilement

### 5. Interface Utilisateur Riche
- Timeline interactive avec détails
- Statistiques en temps réel
- Annotations contextuelles
- Auto-scroll intelligent

## Exemples d'Utilisation

### Recherche Web
```typescript
const searchId = `search-${Date.now()}`;
timelineWriter.writeEvent(TimelineEvents.searchStart(searchId, query));
const results = await searchWeb(query);
timelineWriter.updateEvent(searchId, TimelineEvents.searchComplete(searchId, query, results));
```

### Analyse de Données
```typescript
const analysisId = `analysis-${Date.now()}`;
timelineWriter.writeEvent(TimelineEvents.analysisStart(analysisId, topic));
const insights = await analyzeData(data);
timelineWriter.updateEvent(analysisId, TimelineEvents.analysisComplete(analysisId, topic, insights, 0.9));
```

### Exécution de Code
```typescript
const codeId = `code-${Date.now()}`;
timelineWriter.writeEvent(TimelineEvents.codeExecution(codeId, 'python', code));
const output = await runCode(code);
timelineWriter.updateEvent(codeId, TimelineEvents.codeComplete(codeId, 'python', code, output));
```

## Migration depuis DataStreamWriter

### Avant
```typescript
dataStream.writeMessageAnnotation({
  status: { title: 'Searching...' },
});
```

### Après
```typescript
timelineWriter.writeEvent({
  id: 'search-1',
  type: 'search',
  title: 'Recherche en cours',
  status: 'loading',
});
```

Le nouveau système est beaucoup plus expressif et permet une meilleure expérience utilisateur avec une timeline interactive en temps réel.