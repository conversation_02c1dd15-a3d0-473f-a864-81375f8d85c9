/**
 * Generates speech from text using the ElevenLabs API
 * @param text The text to convert to speech
 * @param voiceId The ID of the voice to use (defaults to "<PERSON>")
 * @returns A buffer containing the audio data in MP3 format
 */
export async function generateSpeech(
  text: string,
  voiceId = 'JBFqnCBsd6RMkjVDRZzb',
) {
  const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY;

  if (!ELEVENLABS_API_KEY) {
    throw new Error(
      'ELEVENLABS_API_KEY is not defined in environment variables',
    );
  }

  const url = `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`;

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      Accept: 'audio/mpeg',
      'xi-api-key': ELEVENLABS_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      text,
      model_id: 'eleven_turbo_v2_5',
      voice_settings: {
        stability: 0.5,
        similarity_boost: 0.5,
      },
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`ElevenLabs API error: ${response.status} ${errorText}`);
  }

  return await response.arrayBuffer();
}
