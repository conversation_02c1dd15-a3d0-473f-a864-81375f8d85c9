import { memo } from 'react';

import type { ArtifactKind } from './artifact';
import {
  FileIcon,
  HtmlIcon,
  ImageIcon,
  LoaderIcon,
  MessageIcon,
  PencilEditIcon,
} from './icons';
import { toast } from 'sonner';
import { useArtifact } from '@/hooks/use-artifact';
import { getDocumentContentFromMemory } from '@/lib/ai/memory-retrieval';

const getActionText = (
  type: 'create' | 'update' | 'request-suggestions',
  tense: 'present' | 'past',
) => {
  switch (type) {
    case 'create':
      return tense === 'present' ? 'Creating' : 'Created';
    case 'update':
      return tense === 'present' ? 'Updating' : 'Updated';
    case 'request-suggestions':
      return tense === 'present'
        ? 'Adding suggestions'
        : 'Added suggestions to';
    default:
      return null;
  }
};

interface DocumentToolResultProps {
  type: 'create' | 'update' | 'request-suggestions';
  result: { id: string; title: string; kind: ArtifactKind };
  isReadonly: boolean;
}

function PureDocumentToolResult({
  type,
  result,
  isReadonly,
}: DocumentToolResultProps) {
  const { setArtifact } = useArtifact();

  return (
    <button
      type="button"
      className="bg-background cursor-pointer border py-2 px-3 rounded-xl w-fit flex flex-row gap-3 items-start"
      onClick={async (event) => {
        if (isReadonly) {
          toast.error(
            'Viewing files in shared chats is currently not supported.',
          );
          return;
        }

        const rect = event.currentTarget.getBoundingClientRect();

        const boundingBox = {
          top: rect.top,
          left: rect.left,
          width: rect.width,
          height: rect.height,
        };

        // Vérifier si le document est un artifact HTML en examinant son contenu
        let isHtmlArtifact = false;
        let detectedKind = result.kind || 'text'; // Valeur par défaut

        try {
          // Récupérer le document depuis l'API
          console.log(
            `Fetching document from API: /api/document?id=${result.id}`,
          );
          const response = await fetch(`/api/document?id=${result.id}`);
          if (response.ok) {
            // L'API existante renvoie un tableau de documents
            const documents = await response.json();

            // Vérifier si nous avons reçu un tableau ou un objet unique
            const document = Array.isArray(documents)
              ? documents[0]
              : documents;

            if (!document) {
              console.error(
                `DocumentToolResult - Document not found or empty response`,
              );
              throw new Error(`Document not found or empty response`);
            }

            // Utiliser directement le kind retourné par l'API, qui a déjà fait la détection
            const documentKind = document.kind || document.text;
            if (documentKind) {
              detectedKind = documentKind;
              // Si le kind est 'html', alors c'est un artifact HTML
              isHtmlArtifact = documentKind === 'html';

              console.log(
                `DocumentToolResult - API returned kind=${documentKind} for document ${result.id}`,
              );
            } else {
              console.warn(
                `DocumentToolResult - API did not return kind for document ${result.id}, falling back to content detection`,
              );

              // Fallback: vérifier si le contenu est un JSON valide pour un artifact HTML
              if (document.content) {
                try {
                  const content = JSON.parse(document.content);
                  // Vérifier si au moins un des champs HTML est présent
                  if (
                    content &&
                    typeof content === 'object' &&
                    (content.htmlContent ||
                      content.cssContent ||
                      content.jsContent)
                  ) {
                    isHtmlArtifact = true;
                    detectedKind = 'html';
                    console.log(
                      `DocumentToolResult - Detected HTML artifact from content for document ${result.id}`,
                      {
                        hasHtml: !!content.htmlContent,
                        hasCss: !!content.cssContent,
                        hasJs: !!content.jsContent,
                      },
                    );
                  }
                } catch (e) {
                  console.error(
                    `DocumentToolResult - Failed to parse content as JSON for document ${result.id}`,
                    e,
                  );
                  // Pas un JSON valide, donc pas un artifact HTML
                }
              }
            }

            console.log(
              `Document ${result.id}: detected kind=${detectedKind}, original kind=${result.kind || 'unknown'}, isHtmlArtifact=${isHtmlArtifact}`,
            );
          }
        } catch (error) {
          console.error('Error checking document content:', error);
        }

        setArtifact((currentArtifact) => {
          // Pour les artifacts HTML, préserver le contenu s'il existe déjà
          if (
            (detectedKind === 'html' || result.kind === 'html') &&
            currentArtifact.kind === 'html' &&
            currentArtifact.documentId === result.id &&
            currentArtifact.content
          ) {
            console.log(
              'Opening existing HTML artifact with preserved content:',
              {
                resultKind: result.kind,
                detectedKind,
                currentKind: currentArtifact.kind,
                documentId: result.id,
                title: result.title,
                contentLength: currentArtifact.content?.length || 0,
              },
            );

            return {
              ...currentArtifact,
              documentId: result.id,
              title: result.title,
              isVisible: true,
              status: 'idle',
              boundingBox,
            };
          }

          // Pour les HTML artifacts, mais avec un nouveau document ou sans contenu existant
          if (detectedKind === 'html' || result.kind === 'html') {
            console.log('Opening new HTML artifact:', {
              resultKind: result.kind,
              detectedKind,
              currentKind: currentArtifact.kind,
              documentId: result.id,
              title: result.title,
            });

            return {
              documentId: result.id,
              kind: 'html', // Forcer le type à HTML
              content: '', // Le contenu sera chargé par le composant HtmlPreview
              title: result.title,
              isVisible: true,
              status: 'idle',
              boundingBox,
            };
          }

          // Pour les autres types d'artifacts
          console.log('Opening non-HTML artifact:', {
            resultKind: result.kind,
            detectedKind,
            documentId: result.id,
            title: result.title,
          });

          return {
            documentId: result.id,
            kind: detectedKind, // Utiliser le type détecté
            content: '',
            title: result.title,
            isVisible: true,
            status: 'idle',
            boundingBox,
          };
        });

        // Vérifier si le document existe déjà en mémoire
        try {
          // Pour les mises à jour, ne jamais afficher le toast
          if (type === 'update') {
            // Ne rien afficher pour les mises à jour, car elles sont déjà traitées
            console.log(`Update for document "${result.title}" completed`);
          } else {
            // Pour les autres types (create, request-suggestions), vérifier la mémoire
            const content = await getDocumentContentFromMemory(result.id);
            // Ne rien afficher si le document est déjà en mémoire
            if (!content) {
              // Document pas encore en mémoire, afficher le toast
              toast.success(
                `Le document "${result.title}" a été sauvegardé en mémoire. Il sera disponible pour consultation dans quelques instants.`,
                { duration: 5000 },
              );
            }
          }
        } catch (error) {
          console.error('Error checking document memory status:', error);
          // En cas d'erreur, ne pas afficher de toast
        }
      }}
    >
      <div className="text-muted-foreground mt-1">
        {type === 'create' ? (
          result.kind === 'html' ? (
            <HtmlIcon />
          ) : result.kind === 'image' ? (
            <ImageIcon />
          ) : (
            <FileIcon />
          )
        ) : type === 'update' ? (
          <PencilEditIcon />
        ) : type === 'request-suggestions' ? (
          <MessageIcon />
        ) : null}
      </div>
      <div className="text-left">
        {`${getActionText(type, 'past')} "${result.title}"`}
      </div>
    </button>
  );
}

export const DocumentToolResult = memo(PureDocumentToolResult, () => true);

interface DocumentToolCallProps {
  type: 'create' | 'update' | 'request-suggestions';
  args: { title: string; kind?: ArtifactKind };
  isReadonly: boolean;
}

function PureDocumentToolCall({
  type,
  args,
  isReadonly,
}: DocumentToolCallProps) {
  const { setArtifact } = useArtifact();

  return (
    <button
      type="button"
      className="cursor pointer w-fit border py-2 px-3 rounded-xl flex flex-row items-start justify-between gap-3"
      onClick={(event) => {
        if (isReadonly) {
          toast.error(
            'Viewing files in shared chats is currently not supported.',
          );
          return;
        }

        const rect = event.currentTarget.getBoundingClientRect();

        const boundingBox = {
          top: rect.top,
          left: rect.left,
          width: rect.width,
          height: rect.height,
        };

        setArtifact((currentArtifact) => ({
          ...currentArtifact,
          isVisible: true,
          boundingBox,
        }));
      }}
    >
      <div className="flex flex-row gap-3 items-start">
        <div className="text-zinc-500 mt-1">
          {type === 'create' ? (
            args.kind === 'html' ? (
              <HtmlIcon />
            ) : args.kind === 'image' ? (
              <ImageIcon />
            ) : (
              <FileIcon />
            )
          ) : type === 'update' ? (
            <PencilEditIcon />
          ) : type === 'request-suggestions' ? (
            <MessageIcon />
          ) : null}
        </div>

        <div className="text-left">
          {`${getActionText(type, 'present')} ${args.title ? `"${args.title}"` : ''}`}
        </div>
      </div>

      <div className="animate-spin mt-1">{<LoaderIcon />}</div>
    </button>
  );
}

export const DocumentToolCall = memo(PureDocumentToolCall, () => true);
