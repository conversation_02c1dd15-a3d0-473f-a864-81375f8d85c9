import { AccommodationDiningAgent } from './lib/ai/workflows/agents/accommodation-dining-agent';
import { myProvider } from './lib/ai/providers';

// Test data
const destinationInfo = {
  destination: 'Paris',
  country: 'France',
  coordinates: {
    lat: '48.8566',
    lng: '2.3522',
  },
  duration: '5 days',
};

const userPreferences = {
  budget: {
    level: 'moderate',
  },
  accommodation: {
    type: ['hotel', 'boutique'],
  },
  dining: {
    cuisinePreferences: ['french', 'international'],
    dietaryRestrictions: [],
  },
  travelWithChildren: false,
  accessibility: {
    mobilityIssues: false,
  },
};

async function testAccommodationAgent() {
  console.log('🧪 Testing AccommodationDiningAgent...');

  try {
    // Create agent instance
    const agent = new AccommodationDiningAgent(
      myProvider.languageModel('artifact-model'),
    );

    // Get recommendations
    console.log('📍 Getting recommendations for Paris...');
    const recommendations = await agent.getRecommendations(
      destinationInfo,
      userPreferences,
    );

    // Display results
    console.log('\n🏨 ACCOMMODATIONS:');
    console.log(
      'Recommended:',
      recommendations.accommodations.recommended.length,
      'hotels',
    );
    recommendations.accommodations.recommended.forEach((hotel, i) => {
      console.log(`  ${i + 1}. ${hotel.name} - ${hotel.priceRange}`);
      console.log(`     ${hotel.description}`);
    });

    console.log('\n🍽️ DINING:');
    console.log('Dinner options:', recommendations.dining.dinner.length);
    recommendations.dining.dinner.forEach((restaurant, i) => {
      console.log(`  ${i + 1}. ${restaurant.name} - ${restaurant.cuisine}`);
      console.log(`     ${restaurant.description} (${restaurant.priceRange})`);
    });

    console.log('\n🥘 LOCAL SPECIALTIES:');
    recommendations.dining.localSpecialties.forEach((specialty, i) => {
      console.log(`  ${i + 1}. ${specialty.dish}`);
      console.log(`     ${specialty.description}`);
      console.log(`     Where to try: ${specialty.whereToTry.join(', ')}`);
    });

    console.log('\n👨‍🍳 CULINARY EXPERIENCES:');
    console.log(
      'Cooking classes:',
      recommendations.culinaryExperiences.cookingClasses.length,
    );
    recommendations.culinaryExperiences.cookingClasses.forEach(
      (experience, i) => {
        console.log(`  ${i + 1}. ${experience.name} - ${experience.duration}`);
        console.log(`     ${experience.description} (${experience.price})`);
      },
    );

    // Test HTML presentation
    console.log('\n🎨 Generating HTML presentation...');
    const htmlPresentation = agent.createHtmlPresentation(recommendations);
    console.log(
      'HTML generated successfully! Length:',
      htmlPresentation.length,
      'characters',
    );

    console.log('\n✅ Test completed successfully!');
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testAccommodationAgent();
