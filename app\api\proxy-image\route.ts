import { NextResponse } from 'next/server';

/**
 * Proxy pour contourner les problèmes CORS lors de la récupération d'images
 * Cette API permet de récupérer des images depuis des sources externes qui bloquent les requêtes CORS
 */
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  let imageUrl = searchParams.get('url');

  if (!imageUrl) {
    return new NextResponse('URL parameter is required', { status: 400 });
  }

  // Gérer le cas où l'URL est déjà encodée
  // Si l'URL contient des séquences encodées comme %3D, %2F, etc.
  // on la décode une fois pour éviter le double encodage
  if (imageUrl.includes('%')) {
    try {
      // Tenter de décoder l'URL une fois
      const decodedUrl = decodeURIComponent(imageUrl);
      imageUrl = decodedUrl;
    } catch (e) {
      // Si le décodage échoue, on garde l'URL originale
      console.error('Failed to decode URL:', e);
    }
  }

  try {
    // Vérifier si l'URL est valide
    new URL(imageUrl);
  } catch (e) {
    return new NextResponse('Invalid URL', { status: 400 });
  }

  try {
    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; ImageProxy/1.0)',
        Accept: 'image/*',
      },
      redirect: 'follow',
    });

    if (!response.ok) {
      return new NextResponse(`Failed to fetch image: ${response.statusText}`, {
        status: response.status,
      });
    }

    const contentType = response.headers.get('content-type');
    if (!contentType?.startsWith('image/')) {
      return new NextResponse('Not an image', { status: 415 });
    }

    const imageBuffer = await response.arrayBuffer();
    const headers = new Headers();
    headers.set('Content-Type', contentType);
    headers.set('Cache-Control', 'public, max-age=86400'); // Cache for 24 hours
    headers.set('x-final-url', response.url); // Ajouter l'URL finale en cas de redirection

    return new NextResponse(imageBuffer, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('Image proxy error:', error);
    return new NextResponse('Failed to fetch image', { status: 500 });
  }
}
