import { z } from 'zod';
import { tool } from 'ai';

// Define and export the tool configuration
export const getStockScreener = tool({
  description: 'Affiche un écran de surveillance boursière avec les actions les plus importantes',
  parameters: z.object({
    // No parameters needed for the stock screener as it shows a predefined view
  }),
  execute: async () => {
    // Return a simple object that will be used by the frontend to render the component
    return {
      type: 'stock_screener',
      data: {}
    };
  }
});
