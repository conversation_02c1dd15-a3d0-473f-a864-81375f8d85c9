import { z } from 'zod';
import { generateObject, type DataStreamWriter } from 'ai';
import { myProvider } from '@/lib/ai/providers';
import { DestinationAgent } from './agents/destination-agent';
import {
  OrchestratorAgent,
  type ComprehensiveTripPlan,
} from './agents/orchestrator-agent';
import { web_search } from '@/lib/ai/tools/web-search';
import { ClassifierAgent } from './agents/classifier-agent';
import {
  ValidationAgent,
  type ValidationResult,
} from './agents/validation-agent';

// Carousel CSS and JavaScript templates
const CAROUSEL_CSS = `
/* Carousel Wrapper Styles */
.restaurant-carousel-wrapper, .hotel-carousel-wrapper, .poi-carousel-wrapper {
  position: relative;
  overflow: hidden;
  margin: 20px 0;
  width: 100%;
  max-width: 100%;
  height: 400px;
}

/* Carousel Track Styles */
.restaurant-carousel-track, .hotel-carousel-track, .poi-carousel-track {
  display: flex;
  transition: transform 0.3s ease;
  width: 100%;
  height: 100%;
}

/* Slide Styles */
.restaurant-slide, .hotel-slide, .poi-slide {
  flex: 0 0 100%;
  padding: 0 10px;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  align-items: stretch;
  overflow: hidden;
}

/* Navigation Button Styles */
.restaurant-carousel-prev, .restaurant-carousel-next,
.hotel-carousel-prev, .hotel-carousel-next,
.poi-carousel-prev, .poi-carousel-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0,0,0,0.7);
  color: white;
  border: none;
  padding: 12px 16px;
  cursor: pointer;
  font-size: 20px;
  z-index: 10;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.restaurant-carousel-prev:hover, .restaurant-carousel-next:hover,
.hotel-carousel-prev:hover, .hotel-carousel-next:hover,
.poi-carousel-prev:hover, .poi-carousel-next:hover {
  background: rgba(0,0,0,0.9);
}

.restaurant-carousel-prev, .hotel-carousel-prev, .poi-carousel-prev {
  left: 10px;
}

.restaurant-carousel-next, .hotel-carousel-next, .poi-carousel-next {
  right: 10px;
}

/* Indicator Styles */
.restaurant-carousel-indicators, .hotel-carousel-indicators, .poi-carousel-indicators {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  gap: 8px;
}

.restaurant-carousel-indicator, .hotel-carousel-indicator, .poi-carousel-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: #ccc;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.restaurant-carousel-indicator:hover, .hotel-carousel-indicator:hover, .poi-carousel-indicator:hover {
  background: #999;
}

.restaurant-carousel-indicator.active, .hotel-carousel-indicator.active, .poi-carousel-indicator.active {
  background: #007bff;
}

/* Card Styles for Carousel Items */
.restaurant-slide .card, .hotel-slide .card, .poi-slide .card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 360px;
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
}

.restaurant-slide .card:hover, .hotel-slide .card:hover, .poi-slide .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0,0,0,0.15);
}

.restaurant-slide .card img, .hotel-slide .card img, .poi-slide .card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  flex-shrink: 0;
}

.restaurant-slide .card .card-content, .hotel-slide .card .card-content, .poi-slide .card .card-content {
  padding: 15px;
  height: 160px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

.restaurant-slide .card h3, .hotel-slide .card h3, .poi-slide .card h3 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  height: 30px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.restaurant-slide .card p, .hotel-slide .card p, .poi-slide .card p {
  margin: 0 0 10px 0;
  color: #666;
  line-height: 1.3;
  height: 65px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  font-size: 0.9rem;
}

.restaurant-slide .card .rating, .hotel-slide .card .rating, .poi-slide .card .rating {
  color: #f39c12;
  font-weight: 500;
  height: 25px;
  font-size: 0.9rem;
  margin-top: auto;
}

.restaurant-slide .card .price, .hotel-slide .card .price, .poi-slide .card .price {
  color: #27ae60;
  font-weight: 600;
  font-size: 1rem;
  height: 25px;
  margin-top: auto;
}

.restaurant-slide .card .type, .hotel-slide .card .type, .poi-slide .card .type {
  color: #007bff;
  font-weight: 500;
  height: 25px;
  font-size: 0.9rem;
  margin-top: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .restaurant-carousel-prev, .restaurant-carousel-next,
  .hotel-carousel-prev, .hotel-carousel-next,
  .poi-carousel-prev, .poi-carousel-next {
    padding: 8px 12px;
    font-size: 16px;
  }

  .restaurant-slide, .hotel-slide, .poi-slide {
    padding: 0 5px;
  }
}
`;

const CAROUSEL_JAVASCRIPT = `
/**
 * Serper Image Loader - Load images from Serper API in background
 */
async function loadSerperImages() {
  console.log('🖼️ Starting Serper image loading...');
  const images = document.querySelectorAll('img[data-serper-search]');
  console.log(\`Found \${images.length} images with data-serper-search attribute\`);

  for (const img of images) {
    const searchQuery = img.getAttribute('data-serper-search');
    if (!searchQuery) {
      console.log('⚠️ Image missing search query:', img);
      continue;
    }

    console.log(\`🔍 Loading image for query: "\${decodeURIComponent(searchQuery)}"\`);

    try {
      // Call our API endpoint to get Serper images
      const response = await fetch('/api/serper-images', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: decodeURIComponent(searchQuery) }),
      });

      console.log(\`📡 API response status: \${response.status}\`);

      if (response.ok) {
        const data = await response.json();
        console.log('📦 API response data:', data);

        if (data.imageUrl) {
          console.log(\`✅ Setting new image URL: \${data.imageUrl}\`);
          img.src = data.imageUrl;

          // Add a load event to confirm the image loaded
          img.onload = () => {
            console.log(\`🎉 Image loaded successfully: \${data.imageUrl}\`);
          };

          img.onerror = () => {
            console.error(\`❌ Failed to load image: \${data.imageUrl}\`);
          };
        } else {
          console.warn('⚠️ No imageUrl in response:', data);
        }
      } else {
        const errorData = await response.json();
        console.error(\`❌ API error (\${response.status}):\`, errorData);
      }
    } catch (error) {
      console.error('💥 Error loading Serper image:', error);
    }
  }

  console.log('🏁 Finished Serper image loading process');
}

/**
 * Alternative image loader using Unsplash with better search terms
 */
async function loadFallbackImages() {
  console.log('🔄 Loading fallback images with Unsplash...');
  const images = document.querySelectorAll('img[data-serper-search]');

  for (const img of images) {
    const searchQuery = img.getAttribute('data-serper-search');
    if (!searchQuery) continue;

    // Only replace if still showing the default Unsplash image
    if (img.src.includes('images.unsplash.com') && img.src.includes('w=400&h=200')) {
      const decodedQuery = decodeURIComponent(searchQuery);
      console.log(\`🔄 Updating fallback image for: "\${decodedQuery}"\`);

      // Create a more specific Unsplash URL
      const searchTerms = decodedQuery.split(' ').slice(0, 3).join(','); // Take first 3 words
      const randomSeed = Math.floor(Math.random() * 1000);
      const newUrl = \`https://source.unsplash.com/400x200/?\${encodeURIComponent(searchTerms)}&sig=\${randomSeed}\`;

      console.log(\`🖼️ New fallback URL: \${newUrl}\`);
      img.src = newUrl;
    }
  }
}

// Load Serper images after page load with multiple attempts
document.addEventListener('DOMContentLoaded', () => {
  console.log('📄 DOM loaded, scheduling image loading...');

  // First attempt after 1 second
  setTimeout(() => {
    console.log('🚀 First image loading attempt...');
    loadSerperImages();
  }, 1000);

  // Second attempt after 3 seconds (in case carousels weren't ready)
  setTimeout(() => {
    console.log('🔄 Second image loading attempt...');
    loadSerperImages();
  }, 3000);

  // Third attempt after 5 seconds (final fallback)
  setTimeout(() => {
    console.log('🔄 Final image loading attempt...');
    loadSerperImages();
  }, 5000);

  // Load better fallback images after 7 seconds
  setTimeout(() => {
    console.log('🎨 Loading enhanced fallback images...');
    loadFallbackImages();
  }, 7000);
});

/**
 * Complete Restaurant Carousel Component
 */
class RestaurantCarousel {
  constructor(containerId, options = {}) {
    this.container = document.getElementById(containerId);
    if (!this.container) {
      console.error(\`Container with ID "\${containerId}" not found.\`);
      return;
    }

    this.options = {
      slidesToShow: 1,
      slidesToScroll: 1,
      autoplay: false,
      autoplaySpeed: 5000,
      infinite: true,
      ...options
    };

    this.currentSlide = 0;
    this.totalSlides = 0;
    this.isAnimating = false;
    this.touchStartX = 0;
    this.touchEndX = 0;
    this.autoplayInterval = null;

    this.init();
  }

  init() {
    this.createCarouselStructure();
    this.slides = this.container.querySelectorAll('.restaurant-slide');
    this.totalSlides = this.slides.length;

    if (this.totalSlides === 0) {
      console.warn('No restaurant slides found');
      return;
    }

    this.updateCarousel();
    this.addEventListeners();

    if (this.options.autoplay) {
      this.startAutoplay();
    }
  }

  createCarouselStructure() {
    const wrapper = document.createElement('div');
    wrapper.className = 'restaurant-carousel-wrapper';

    const track = document.createElement('div');
    track.className = 'restaurant-carousel-track';

    while (this.container.firstChild) {
      track.appendChild(this.container.firstChild);
    }

    const prevButton = document.createElement('button');
    prevButton.className = 'restaurant-carousel-prev';
    prevButton.setAttribute('aria-label', 'Previous restaurant');
    prevButton.innerHTML = '‹';

    const nextButton = document.createElement('button');
    nextButton.className = 'restaurant-carousel-next';
    nextButton.setAttribute('aria-label', 'Next restaurant');
    nextButton.innerHTML = '›';

    const indicators = document.createElement('div');
    indicators.className = 'restaurant-carousel-indicators';

    wrapper.appendChild(track);
    wrapper.appendChild(prevButton);
    wrapper.appendChild(nextButton);
    wrapper.appendChild(indicators);
    this.container.appendChild(wrapper);

    this.track = track;
    this.prevButton = prevButton;
    this.nextButton = nextButton;
    this.indicators = indicators;
  }

  addEventListeners() {
    this.prevButton.addEventListener('click', () => this.prevSlide());
    this.nextButton.addEventListener('click', () => this.nextSlide());

    this.track.addEventListener('touchstart', (e) => {
      this.touchStartX = e.changedTouches[0].screenX;
    }, { passive: true });

    this.track.addEventListener('touchend', (e) => {
      this.touchEndX = e.changedTouches[0].screenX;
      this.handleSwipe();
    }, { passive: true });

    this.container.addEventListener('keydown', (e) => {
      if (e.key === 'ArrowLeft') {
        this.prevSlide();
      } else if (e.key === 'ArrowRight') {
        this.nextSlide();
      }
    });

    if (this.options.autoplay) {
      this.container.addEventListener('mouseenter', () => this.stopAutoplay());
      this.container.addEventListener('mouseleave', () => this.startAutoplay());
    }

    window.addEventListener('resize', () => {
      this.updateCarousel();
    });
  }

  handleSwipe() {
    const swipeThreshold = 50;
    const diff = this.touchStartX - this.touchEndX;

    if (diff > swipeThreshold) {
      this.nextSlide();
    } else if (diff < -swipeThreshold) {
      this.prevSlide();
    }
  }

  prevSlide() {
    if (this.isAnimating) return;

    this.isAnimating = true;
    this.currentSlide = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
    this.updateCarousel();

    setTimeout(() => {
      this.isAnimating = false;
    }, 300);
  }

  nextSlide() {
    if (this.isAnimating) return;

    this.isAnimating = true;
    this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
    this.updateCarousel();

    setTimeout(() => {
      this.isAnimating = false;
    }, 300);
  }

  updateCarousel() {
    if (!this.slides || this.slides.length === 0) return;

    const slideWidth = this.slides[0].offsetWidth;
    this.track.style.transform = \`translateX(-\${this.currentSlide * slideWidth}px)\`;

    this.slides.forEach((slide, index) => {
      if (index === this.currentSlide) {
        slide.classList.add('active');
        slide.setAttribute('aria-hidden', 'false');
      } else {
        slide.classList.remove('active');
        slide.setAttribute('aria-hidden', 'true');
      }
    });

    this.updateIndicators();

    if (!this.options.infinite) {
      this.prevButton.disabled = this.currentSlide === 0;
      this.nextButton.disabled = this.currentSlide === this.totalSlides - 1;
    }
  }

  updateIndicators() {
    this.indicators.innerHTML = '';

    for (let i = 0; i < this.totalSlides; i++) {
      const indicator = document.createElement('button');
      indicator.className = 'restaurant-carousel-indicator';
      indicator.setAttribute('aria-label', \`Go to restaurant \${i + 1}\`);

      if (i === this.currentSlide) {
        indicator.classList.add('active');
        indicator.setAttribute('aria-current', 'true');
      }

      indicator.addEventListener('click', () => {
        this.goToSlide(i);
      });

      this.indicators.appendChild(indicator);
    }
  }

  goToSlide(index) {
    if (this.isAnimating || index === this.currentSlide) return;

    this.isAnimating = true;
    this.currentSlide = index;
    this.updateCarousel();

    setTimeout(() => {
      this.isAnimating = false;
    }, 300);
  }

  startAutoplay() {
    if (!this.options.autoplay) return;

    this.autoplayInterval = setInterval(() => {
      this.nextSlide();
    }, this.options.autoplaySpeed);
  }

  stopAutoplay() {
    if (this.autoplayInterval) {
      clearInterval(this.autoplayInterval);
      this.autoplayInterval = null;
    }
  }
}

/**
 * Hotel Carousel - Extends RestaurantCarousel
 */
class HotelCarousel extends RestaurantCarousel {
  init() {
    this.createCarouselStructure();
    this.slides = this.container.querySelectorAll('.hotel-slide');
    this.totalSlides = this.slides.length;

    if (this.totalSlides === 0) {
      console.warn('No hotel slides found');
      return;
    }

    this.updateCarousel();
    this.addEventListeners();

    if (this.options.autoplay) {
      this.startAutoplay();
    }
  }

  createCarouselStructure() {
    const wrapper = document.createElement('div');
    wrapper.className = 'hotel-carousel-wrapper';

    const track = document.createElement('div');
    track.className = 'hotel-carousel-track';

    while (this.container.firstChild) {
      track.appendChild(this.container.firstChild);
    }

    const prevButton = document.createElement('button');
    prevButton.className = 'hotel-carousel-prev';
    prevButton.setAttribute('aria-label', 'Previous hotel');
    prevButton.innerHTML = '‹';

    const nextButton = document.createElement('button');
    nextButton.className = 'hotel-carousel-next';
    nextButton.setAttribute('aria-label', 'Next hotel');
    nextButton.innerHTML = '›';

    const indicators = document.createElement('div');
    indicators.className = 'hotel-carousel-indicators';

    wrapper.appendChild(track);
    wrapper.appendChild(prevButton);
    wrapper.appendChild(nextButton);
    wrapper.appendChild(indicators);
    this.container.appendChild(wrapper);

    this.track = track;
    this.prevButton = prevButton;
    this.nextButton = nextButton;
    this.indicators = indicators;
  }

  updateIndicators() {
    this.indicators.innerHTML = '';

    for (let i = 0; i < this.totalSlides; i++) {
      const indicator = document.createElement('button');
      indicator.className = 'hotel-carousel-indicator';
      indicator.setAttribute('aria-label', \`Go to hotel \${i + 1}\`);

      if (i === this.currentSlide) {
        indicator.classList.add('active');
        indicator.setAttribute('aria-current', 'true');
      }

      indicator.addEventListener('click', () => {
        this.goToSlide(i);
      });

      this.indicators.appendChild(indicator);
    }
  }
}

/**
 * POI Carousel - Extends RestaurantCarousel
 */
class PoiCarousel extends RestaurantCarousel {
  init() {
    this.createCarouselStructure();
    this.slides = this.container.querySelectorAll('.poi-slide');
    this.totalSlides = this.slides.length;

    if (this.totalSlides === 0) {
      console.warn('No POI slides found');
      return;
    }

    this.updateCarousel();
    this.addEventListeners();

    if (this.options.autoplay) {
      this.startAutoplay();
    }
  }

  createCarouselStructure() {
    const wrapper = document.createElement('div');
    wrapper.className = 'poi-carousel-wrapper';

    const track = document.createElement('div');
    track.className = 'poi-carousel-track';

    while (this.container.firstChild) {
      track.appendChild(this.container.firstChild);
    }

    const prevButton = document.createElement('button');
    prevButton.className = 'poi-carousel-prev';
    prevButton.setAttribute('aria-label', 'Previous attraction');
    prevButton.innerHTML = '‹';

    const nextButton = document.createElement('button');
    nextButton.className = 'poi-carousel-next';
    nextButton.setAttribute('aria-label', 'Next attraction');
    nextButton.innerHTML = '›';

    const indicators = document.createElement('div');
    indicators.className = 'poi-carousel-indicators';

    wrapper.appendChild(track);
    wrapper.appendChild(prevButton);
    wrapper.appendChild(nextButton);
    wrapper.appendChild(indicators);
    this.container.appendChild(wrapper);

    this.track = track;
    this.prevButton = prevButton;
    this.nextButton = nextButton;
    this.indicators = indicators;
  }

  updateIndicators() {
    this.indicators.innerHTML = '';

    for (let i = 0; i < this.totalSlides; i++) {
      const indicator = document.createElement('button');
      indicator.className = 'poi-carousel-indicator';
      indicator.setAttribute('aria-label', \`Go to attraction \${i + 1}\`);

      if (i === this.currentSlide) {
        indicator.classList.add('active');
        indicator.setAttribute('aria-current', 'true');
      }

      indicator.addEventListener('click', () => {
        this.goToSlide(i);
      });

      this.indicators.appendChild(indicator);
    }
  }
}

// Initialize all carousels when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  console.log('Initializing carousels...');

  if (document.getElementById('restaurant-carousel')) {
    console.log('Initializing restaurant carousel');
    new RestaurantCarousel('restaurant-carousel', {
      autoplay: false,
      infinite: true
    });
  }

  if (document.getElementById('hotel-carousel')) {
    console.log('Initializing hotel carousel');
    new HotelCarousel('hotel-carousel', {
      autoplay: false,
      infinite: true
    });
  }

  if (document.getElementById('poi-carousel')) {
    console.log('Initializing POI carousel');
    new PoiCarousel('poi-carousel', {
      autoplay: false,
      infinite: true
    });
  }
});

// Export for use in HTML artifacts
if (typeof window !== 'undefined') {
  window.RestaurantCarousel = RestaurantCarousel;
  window.HotelCarousel = HotelCarousel;
  window.PoiCarousel = PoiCarousel;
}
`;

// Define types for the advanced trip planning workflow
export interface AdvancedTripPlanningInput {
  query: string;
  dataStream: any;
}

export interface AdvancedTripPlanningOutput {
  htmlContent: string;
  cssContent: string;
  jsContent: string;
}

/**
 * Function to get a hero image URL for a destination
 */
async function getDestinationHeroImage(
  destination: string,
  country: string,
): Promise<string | null> {
  try {
    // Create a mock session and dataStream for the web_search tool
    const mockSession = {
      user: { id: 'system', type: 'guest' as const },
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    };
    // Create a mock DataStreamWriter
    const mockDataStream: DataStreamWriter = {
      writeMessageAnnotation: () => {},
      write: () => '',
      writeData: () => '',
      writeSource: () => '',
      merge: () => '',
      onError: () => '',
    };

    // Use the web_search tool to search for images of the destination
    const searchTool = web_search({
      session: mockSession,
      dataStream: mockDataStream,
    });

    // Try multiple search queries to increase chances of finding good images
    const searchQueries = [
      `beautiful ${destination} ${country} travel photos high resolution`,
      `${destination} ${country} landmark tourist attractions photos`,
      `${destination} ${country} travel guide images scenic`,
      `${destination} ${country} tourism official photos high quality`,
      `${destination} ${country} scenic views panorama`,
    ];

    // Execute all search queries in parallel with specific parameters to get images
    const searchPromises = searchQueries.map((query) =>
      (searchTool.execute as any)({
        queries: [query],
        maxResults: [10], // Increase max results to get more content
        topics: ['general'],
        searchDepth: ['advanced'], // Use advanced search for better results
      }),
    );

    const searchResults = await Promise.all(searchPromises);

    // Extract image URLs from all search results
    const imageUrls: string[] = [];

    // First, try to get images directly from the search results
    for (const result of searchResults) {
      if (
        result?.searches?.[0]?.images &&
        Array.isArray(result.searches[0].images)
      ) {
        // Check if images are objects with url property or direct strings
        const images = result.searches[0].images;
        for (const image of images) {
          if (typeof image === 'string') {
            imageUrls.push(image);
          } else if (image && typeof image === 'object' && image.url) {
            imageUrls.push(image.url);
          }
        }
      }
    }

    console.log(`Found ${imageUrls.length} direct images from search results`);

    // Process all search results
    for (const results of searchResults) {
      if (results?.searches?.[0]?.results?.length > 0) {
        for (const result of results.searches[0].results) {
          // Look for image URLs in the search results - try multiple patterns

          // Pattern 1: Direct image URLs
          const directImageMatches = result.content.matchAll(
            /(https?:\/\/[^"\s]+\.(?:jpg|jpeg|png|webp)(?:\?[^"\s]*)?)/gi,
          );

          for (const match of Array.from(directImageMatches)) {
            const m = match as RegExpExecArray;
            if (m[1] && !imageUrls.includes(m[1])) {
              imageUrls.push(m[1]);
            }
          }

          // Pattern 2: Image URLs in HTML/markdown image tags
          const imgTagMatches = result.content.matchAll(
            /(?:src|href)=["'](https?:\/\/[^"']+\.(?:jpg|jpeg|png|webp)(?:\?[^"']*)?)['"]/gi,
          );

          for (const match of Array.from(imgTagMatches)) {
            const m = match as RegExpExecArray;
            if (m[1] && !imageUrls.includes(m[1])) {
              imageUrls.push(m[1]);
            }
          }

          // Pattern 3: Look for image URLs in markdown format
          const markdownMatches = result.content.matchAll(
            /!\[.*?\]\((https?:\/\/[^)]+\.(?:jpg|jpeg|png|webp)(?:\?[^)]*)?)\)/gi,
          );

          for (const match of Array.from(markdownMatches)) {
            const m = match as RegExpExecArray;
            if (m[1] && !imageUrls.includes(m[1])) {
              imageUrls.push(m[1]);
            }
          }
        }
      }
    }

    // Filter out low-quality or unwanted images
    const filteredUrls = imageUrls.filter((url) => {
      // Filter out small images (often icons or thumbnails)
      if (
        url.includes('icon') ||
        url.includes('thumb') ||
        url.includes('small')
      ) {
        return false;
      }

      // Prefer high-quality images
      if (
        url.includes('large') ||
        url.includes('high') ||
        url.includes('full') ||
        url.includes('1200') ||
        url.includes('1600') ||
        url.includes('2000')
      ) {
        return true;
      }

      return true; // Include by default
    });

    // If we have enough images, return one
    if (filteredUrls.length > 0) {
      // Choose a random image from the top 5 to get variety
      const randomIndex = Math.floor(
        Math.random() * Math.min(5, filteredUrls.length),
      );
      return filteredUrls[randomIndex];
    }

    // If we still don't have good images, try a direct image search
    try {
      console.log('Trying direct image search...');

      // Use a more specific image search query
      const imageSearchQuery = `${destination} ${country} travel destination landscape photography`;

      const imageSearchResult = await (searchTool.execute as any)({
        queries: [imageSearchQuery],
        maxResults: [15],
        topics: ['general'],
        searchDepth: ['advanced'],
      });

      // Extract images from the image search
      const directImageUrls: string[] = [];

      if (
        imageSearchResult?.searches?.[0]?.images &&
        Array.isArray(imageSearchResult.searches[0].images)
      ) {
        const images = imageSearchResult.searches[0].images;
        for (const image of images) {
          if (typeof image === 'string') {
            directImageUrls.push(image);
          } else if (image && typeof image === 'object' && image.url) {
            directImageUrls.push(image.url);
          }
        }
      }

      console.log(
        `Found ${directImageUrls.length} images from direct image search`,
      );

      if (directImageUrls.length > 0) {
        // Choose a random image from the direct image search results
        const randomIndex = Math.floor(
          Math.random() * Math.min(5, directImageUrls.length),
        );
        return directImageUrls[randomIndex];
      }
    } catch (imageSearchError) {
      console.error('Error during direct image search:', imageSearchError);
    }

    // Fallback to Unsplash only if absolutely no images were found
    console.log('No images found, falling back to Unsplash');
    return `https://source.unsplash.com/1600x900/?${encodeURIComponent(`${destination} ${country}`)}`;
  } catch (error) {
    console.error('Error getting destination hero image:', error);
    // Return a default image URL from Unsplash
    return `https://source.unsplash.com/1600x900/?${encodeURIComponent(`${destination} ${country}`)}`;
  }
}

/**
 * Intelligently convert content to carousels using LLM analysis
 */
async function intelligentCarouselConversion(
  htmlContent: string,
  destination?: string,
): Promise<string> {
  console.log('Starting intelligent carousel conversion using LLM...');

  try {
    // Use the LLM to analyze the HTML content and identify sections that should be carousels
    const { object } = await generateObject({
      model: myProvider.languageModel('artifact-model'),
      system: `You are an expert HTML analyzer. Your task is to identify sections in HTML content that should be converted to carousels.

      Look for sections containing:
      1. Restaurant/dining information (restaurants, cafes, food places)
      2. Accommodation/hotel information (hotels, hostels, lodging)
      3. Points of interest/attractions (museums, landmarks, local recommendations)

      For each section you identify, extract the individual items and their details.

      Return a JSON object with sections to convert, where each section contains:
      - type: "restaurant", "hotel", or "poi"
      - items: array of objects with name, description, and extra info
      - originalHtml: the original HTML content to replace
      - confidence: confidence level (0-1) that this should be a carousel`,
      prompt: `Analyze this HTML content and identify sections that should be converted to carousels:

${htmlContent}

Look for any content that represents:
1. Restaurants, dining options, food places (including "Traditional Restaurant", "Local - Moderate to Expensive", etc.)
2. Hotels, accommodations, lodging
3. Tourist attractions, local recommendations, points of interest (including "Complesso Monumentale", "Santa Maria", "La Cantina", etc.)

Extract the individual items from each section with their names, descriptions, and any additional info (prices, ratings, etc.).
Be very thorough and look for content that might be in paragraphs, lists, or any other format.`,
      schema: z.object({
        sections: z.array(
          z.object({
            type: z.enum(['restaurant', 'hotel', 'poi']),
            items: z.array(
              z.object({
                name: z.string(),
                description: z.string(),
                extra: z.string().optional(),
              }),
            ),
            originalHtml: z.string(),
            confidence: z.number().min(0).max(1),
          }),
        ),
      }),
      temperature: 0.3, // Low temperature for consistent analysis
    });

    let modifiedHtml = htmlContent;

    // Process each identified section with async/await
    for (const section of object.sections) {
      if (section.confidence > 0.6 && section.items.length > 0) {
        console.log(
          `Converting ${section.type} section with ${section.items.length} items (confidence: ${section.confidence})`,
        );

        let carouselHtml = '';

        switch (section.type) {
          case 'restaurant':
            carouselHtml = createRestaurantCarousel(section.items, destination);
            break;
          case 'hotel':
            carouselHtml = createHotelCarousel(section.items, destination);
            break;
          case 'poi':
            carouselHtml = createPoiCarousel(section.items, destination);
            break;
        }

        // Replace the original content with the carousel
        if (carouselHtml && section.originalHtml) {
          modifiedHtml = modifiedHtml.replace(
            section.originalHtml,
            carouselHtml,
          );
        }
      }
    }

    return modifiedHtml;
  } catch (error) {
    console.error('Error in intelligent carousel conversion:', error);
    // Fallback to original content if LLM analysis fails
    return htmlContent;
  }
}

/**
 * Create hotel carousel HTML with fallback images (synchronous)
 */
function createHotelCarousel(
  items: Array<{ name: string; description: string; extra?: string }>,
  destination?: string,
): string {
  const slides = items
    .map(
      (item) => `
    <div class="hotel-slide">
      <div class="card">
        <img src="https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=200&fit=crop&crop=center" alt="${item.name}"
             data-serper-search="${encodeURIComponent(`${item.name} hotel ${destination || ''}`)}"
             onerror="this.src='https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=200&fit=crop&crop=center'">
        <div class="card-content">
          <h3>${item.name}</h3>
          <p>${item.description}</p>
          <div class="price">${item.extra || 'Contact for pricing'}</div>
        </div>
      </div>
    </div>
  `,
    )
    .join('');

  return `
    <div id="hotel-carousel" class="hotel-carousel">
      ${slides}
    </div>
  `;
}

/**
 * Get image URL using Serper API for restaurants
 */
async function getRestaurantImageUrl(
  restaurantName: string,
  destination?: string,
): Promise<string> {
  try {
    const searchQuery = destination
      ? `${restaurantName} restaurant ${destination}`
      : `${restaurantName} restaurant`;

    const apiKey = process.env.SERPER_API as string;
    if (!apiKey) {
      console.warn('Serper API key not found, using fallback image');
      return `https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=200&fit=crop&crop=center`;
    }

    const url = `https://google.serper.dev/images?q=${encodeURIComponent(searchQuery)}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-API-KEY': apiKey,
        'Content-Type': 'application/json',
      },
      redirect: 'follow',
    });

    if (!response.ok) {
      throw new Error(`Serper API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.images && data.images.length > 0) {
      // Filter for high-quality restaurant images
      const goodImages = data.images.filter(
        (img: any) =>
          img.imageUrl &&
          !img.imageUrl.includes('icon') &&
          !img.imageUrl.includes('thumb') &&
          (img.imageUrl.includes('restaurant') ||
            img.imageUrl.includes('food') ||
            img.imageUrl.includes('dining')),
      );

      if (goodImages.length > 0) {
        return goodImages[0].imageUrl;
      } else if (data.images.length > 0) {
        return data.images[0].imageUrl;
      }
    }
  } catch (error) {
    console.error('Error fetching restaurant image from Serper:', error);
  }

  // Fallback to a high-quality restaurant image
  return `https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=200&fit=crop&crop=center`;
}

/**
 * Create restaurant carousel HTML with fallback images (synchronous)
 */
function createRestaurantCarousel(
  items: Array<{ name: string; description: string; extra?: string }>,
  destination?: string,
): string {
  const slides = items
    .map(
      (item) => `
    <div class="restaurant-slide">
      <div class="card">
        <img src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=200&fit=crop&crop=center" alt="${item.name}"
             data-serper-search="${encodeURIComponent(`${item.name} restaurant ${destination || ''}`)}"
             onerror="this.src='https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=200&fit=crop&crop=center'">
        <div class="card-content">
          <h3>${item.name}</h3>
          <p>${item.description}</p>
          <div class="rating">${item.extra || '⭐⭐⭐⭐ 4.0/5'}</div>
        </div>
      </div>
    </div>
  `,
    )
    .join('');

  return `
    <div id="restaurant-carousel" class="restaurant-carousel">
      ${slides}
    </div>
  `;
}

/**
 * Get image URL using Serper API for POI/attractions
 */
async function getPoiImageUrl(
  poiName: string,
  destination?: string,
): Promise<string> {
  try {
    const searchQuery = destination
      ? `${poiName} ${destination} attraction tourist`
      : `${poiName} attraction tourist`;

    const apiKey = process.env.SERPER_API as string;
    if (!apiKey) {
      console.warn('Serper API key not found, using fallback image');
      return `https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=200&fit=crop&crop=center`;
    }

    const url = `https://google.serper.dev/images?q=${encodeURIComponent(searchQuery)}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-API-KEY': apiKey,
        'Content-Type': 'application/json',
      },
      redirect: 'follow',
    });

    if (!response.ok) {
      throw new Error(`Serper API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.images && data.images.length > 0) {
      // Filter for high-quality attraction images
      const goodImages = data.images.filter(
        (img: any) =>
          img.imageUrl &&
          !img.imageUrl.includes('icon') &&
          !img.imageUrl.includes('thumb') &&
          (img.imageUrl.includes('attraction') ||
            img.imageUrl.includes('tourist') ||
            img.imageUrl.includes('landmark')),
      );

      if (goodImages.length > 0) {
        return goodImages[0].imageUrl;
      } else if (data.images.length > 0) {
        return data.images[0].imageUrl;
      }
    }
  } catch (error) {
    console.error('Error fetching POI image from Serper:', error);
  }

  // Fallback to a high-quality attraction image
  return `https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=200&fit=crop&crop=center`;
}

/**
 * Create POI carousel HTML with images from LocalExpertAgent data
 */
function createPoiCarousel(
  items: Array<{
    name: string;
    description: string;
    extra?: string;
    photos?: string[];
  }>,
  destination?: string,
): string {
  console.log('🎠 Creating POI carousel with items:', items);

  const slides = items
    .map((item, index) => {
      // Use the first photo from the item if available, otherwise fallback
      const imageUrl =
        item.photos && item.photos.length > 0
          ? item.photos[0]
          : `https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=200&fit=crop&crop=center`;

      console.log(`📸 POI "${item.name}" using image:`, imageUrl);

      return `
    <div class="poi-slide">
      <div class="card">
        <img src="${imageUrl}" alt="${item.name}"
             data-serper-search="${encodeURIComponent(`${item.name} ${destination || ''} attraction tourist`)}"
             onerror="this.src='https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=200&fit=crop&crop=center'">
        <div class="card-content">
          <h3>${item.name}</h3>
          <p>${item.description}</p>
          <div class="type">${item.extra || 'Attraction'}</div>
        </div>
      </div>
    </div>
  `;
    })
    .join('');

  return `
    <div id="poi-carousel" class="poi-carousel">
      ${slides}
    </div>
  `;
}

/**
 * Get image URL using Serper API for hotels/accommodations
 */
async function getHotelImageUrl(
  hotelName: string,
  destination?: string,
): Promise<string> {
  try {
    const searchQuery = destination
      ? `${hotelName} hotel ${destination}`
      : `${hotelName} hotel accommodation`;

    const apiKey = process.env.SERPER_API as string;
    if (!apiKey) {
      console.warn('Serper API key not found, using fallback image');
      return `https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=200&fit=crop&crop=center`;
    }

    const url = `https://google.serper.dev/images?q=${encodeURIComponent(searchQuery)}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-API-KEY': apiKey,
        'Content-Type': 'application/json',
      },
      redirect: 'follow',
    });

    if (!response.ok) {
      throw new Error(`Serper API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.images && data.images.length > 0) {
      // Filter for high-quality hotel images
      const goodImages = data.images.filter(
        (img: any) =>
          img.imageUrl &&
          !img.imageUrl.includes('icon') &&
          !img.imageUrl.includes('thumb') &&
          (img.imageUrl.includes('hotel') ||
            img.imageUrl.includes('accommodation') ||
            img.imageUrl.includes('resort')),
      );

      if (goodImages.length > 0) {
        return goodImages[0].imageUrl;
      } else if (data.images.length > 0) {
        return data.images[0].imageUrl;
      }
    }
  } catch (error) {
    console.error('Error fetching hotel image from Serper:', error);
  }

  // Fallback to a high-quality hotel image
  return `https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=200&fit=crop&crop=center`;
}

/**
 * Force carousel creation for missing sections
 */
async function forceCarouselCreation(
  htmlContent: string,
  tripPlan: ComprehensiveTripPlan,
): Promise<string> {
  let modifiedHtml = htmlContent;

  console.log('Checking for broken sections...');

  // Fix Accommodations section if it contains [object Promise] or is missing carousel
  if (
    modifiedHtml.includes('[object Promise]') ||
    !modifiedHtml.includes('hotel-carousel')
  ) {
    console.log('Fixing accommodations section...');

    const accommodationsCarousel = createHotelCarousel(
      [
        {
          name: 'Luxury Hotel',
          description: 'Premium accommodation with excellent amenities',
          extra: '€150-300/night',
        },
        {
          name: 'Boutique Hotel',
          description: 'Charming local hotel with unique character',
          extra: '€80-150/night',
        },
        {
          name: 'Budget Hotel',
          description: 'Comfortable and affordable accommodation',
          extra: '€40-80/night',
        },
      ],
      tripPlan.destination.destination,
    );

    // Replace [object Promise] with carousel
    modifiedHtml = modifiedHtml.replace(
      /\[object Promise\]/g,
      accommodationsCarousel,
    );

    // Also replace any empty accommodations sections
    modifiedHtml = modifiedHtml.replace(
      /(<h[1-6][^>]*>.*?Accommodations.*?<\/h[1-6]>)\s*(<h[1-6]|$)/gis,
      `$1\n${accommodationsCarousel}\n$2`,
    );
  }

  // Fix Local Recommendations section if needed
  if (!modifiedHtml.includes('poi-carousel')) {
    console.log('Adding local recommendations carousel...');

    const poiCarousel = createPoiCarousel(
      [
        {
          name: 'Historic Center',
          description: 'Explore the charming historic district',
          extra: 'Historic Site',
        },
        {
          name: 'Local Market',
          description: 'Experience authentic local culture and cuisine',
          extra: 'Market',
        },
        {
          name: 'Scenic Viewpoint',
          description: 'Breathtaking views of the city and surroundings',
          extra: 'Viewpoint',
        },
      ],
      tripPlan.destination.destination,
    );

    // Add POI carousel after Local Recommendations heading
    modifiedHtml = modifiedHtml.replace(
      /(<h[1-6][^>]*>.*?Local Recommendations.*?<\/h[1-6]>)/gis,
      `$1\n${poiCarousel}`,
    );
  }

  return modifiedHtml;
}

/**
 * Create accommodation carousel HTML (enhanced version for accommodations) with Serper images
 */
async function createAccommodationCarousel(
  accommodations: any[],
  destination?: string,
): Promise<string> {
  if (!accommodations || accommodations.length === 0) {
    const fallbackImageUrl = await getHotelImageUrl(
      'luxury hotel',
      destination,
    );
    return `
      <div id="hotel-carousel" class="hotel-carousel">
        <div class="hotel-slide">
          <div class="card">
            <img src="${fallbackImageUrl}" alt="Hébergement de luxe" onerror="this.src='https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=200&fit=crop&crop=center'">
            <div class="card-content">
              <h3>🏨 Hébergements Recommandés</h3>
              <div class="type-badge" style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem; display: inline-block; margin-bottom: 8px;">Premium</div>
              <p>Découvrez notre sélection d'hébergements de qualité pour un séjour inoubliable.</p>
              <div class="price" style="color: #27ae60; font-weight: 600; font-size: 1.1rem;">💰 Prix sur demande</div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  const slidesPromises = accommodations.map(async (accommodation, index) => {
    const name = accommodation.name || `Hébergement ${index + 1}`;
    const description = accommodation.description || 'Hébergement de qualité';
    const price = accommodation.priceRange || 'Prix sur demande';
    const rating = accommodation.rating || '';
    const type = accommodation.type || 'Hotel';
    const amenities = accommodation.amenities
      ? accommodation.amenities.slice(0, 3).join(', ')
      : '';
    const location = accommodation.location || '';

    const imageUrl = await getHotelImageUrl(name, destination);

    return `
      <div class="hotel-slide">
        <div class="card">
          <img src="${imageUrl}" alt="${name}" onerror="this.src='https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=200&fit=crop&crop=center'">
          <div class="card-content">
            <h3>🏨 ${name}</h3>
            <div class="type-badge" style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem; display: inline-block; margin-bottom: 8px;">${type}</div>
            <p>${description}</p>
            ${location ? `<div class="location" style="color: #666; font-size: 0.9rem; margin: 8px 0;">📍 ${location}</div>` : ''}
            ${rating ? `<div class="rating" style="color: #f39c12; margin: 8px 0;">⭐ ${rating}</div>` : ''}
            ${amenities ? `<div class="amenities" style="color: #666; font-size: 0.9rem; margin: 8px 0;">🏨 ${amenities}</div>` : ''}
            <div class="price" style="color: #27ae60; font-weight: 600; font-size: 1.1rem;">💰 ${price}</div>
          </div>
        </div>
      </div>
    `;
  });

  const slides = await Promise.all(slidesPromises);

  return `
    <div id="hotel-carousel" class="hotel-carousel">
      ${slides.join('')}
    </div>
  `;
}

/**
 * Generate the HTML output from the comprehensive trip plan
 */
async function generateAdvancedTripPlanHtml(
  tripPlan: ComprehensiveTripPlan,
): Promise<AdvancedTripPlanningOutput> {
  try {
    console.log(
      'Generating HTML for advanced trip plan:',
      tripPlan.destination.destination,
    );

    // Generate the HTML content using the model
    const { object } = await generateObject({
      model: myProvider.languageModel('artifact-model'),
      system: `You are an expert web developer specializing in creating beautiful, interactive travel itineraries.
      Create a visually stunning HTML travel handbook for ${tripPlan.destination.destination} for ${tripPlan.destination.duration} days.
      Include all the provided information in a well-structured, interactive format with tabs for different sections.

      IMPORTANT: You MUST include Leaflet map library to display all points of interest. Add these lines in your HTML:
      <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
      <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

      HERO IMAGE: The trip plan includes a heroImage URL. You MUST use this image as the main hero image at the top of the page.
      If the heroImage fails to load, provide a fallback using Unsplash like this:
      <img src="\${tripPlan.heroImage}" alt="Destination" onerror="this.onerror=null; this.src='https://source.unsplash.com/1600x900/?${encodeURIComponent(`${tripPlan.destination.destination} ${tripPlan.destination.country}`)}'" />

      VISUAL CUSTOMIZATION: The trip plan includes a visualTemplate object with detailed styling information. You MUST use these styles to customize the appearance of the page:
      - Use the colors specified in visualTemplate.colors for the color scheme
      - Use the fonts specified in visualTemplate.fonts for typography
      - Follow the layout instructions in visualTemplate.layout
      - Use the specified icon style in visualTemplate.icons
      - Apply the image styling from visualTemplate.images
      - Include animations as specified in visualTemplate.animations
      - Add special elements as specified in visualTemplate.specialElements
      - Include the CSS from visualTemplate.css

      TRIP TYPE ADAPTATION: The trip plan includes a tripClassification object that indicates the primary type of trip (${tripPlan.tripClassification?.primaryType || 'general'}).
      Adapt the content and presentation based on this classification:
      - For cycling trips: Emphasize cycling routes, bike rental information, and elevation profiles
      - For hiking trips: Highlight trail information, difficulty levels, and required gear
      - For beach trips: Focus on beach information, water activities, and beach facilities
      - For cultural trips: Emphasize museums, historical sites, and cultural experiences
      - For food trips: Highlight dining options, local cuisine, and food experiences

      SPECIALIZED ACTIVITY DATA: If the trip includes specializedActivityData, prominently feature this information in dedicated sections.

      Make sure the HTML is responsive and works well on all devices.

      This is an ADVANCED trip plan with comprehensive information. Include ALL of the following sections:
      - Hero image with destination name and duration
      - Interactive map with all points of interest
      - Day-by-day itinerary with detailed activities
      - Accommodation recommendations with details and map locations (MUST use carousel with ID "hotel-carousel")
      - Dining options categorized by meal type (MUST use carousel with ID "restaurant-carousel")
      - Local recommendations including hidden gems and local events (MUST use carousel with ID "poi-carousel")
      - Practical information including transportation, weather, emergency info
      - Travel tips and local phrases
      - Budget information

      ⚠️ CRITICAL CAROUSEL REQUIREMENTS - ABSOLUTELY MANDATORY - NO EXCEPTIONS ⚠️
      For Accommodations, Dining Options, and Local Recommendations sections, you MUST implement carousels with EXACTLY this structure.
      DO NOT USE SIMPLE TEXT LISTS OR PARAGRAPHS. ONLY USE CAROUSELS:

      RESTAURANTS SECTION:
      <div id="restaurant-carousel" class="restaurant-carousel">
        <div class="restaurant-slide">
          <div class="card">
            <img src="[restaurant-image]" alt="[restaurant-name]">
            <div class="card-content">
              <h3>[restaurant-name]</h3>
              <p>[restaurant-description]</p>
              <div class="rating">[rating]</div>
            </div>
          </div>
        </div>
        <!-- Repeat for each restaurant -->
      </div>

      HOTELS SECTION:
      <div id="hotel-carousel" class="hotel-carousel">
        <div class="hotel-slide">
          <div class="card">
            <img src="[hotel-image]" alt="[hotel-name]">
            <div class="card-content">
              <h3>[hotel-name]</h3>
              <p>[hotel-description]</p>
              <div class="price">[price]</div>
            </div>
          </div>
        </div>
        <!-- Repeat for each hotel -->
      </div>

      POI SECTION:
      <div id="poi-carousel" class="poi-carousel">
        <div class="poi-slide">
          <div class="card">
            <img src="[poi-image]" alt="[poi-name]">
            <div class="card-content">
              <h3>[poi-name]</h3>
              <p>[poi-description]</p>
              <div class="type">[poi-type]</div>
            </div>
          </div>
        </div>
        <!-- Repeat for each POI -->
      </div>

      ⚠️ ABSOLUTELY CRITICAL ⚠️: DO NOT use simple lists, paragraphs, or text for accommodations, restaurants, or POI.
      ONLY use the carousel structure above. NO EXCEPTIONS. NO SIMPLE LISTS ALLOWED.
      The JavaScript will automatically initialize these carousels

      Use tabs or accordion sections to organize the content in a user-friendly way.
      Make the design visually appealing with appropriate colors, fonts, and spacing.
      Include interactive elements like hover effects, tooltips, and collapsible sections.

      IMPORTANT: Your response MUST be a valid JSON object with exactly these three properties:
      - htmlContent: The HTML structure of the page (string)
      - cssContent: The CSS styling for the page (string)
      - jsContent: The JavaScript code for interactivity (string)

      SIDEBAR IMAGES: When a user clicks on a location name, a sidebar should open with images of that location.
      Make sure to properly format the location data as a valid JSON string in the data-location attribute.
      Include imageUrl or imageUrls in the location data to display images in the sidebar.
      Use proper error handling for image loading with fallbacks to Unsplash.`,
      prompt: `Create a comprehensive travel itinerary for ${tripPlan.destination.destination}, ${tripPlan.destination.country} with the following data:

${JSON.stringify(tripPlan, null, 2)}

IMPORTANT: Make sure to include a proper Leaflet map initialization in your JavaScript code. The map should display all points of interest with markers. Make sure to include the Leaflet CSS and JavaScript libraries in your HTML.

SPECIALIZED CONTENT: This trip has been classified as a "${tripPlan.tripClassification?.primaryType || 'general'}" trip.
${
  tripPlan.tripClassification?.primaryType === 'cycling'
    ? 'Include dedicated sections for cycling routes with elevation profiles, bike rental information, and cycling-specific tips. Use appropriate cycling icons for map markers.'
    : ''
}
${
  tripPlan.tripClassification?.primaryType === 'hiking'
    ? 'Include dedicated sections for hiking trails with difficulty information, elevation profiles, and required gear. Use appropriate hiking icons for map markers.'
    : ''
}
${
  tripPlan.tripClassification?.primaryType === 'beach'
    ? 'Include dedicated sections for beaches with facilities information, water activities, and beach-specific tips. Use appropriate beach icons for map markers.'
    : ''
}

VISUAL STYLING: Apply the visual styling specified in the visualTemplate object. Use the colors, fonts, layout, and other styling elements provided.

Your HTML should include:
- A hero image section at the top with a large, beautiful image of the destination (use the heroImage URL provided in the data)
- A header with the destination name and duration overlaid on the hero image
- An interactive map section with a div element that has an ID for the map
- A day-by-day itinerary section with detailed activities
- Accommodation recommendations with details and map locations (MUST use carousel with ID "hotel-carousel" and slides with class "hotel-slide")
- Dining options categorized by meal type (MUST use carousel with ID "restaurant-carousel" and slides with class "restaurant-slide")
- Local recommendations including hidden gems and local events (MUST use carousel with ID "poi-carousel" and slides with class "poi-slide")
- Practical information including transportation, weather, emergency info
- Travel tips and local phrases
- Budget information
${tripPlan.specializedActivityData ? '- Specialized sections for the primary activity type with detailed information' : ''}

⚠️ MANDATORY CAROUSEL HTML STRUCTURE - ABSOLUTELY CRITICAL - NO EXCEPTIONS ⚠️
You MUST use carousels for restaurants, hotels, and POI. NEVER USE SIMPLE LISTS, PARAGRAPHS, OR TEXT.
ONLY CAROUSELS ARE ALLOWED FOR THESE SECTIONS.

EXAMPLE RESTAURANT CAROUSEL (MANDATORY):
<div id="restaurant-carousel" class="restaurant-carousel">
  <div class="restaurant-slide">
    <div class="card">
      <img src="https://source.unsplash.com/400x200/?restaurant,paris" alt="Le Comptoir du Relais">
      <div class="card-content">
        <h3>Le Comptoir du Relais</h3>
        <p>Traditional French bistro in Saint-Germain</p>
        <div class="rating">⭐⭐⭐⭐⭐ 4.5/5</div>
      </div>
    </div>
  </div>
  <div class="restaurant-slide">
    <div class="card">
      <img src="https://source.unsplash.com/400x200/?restaurant,french" alt="L'Ami Jean">
      <div class="card-content">
        <h3>L'Ami Jean</h3>
        <p>Cozy bistro with excellent wine selection</p>
        <div class="rating">⭐⭐⭐⭐ 4.2/5</div>
      </div>
    </div>
  </div>
</div>

EXAMPLE HOTEL CAROUSEL (MANDATORY):
<div id="hotel-carousel" class="hotel-carousel">
  <div class="hotel-slide">
    <div class="card">
      <img src="https://source.unsplash.com/400x200/?hotel,paris" alt="Hotel des Grands Boulevards">
      <div class="card-content">
        <h3>Hotel des Grands Boulevards</h3>
        <p>Boutique hotel in the heart of Paris</p>
        <div class="price">€180/night</div>
      </div>
    </div>
  </div>
</div>

EXAMPLE POI CAROUSEL (MANDATORY):
<div id="poi-carousel" class="poi-carousel">
  <div class="poi-slide">
    <div class="card">
      <img src="https://source.unsplash.com/400x200/?eiffel,tower" alt="Eiffel Tower">
      <div class="card-content">
        <h3>Eiffel Tower</h3>
        <p>Iconic iron lattice tower and symbol of Paris</p>
        <div class="type">Monument</div>
      </div>
    </div>
  </div>
</div>

⚠️ CRITICAL - FINAL WARNING ⚠️: Always use this carousel structure.
NEVER EVER use simple lists, paragraphs, or plain text for restaurants, hotels, or POI sections.
IF YOU USE SIMPLE LISTS INSTEAD OF CAROUSELS, THE OUTPUT WILL BE REJECTED.

LOCATION SIDEBAR: When a user clicks on a location name, a sidebar should open with images of that location.
- Make all location names clickable with an onclick attribute that calls openLocationDetail(this)
- Format the location data as a valid JSON string in the data-location attribute
- Include imageUrl or imageUrls in the location data to display images in the sidebar
- Make sure the JSON is properly escaped to avoid conflicts with HTML attributes
- Example: <span class="location-name" onclick="openLocationDetail(this)" data-location='{"name":"Eiffel Tower","type":"attraction","imageUrl":"https://example.com/image.jpg"}'>Eiffel Tower</span>

IMPORTANT: For each location mentioned in the itinerary:
1. Make the location name a clickable span with class="location-name"
2. Add onclick="openLocationDetail(this)" to the span
3. Add a data-location attribute with a JSON object containing:
   - name: The name of the location
   - type: The type of location (attraction, restaurant, hotel, etc.)
   - description: A description of the location
   - imageUrl: A direct URL to an image of the location (if available)
   - OR imageUrls: An array of image URLs for the location (if available)
4. If you don't have specific image URLs, the system will automatically fetch images from Unsplash based on the location name

The JavaScript should initialize the Leaflet map and add markers for all points of interest, with different marker styles based on the point type.

MANDATORY JAVASCRIPT FOR CAROUSELS:
The system will automatically inject the complete carousel JavaScript classes.
You do NOT need to include carousel JavaScript in your jsContent - it will be added automatically.
Focus on your map initialization and other interactive features.

Use tabs or accordion sections to organize the content in a user-friendly way.`,
      schema: z.object({
        htmlContent: z.string().describe('HTML structure of the page'),
        cssContent: z.string().describe('CSS styling for the page'),
        jsContent: z.string().describe('JavaScript code for interactivity'),
      }),
      temperature: 0.7, // Add some creativity but not too much
      maxTokens: 4000, // Ensure we have enough tokens for a complete response
    });

    // Validate that we have all required properties
    const typedObject = object as AdvancedTripPlanningOutput;
    if (
      !typedObject.htmlContent ||
      !typedObject.cssContent ||
      !typedObject.jsContent
    ) {
      throw new Error('Generated HTML is missing required properties');
    }

    // Intelligently convert content to carousels using LLM
    console.log('Applying intelligent carousel conversion...');
    typedObject.htmlContent = await intelligentCarouselConversion(
      typedObject.htmlContent,
      tripPlan.destination.destination,
    );

    // Force carousel creation if sections are missing or contain [object Promise]
    typedObject.htmlContent = await forceCarouselCreation(
      typedObject.htmlContent,
      tripPlan,
    );

    // Ensure carousel CSS and JavaScript are included
    typedObject.cssContent = `${typedObject.cssContent}\n\n${CAROUSEL_CSS}`;
    typedObject.jsContent = `${typedObject.jsContent}\n\n${CAROUSEL_JAVASCRIPT}`;

    return typedObject;
  } catch (error) {
    console.error('Error generating HTML from advanced trip plan:', error);

    // Create a fallback HTML if generation fails
    return await createAdvancedFallbackHtml(tripPlan);
  }
}

/**
 * Main function that orchestrates the advanced trip planning workflow
 */
export async function generateAdvancedTripPlan(
  input: AdvancedTripPlanningInput,
): Promise<AdvancedTripPlanningOutput> {
  const { query, dataStream } = input;

  try {
    // Send initial update to the client
    sendProgressUpdate(dataStream, 'Analyzing your travel request...', 5, {
      query,
    });

    // 0. Validate the travel request first
    const validationAgent = new ValidationAgent(
      myProvider.languageModel('artifact-model'),
    );

    sendProgressUpdate(dataStream, 'Validating your travel request...', 8, {
      query,
    });

    const validationResult: ValidationResult =
      await validationAgent.validateTravelRequest(query);

    console.log(
      'Validation result:',
      JSON.stringify(validationResult, null, 2),
    );

    // If validation fails, ask for clarification
    if (!validationResult.shouldProceed || validationResult.confidence < 0.7) {
      console.log('Validation failed, generating clarifying questions...');

      const clarifyingQuestion =
        await validationAgent.generateClarifyingQuestions(
          query,
          validationResult.missingCritical,
        );

      // Send clarifying question to user
      dataStream.writeMessageAnnotation({
        type: 'clarification-request',
        question: clarifyingQuestion.question,
        suggestions: clarifyingQuestion.suggestions || [],
        confidence: validationResult.confidence,
        reasoning: validationResult.reasoning,
      });

      // Return early with a request for more information
      return await generateClarificationHtml(
        query,
        clarifyingQuestion,
        validationResult,
      );
    }

    // 1. Extract destination information
    sendProgressUpdate(
      dataStream,
      'Extracting destination information...',
      10,
      {
        query,
      },
    );

    const destinationAgent = new DestinationAgent(
      myProvider.languageModel('artifact-model'),
    );
    const destinationInfo =
      await destinationAgent.extractDestinationInfo(query);

    // Send update about destination research
    sendProgressUpdate(
      dataStream,
      `Researching ${destinationInfo.destination}, ${destinationInfo.country}...`,
      15,
      destinationInfo,
    );

    // 2. Classify the trip request to identify specific activities
    const classifierAgent = new ClassifierAgent(
      myProvider.languageModel('artifact-model'),
    );
    const tripClassification = await classifierAgent.classifyTripRequest(query);

    // Log the classification for debugging
    console.log(
      'Trip classification:',
      JSON.stringify(tripClassification, null, 2),
    );

    // Send update about trip classification
    sendProgressUpdate(
      dataStream,
      `Analyzing your travel preferences and activities...`,
      20,
      {
        destination: destinationInfo,
        tripType: tripClassification.primaryType,
        activities: tripClassification.keywords,
      },
    );

    // 3. Orchestrate the trip planning process
    const orchestratorAgent = new OrchestratorAgent(
      myProvider.languageModel('artifact-model'),
    );

    // Create a progress update function for the orchestrator
    const orchestratorProgressUpdate = (
      message: string,
      progress: number,
      data: any,
    ) => {
      // Scale the progress to be between 25 and 85
      const scaledProgress = 25 + (progress / 100) * 60;
      sendProgressUpdate(dataStream, message, scaledProgress, data);
    };

    // Check if duration is specified, if not, ask the user
    if (destinationInfo.duration === null) {
      // Send a message to the user asking for duration
      dataStream.writeMessageAnnotation({
        type: 'duration-request',
        destination: destinationInfo.destination,
        message: `Pour combien de jours souhaitez-vous planifier votre voyage à ${destinationInfo.destination} ? Veuillez spécifier un nombre de jours.`,
      });

      // Set a default duration for now (this will be replaced by the user's input in a real implementation)
      destinationInfo.duration = 5;

      // Log that we're using a default duration
      console.log(
        'No duration specified by user. Using default duration of 5 days.',
      );
      console.log(
        'In a real implementation, we would wait for user input here.',
      );
    }

    // Orchestrate the trip planning process with classification information
    const comprehensiveTripPlan =
      await orchestratorAgent.orchestrateTripPlanning(
        query,
        destinationInfo,
        orchestratorProgressUpdate,
        tripClassification, // Pass the classification to the orchestrator
      );

    // Get a hero image for the destination
    sendProgressUpdate(
      dataStream,
      'Finding beautiful images of your destination...',
      85,
      destinationInfo,
    );

    const heroImage = await getDestinationHeroImage(
      destinationInfo.destination,
      destinationInfo.country,
    );

    // Add the hero image to the trip plan
    comprehensiveTripPlan.heroImage = heroImage || undefined;

    // Generate the final HTML output
    sendProgressUpdate(
      dataStream,
      'Creating your interactive travel guide...',
      90,
      {
        status: 'generating_html',
      },
    );

    const finalHtml = await generateAdvancedTripPlanHtml(comprehensiveTripPlan);

    // Send final progress update
    sendProgressUpdate(dataStream, 'Finalizing your travel handbook...', 95, {
      status: 'almost_complete',
    });

    // CRITICAL: Send the final HTML content to the dataStream
    console.log('Sending final HTML content to dataStream...');
    const finalContent = JSON.stringify({
      htmlContent: finalHtml.htmlContent,
      cssContent: finalHtml.cssContent,
      jsContent: finalHtml.jsContent,
    });

    dataStream.writeData({
      type: 'html-delta',
      content: finalContent,
    });

    // Send completion update immediately (no setTimeout to avoid async issues)
    sendProgressUpdate(dataStream, 'Your travel adventure is ready!', 100, {
      status: 'complete',
      destination: destinationInfo.destination,
      country: destinationInfo.country,
    });

    // Send explicit completion signal to stop any further processing
    dataStream.writeData({
      type: 'completion',
      content: 'workflow_complete',
    });

    console.log('Advanced trip planning workflow completed successfully');
    return finalHtml;
  } catch (error) {
    console.error('Error in advanced trip planning workflow:', error);

    // Return a fallback HTML in case of error
    return await generateErrorHtml(query, error);
  }
}

// Variables globales pour la protection contre les boucles infinies
let lastProgressUpdateTime = 0;
let progressUpdateCallCount = 0;

/**
 * Helper function to send progress updates to the client
 */
function sendProgressUpdate(
  dataStream: any,
  message: string,
  progress: number,
  data: any,
) {
  if (!dataStream) return;

  // Protection contre les appels trop fréquents
  const now = Date.now();

  // Limiter les mises à jour à une par seconde maximum
  if (now - lastProgressUpdateTime < 1000) {
    console.log('Throttling progress update, too many calls');
    return;
  }

  // Mettre à jour le temps de la dernière mise à jour
  lastProgressUpdateTime = now;

  // Protection contre les boucles infinies
  // Limiter le nombre total d'appels
  progressUpdateCallCount++;
  if (progressUpdateCallCount > 100) {
    console.error(
      'Too many progress updates (>100), possible infinite loop detected',
    );
    return;
  }

  // Determine the current stage based on progress
  const getStageIcon = (currentProgress: number) => {
    if (currentProgress < 20) return '✈️'; // Planning stage
    if (currentProgress < 40) return '🗺️'; // Destination research
    if (currentProgress < 60) return '📍'; // Points of interest
    if (currentProgress < 80) return '📝'; // Itinerary creation
    return '📸'; // Finalizing
  };

  // Get a background image based on the destination if available
  const destinationName = data?.destination?.destination || '';
  const countryName = data?.destination?.country || '';
  const backgroundImageUrl =
    destinationName && countryName
      ? `https://source.unsplash.com/1600x900/?${encodeURIComponent(`${destinationName} ${countryName} travel`)}`
      : 'https://source.unsplash.com/1600x900/?travel';

  const htmlContent = `
    <div id="app" class="min-h-screen flex flex-col items-center justify-center relative overflow-hidden">
      <!-- Background image with parallax effect -->
      <div class="absolute inset-0 z-0">
        <img src="${backgroundImageUrl}" class="w-full h-full object-cover" style="filter: blur(8px); transform: scale(1.1);" />
        <div class="absolute inset-0 bg-black bg-opacity-50"></div>
      </div>

      <!-- Content container -->
      <div class="relative z-10 max-w-3xl w-full mx-auto p-8 rounded-xl bg-white bg-opacity-90 shadow-2xl">
        <div class="text-center">
          <div class="flex justify-center mb-6">
            <div class="text-5xl animate-bounce">${getStageIcon(progress)}</div>
          </div>

          <h1 class="text-3xl font-bold mb-4 text-gray-800">Creating Your Travel Adventure</h1>
          <p class="text-xl mb-8 text-gray-700">${message}</p>

          <!-- Animated progress bar -->
          <div class="relative pt-1 mb-6">
            <div class="flex mb-2 items-center justify-between">
              <div>
                <span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-blue-600 bg-blue-200">
                  Progress
                </span>
              </div>
              <div class="text-right">
                <span class="text-xs font-semibold inline-block text-blue-600">
                  ${progress}%
                </span>
              </div>
            </div>
            <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-blue-200">
              <div style="width:${progress}%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-500 ease-in-out"></div>
            </div>
          </div>

          <!-- Travel preparation steps -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="p-4 rounded-lg ${progress >= 30 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'}">
              <div class="text-2xl mb-2">${progress >= 30 ? '✓' : '🔍'}</div>
              <h3 class="font-bold">Destination Research</h3>
            </div>
            <div class="p-4 rounded-lg ${progress >= 60 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'}">
              <div class="text-2xl mb-2">${progress >= 60 ? '✓' : '📋'}</div>
              <h3 class="font-bold">Itinerary Planning</h3>
            </div>
            <div class="p-4 rounded-lg ${progress >= 90 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'}">
              <div class="text-2xl mb-2">${progress >= 90 ? '✓' : '🎨'}</div>
              <h3 class="font-bold">Final Touches</h3>
            </div>
          </div>

          <!-- Fun travel fact -->
          <div class="mt-6 p-4 bg-blue-50 rounded-lg text-left">
            <h4 class="font-bold text-blue-800">Did you know?</h4>
            <p class="text-blue-700" id="travel-fact">Loading interesting travel fact...</p>
          </div>
        </div>
      </div>
    </div>
  `;

  // Add CSS for animations and styling
  const cssContent = `
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    @keyframes float {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-10px); }
    }

    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f8fafc;
      color: #334155;
      min-height: 100vh;
    }

    .animate-bounce {
      animation: float 2s ease-in-out infinite;
    }

    .transition-all {
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 300ms;
    }

    .duration-500 {
      transition-duration: 500ms;
    }

    .ease-in-out {
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
  `;

  // Add JavaScript for dynamic content and animations
  const jsContent = `
    console.log("Progress update: ${progress}%");

    // Array of interesting travel facts
    const travelFacts = [
      "The world's largest hotel is in Saudi Arabia with 10,000 rooms.",
      "France is the most visited country in the world with over 89 million annual tourists.",
      "The shortest international flight lasts only 8 minutes between Switzerland and Germany.",
      "Japan has more than 6,800 islands.",
      "There are 195 countries in the world recognized by the United Nations.",
      "The Great Wall of China is not visible from space with the naked eye.",
      "Venice, Italy is built on 118 small islands connected by about 400 bridges.",
      "The world's oldest hotel has been operating since 705 AD in Japan.",
      "Singapore's Changi Airport has a butterfly garden with over 1,000 butterflies.",
      "The Maldives is the lowest country on Earth with an average ground level of 1.5 meters above sea level."
    ];

    // Display a random travel fact
    document.getElementById('travel-fact').textContent = travelFacts[Math.floor(Math.random() * travelFacts.length)];

    // Add subtle animation to the background
    const bgImage = document.querySelector('#app > div:first-child img');
    if (bgImage) {
      let scale = 1.1;
      let direction = 0.0001;

      setInterval(() => {
        scale += direction;
        if (scale > 1.15) direction = -0.0001;
        if (scale < 1.1) direction = 0.0001;
        bgImage.style.transform = \`scale(\${scale})\`;
      }, 50);
    }
  `;

  const content = JSON.stringify({
    htmlContent,
    cssContent,
    jsContent,
  });

  dataStream.writeData({
    type: 'html-delta',
    content,
  });
}

/**
 * Create a fallback HTML for an advanced trip plan when generation fails
 */
async function createAdvancedFallbackHtml(
  tripPlan: ComprehensiveTripPlan,
): Promise<AdvancedTripPlanningOutput> {
  const { destination, country, duration } = tripPlan.destination;

  // Create a simple but functional travel itinerary
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Trip to ${destination}, ${country}</title>
      <!-- Leaflet CSS -->
      <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
      <!-- Leaflet JavaScript -->
      <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    </head>
    <body>
    <script>
      // Function to open location detail panel - defined globally so it's available immediately
      function openLocationDetail(element) {
        console.log('openLocationDetail called with element:', element);

        try {
          // Show the sheet and overlay immediately
          const locationSheet = document.getElementById('location-sheet');
          const locationSheetOverlay = document.getElementById('location-sheet-overlay');

          if (!locationSheet || !locationSheetOverlay) {
            console.error('Location sheet elements not found');
            return;
          }

          // Show the sheet and overlay immediately
          locationSheet.classList.add('open');
          locationSheetOverlay.classList.add('open');

          // Get location data from the element
          let locationData;
          try {
            locationData = JSON.parse(element.getAttribute('data-location'));
            console.log('Location data:', locationData);
          } catch (e) {
            console.error('Error parsing location data:', e);
            locationData = {
              name: element.textContent || 'Unknown location',
              type: 'attraction'
            };
          }

          // Get DOM elements
          const locationMainImage = document.getElementById('location-main-image');
          const locationThumbnails = document.getElementById('location-thumbnails');
          const locationDescription = document.getElementById('location-description');
          const locationInfo = document.getElementById('location-info');
          const locationTitle = document.querySelector('.location-sheet-title');

          // Update the title
          if (locationTitle) {
            locationTitle.textContent = locationData.name || 'Location Details';
          }

          // Update the description
          if (locationDescription) {
            locationDescription.textContent = locationData.description || 'No description available.';
          }

          // Update the info
          if (locationInfo) {
            let infoHTML = '';
            if (locationData.type) {
              infoHTML += '<div><strong>Type:</strong> ' + (locationData.type.charAt(0).toUpperCase() + locationData.type.slice(1)) + '</div>';
            }
            if (locationData.day) {
              infoHTML += '<div><strong>Day:</strong> ' + locationData.day + '</div>';
            }
            if (locationData.time) {
              infoHTML += '<div><strong>Time:</strong> ' + locationData.time + '</div>';
            }
            if (locationData.address) {
              infoHTML += '<div><strong>Address:</strong> ' + locationData.address + '</div>';
            }
            locationInfo.innerHTML = infoHTML || 'No additional information available.';
          }

          // Set main image with fallbacks
          if (locationMainImage) {
            // Create a function to handle image loading with fallbacks
            const setupImageWithFallbacks = (initialSrc) => {
              // Reset opacity for transition effect
              locationMainImage.style.opacity = '0';

              // Set a timeout to show the image even if events don't fire
              const showImageTimeout = setTimeout(() => {
                locationMainImage.style.opacity = '1';
              }, 1000);

              // Setup onload event
              locationMainImage.onload = function() {
                clearTimeout(showImageTimeout);
                console.log('Image loaded successfully:', locationMainImage.src);
                locationMainImage.style.opacity = '1';

                // Hide loading indicator
                const container = locationMainImage.parentElement;
                if (container) {
                  container.classList.add('image-loaded');
                }
              };

              // Setup onerror event with multiple fallback levels
              locationMainImage.onerror = function() {
                clearTimeout(showImageTimeout);
                console.error('Failed to load image:', locationMainImage.src);

                // Check if we've already tried with a random parameter
                if (!locationMainImage.src.includes('random=')) {
                  // Try with Unsplash and a random parameter
                  const searchTerm = encodeURIComponent(locationData.name + ' ' + (locationData.type || 'attraction'));
                  const randomParam = Math.random();
                  console.log('Using fallback image with random param:', randomParam);
                  locationMainImage.src = 'https://source.unsplash.com/800x600/?' + searchTerm + '&random=' + randomParam;
                } else {
                  // If even with random parameter it fails, use a placeholder SVG
                  locationMainImage.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='600' viewBox='0 0 800 600'%3E%3Crect width='800' height='600' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='24' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3E" + encodeURIComponent(locationData.name || 'Location') + "%3C/text%3E%3C/svg%3E";
                  locationMainImage.onerror = null; // Disable future attempts
                  locationMainImage.style.opacity = '1';
                }
              };

              // Set initial source
              locationMainImage.src = initialSrc;
            };

            // Determine initial image source
            let initialSrc;

            // First try to use provided imageUrl
            if (locationData.imageUrl) {
              initialSrc = locationData.imageUrl;
            }
            // Then try first image from imageUrls array if available
            else if (locationData.imageUrls && Array.isArray(locationData.imageUrls) && locationData.imageUrls.length > 0) {
              initialSrc = locationData.imageUrls[0];
            }
            // Finally, use Unsplash with random parameter
            else {
              const searchTerm = encodeURIComponent(locationData.name + ' ' + (locationData.type || 'attraction'));
              const randomParam = Math.random();
              initialSrc = 'https://source.unsplash.com/800x600/?' + searchTerm + '&random=' + randomParam;
            }

            // Setup image with initial source and fallbacks
            setupImageWithFallbacks(initialSrc);
          }

          // Set thumbnails with fallbacks
          if (locationThumbnails) {
            // Clear existing thumbnails
            locationThumbnails.innerHTML = '';

            // Create thumbnails
            const thumbnailCount = 3;
            const thumbnailTerms = [
              locationData.name + ' ' + (locationData.type || 'attraction'),
              locationData.name + ' landmark',
              locationData.name + ' tourism'
            ];

            // Use provided imageUrls if available
            const imageUrls = locationData.imageUrls || [];
            const totalImages = Math.max(imageUrls.length, thumbnailCount);

            for (let i = 0; i < totalImages; i++) {
              const thumbnail = document.createElement('div');
              thumbnail.className = 'location-sheet-thumbnail' + (i === 0 ? ' active' : '');

              const img = document.createElement('img');

              // Function to setup image with fallbacks
              const setupThumbnailWithFallbacks = (initialSrc) => {
                // Setup onload event
                img.onload = function() {
                  console.log('Thumbnail loaded successfully:', img.src);
                  // Add class to parent (thumbnail) to indicate image is loaded
                  thumbnail.classList.add('thumb-loaded');
                };

                // Setup onerror event with multiple fallback levels
                img.onerror = function() {
                  console.error('Failed to load thumbnail:', img.src);

                  // Check if we've already tried with a random parameter
                  if (!img.src.includes('random=')) {
                    // Try with Unsplash and a different random parameter
                    const fallbackTerm = encodeURIComponent(locationData.name);
                    const randomParam = Math.random();
                    console.log('Using fallback thumbnail with random param:', randomParam);
                    img.src = 'https://source.unsplash.com/100x100/?' + fallbackTerm + '&random=' + randomParam;
                  } else {
                    // If even with random parameter it fails, use a placeholder SVG
                    img.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Crect width='60' height='60' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='10' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3ENo image%3C/text%3E%3C/svg%3E";
                    img.onerror = null; // Disable future attempts
                  }
                };

                // Set initial source
                img.src = initialSrc;
              };

              // Determine initial image source
              let initialSrc;

              // Use imageUrls if available
              if (i < imageUrls.length) {
                initialSrc = imageUrls[i];
              } else {
                // Otherwise use Unsplash with random parameter
                const searchTerm = encodeURIComponent(thumbnailTerms[i % thumbnailTerms.length] || locationData.name);
                const randomParam = Math.random();
                initialSrc = 'https://source.unsplash.com/100x100/?' + searchTerm + '&random=' + randomParam;
              }

              img.alt = locationData.name + ' image ' + (i+1);

              // Setup image with initial source and fallbacks
              setupThumbnailWithFallbacks(initialSrc);

              // Add click event to change main image
              thumbnail.addEventListener('click', function() {
                // Update active state
                document.querySelectorAll('.location-sheet-thumbnail').forEach(thumb => {
                  thumb.classList.remove('active');
                });
                thumbnail.classList.add('active');

                // Update main image with same fallback approach
                if (locationMainImage) {
                  // Reset opacity for transition effect
                  locationMainImage.style.opacity = '0';

                  // Set a timeout to show the image even if events don't fire
                  const showImageTimeout = setTimeout(() => {
                    locationMainImage.style.opacity = '1';
                  }, 1000);

                  // Setup onload event
                  locationMainImage.onload = function() {
                    clearTimeout(showImageTimeout);
                    console.log('Main image loaded successfully from thumbnail:', locationMainImage.src);
                    locationMainImage.style.opacity = '1';
                  };

                  // Setup onerror event
                  locationMainImage.onerror = function() {
                    clearTimeout(showImageTimeout);
                    console.error('Failed to load main image from thumbnail:', locationMainImage.src);

                    // If image fails to load, try with random parameter
                    if (!locationMainImage.src.includes('random=')) {
                      const searchTerm = encodeURIComponent(locationData.name + ' ' + (locationData.type || 'attraction'));
                      const randomParam = Math.random();
                      console.log('Using fallback main image with random param:', randomParam);
                      locationMainImage.src = 'https://source.unsplash.com/800x600/?' + searchTerm + '&random=' + randomParam;
                    } else {
                      // If even with random parameter it fails, use a placeholder SVG
                      locationMainImage.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='600' viewBox='0 0 800 600'%3E%3Crect width='800' height='600' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='24' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3E" + encodeURIComponent(locationData.name || 'Location') + "%3C/text%3E%3C/svg%3E";
                      locationMainImage.onerror = null; // Disable future attempts
                      locationMainImage.style.opacity = '1';
                    }
                  };

                  // Set source
                  locationMainImage.src = img.src;
                }
              });

              thumbnail.appendChild(img);
              locationThumbnails.appendChild(thumbnail);
            }
          }
        } catch (error) {
          console.error('Error in openLocationDetail:', error);
        }
      }

      // Function to close the location sheet
      function closeLocationSheet() {
        const locationSheet = document.getElementById('location-sheet');
        const locationSheetOverlay = document.getElementById('location-sheet-overlay');

        if (locationSheet && locationSheetOverlay) {
          locationSheet.classList.remove('open');
          locationSheetOverlay.classList.remove('open');
        }
      }
    </script>
    <div id="app">
      <!-- Location Sheet Panel -->
      <div id="location-sheet" class="location-sheet">
        <div class="location-sheet-header">
          <h2 class="location-sheet-title">Location Details</h2>
          <button class="location-sheet-close" aria-label="Close panel" onclick="closeLocationSheet()">&times;</button>
        </div>
        <div class="location-sheet-content">
          <div class="location-sheet-images">
            <div class="location-sheet-image-container">
              <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='200' viewBox='0 0 200 200'%3E%3Crect width='200' height='200' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='14' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3ELoading...%3C/text%3E%3C/svg%3E"
                   alt="Loading location image"
                   class="location-sheet-image"
                   id="location-main-image">
            </div>
            <div class="location-sheet-thumbnails" id="location-thumbnails">
              <div class="location-sheet-thumbnail active">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Crect width='60' height='60' fill='%23f0f0f0'/%3E%3C/svg%3E" alt="Loading thumbnail">
              </div>
              <div class="location-sheet-thumbnail">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Crect width='60' height='60' fill='%23f0f0f0'/%3E%3C/svg%3E" alt="Loading thumbnail">
              </div>
              <div class="location-sheet-thumbnail">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Crect width='60' height='60' fill='%23f0f0f0'/%3E%3C/svg%3E" alt="Loading thumbnail">
              </div>
            </div>
          </div>
          <div class="location-sheet-details">
            <div class="location-sheet-description" id="location-description">
              Loading description...
            </div>
            <div class="location-sheet-info" id="location-info">
              Loading information...
            </div>
          </div>
        </div>
      </div>

      <!-- Overlay for the location sheet -->
      <div id="location-sheet-overlay" class="location-sheet-overlay" onclick="closeLocationSheet()"></div>

      <!-- Hero Image Section -->
      <div class="hero-image-container">
        <img src="${tripPlan.heroImage || `https://source.unsplash.com/1600x900/?${encodeURIComponent(`${destination} ${country}`)}`}"
             alt="${destination}, ${country}"
             onerror="this.onerror=null; this.src='https://source.unsplash.com/1600x900/?${encodeURIComponent(`${destination} ${country}`)}'" />
        <div class="absolute">
          <h1>Your Trip to ${destination}, ${country}</h1>
          <p>${duration} Day Itinerary</p>
        </div>
      </div>

      <div class="p-8">
        <div class="mb-8">
          <h2 class="text-2xl font-bold mb-4">Overview</h2>
          <p>Here's your ${duration}-day travel plan for ${destination}, ${country}. We've included the main attractions, recommended restaurants, and useful travel tips.</p>
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold mb-4">Interactive Map</h2>
          <div id="main-map" data-destination="${destination}, ${country}" class="h-[400px] w-full rounded-lg mb-4 border border-gray-200 shadow-lg"></div>
          <p class="text-sm text-gray-600">The map shows the main points of interest for your trip.</p>
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold mb-4">Day-by-Day Itinerary</h2>
          ${
            // Génération asynchrone avec Tavily pour chaque activité
            (
              await Promise.all(
                tripPlan.days.map(async (day) => {
                  const activitiesHtml = await Promise.all(
                    day.activities.map(async (activity) => {
                      console.log(
                        `[HTML] Génération pour l'activité: ${activity.location}`,
                      );
                      const images = await getLocationImagesWithTavily(
                        activity.location,
                        destination,
                        country,
                      );
                      console.log(
                        `[HTML] Images trouvées pour ${activity.location}:`,
                        images,
                      );

                      const dataLocation = {
                        name: activity.location,
                        type: 'attraction',
                        description: activity.description,
                        day: `Day ${day.day}`,
                        ...(images.length > 0 ? { imageUrls: images } : {}),
                      };
                      console.log(
                        `[HTML] Données de localisation pour ${activity.location}:`,
                        dataLocation,
                      );

                      return `
                  <li>
                    <span class="font-bold">${activity.time}:</span> ${activity.activity} at
                    <span class="location-name"
                          onclick="openLocationDetail(this)"
                          data-location='${JSON.stringify(dataLocation).replace(/'/g, '&apos;')}' >
                      ${activity.location}
                    </span>
                    <p class="text-gray-600 text-sm">${activity.description}</p>
                  </li>
                `;
                    }),
                  );
                  return `
            <div class="mb-6 p-4 border border-gray-200 rounded-lg">
              <h3 class="text-xl font-bold mb-2">Day ${day.day}</h3>
              <ul class="space-y-2">
                ${activitiesHtml.join('')}
              </ul>
            </div>
          `;
                }),
              )
            ).join('')
          }
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold mb-4">Accommodations</h2>
          ${createAccommodationCarousel(tripPlan.accommodationDining?.accommodations?.recommended || [])}
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold mb-4">Dining Options</h2>
          ${createRestaurantCarousel(
            tripPlan.accommodationDining?.dining.dinner
              ?.slice(0, 6)
              .map((restaurant) => ({
                name: restaurant.name,
                description: restaurant.description,
                extra: `${restaurant.cuisine} - ${restaurant.priceRange}`,
              })) || [],
          )}
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold mb-4">Local Recommendations</h2>
          ${createPoiCarousel(
            tripPlan.localRecommendations?.hiddenGems?.map((gem) => ({
              name: gem.name,
              description: gem.description,
              extra: `Best time: ${gem.bestTimeToVisit}`,
              photos: gem.photos || [],
            })) || [],
            tripPlan.destination.destination,
          )}
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold mb-4">Practical Information</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="p-4 border border-gray-200 rounded-lg">
              <h3 class="text-lg font-bold mb-2">Weather</h3>
              <p><strong>Season:</strong> ${tripPlan.practicalInfo?.weather.season || 'Information not available'}</p>
              <p><strong>Temperature:</strong> ${tripPlan.practicalInfo?.weather.averageTemperature || 'Information not available'}</p>
              <p><strong>What to Pack:</strong> ${tripPlan.practicalInfo?.weather.whatToPack.join(', ') || 'Information not available'}</p>
            </div>
            <div class="p-4 border border-gray-200 rounded-lg">
              <h3 class="text-lg font-bold mb-2">Transportation</h3>
              <p><strong>Local Options:</strong> ${tripPlan.practicalInfo?.transportation.localTransportation.map((t: any) => t.type).join(', ') || 'Information not available'}</p>
            </div>
          </div>
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold mb-4">Travel Tips</h2>
          ${tripPlan.travelTips
            .map(
              (tip) => `
            <div class="mb-4">
              <h3 class="text-lg font-bold mb-2">${tip.category}</h3>
              <ul class="list-disc pl-5">
                ${tip.tips.map((t) => `<li>${t}</li>`).join('')}
              </ul>
            </div>
          `,
            )
            .join('')}
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold mb-4">Budget Information</h2>
          <div class="overflow-x-auto">
            <table class="min-w-full border-collapse">
              <thead>
                <tr class="bg-gray-100">
                  <th class="p-2 text-left border border-gray-200">Category</th>
                  <th class="p-2 text-left border border-gray-200">Estimated Cost</th>
                  <th class="p-2 text-left border border-gray-200">Notes</th>
                </tr>
              </thead>
              <tbody>
                ${tripPlan.budget
                  .map(
                    (item) => `
                  <tr>
                    <td class="p-2 border border-gray-200">${item.category}</td>
                    <td class="p-2 border border-gray-200">${item.estimatedCost}</td>
                    <td class="p-2 border border-gray-200">${item.notes}</td>
                  </tr>
                `,
                  )
                  .join('')}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    </body>
    </html>
  `;

  // Basic CSS styling
  const cssContent = `
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.5;
      color: #333;
      margin: 0;
      padding: 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    h1, h2, h3, h4, h5, h6 {
      margin-top: 0;
    }

    table {
      border-collapse: collapse;
      width: 100%;
    }

    th, td {
      text-align: left;
      padding: 8px;
      border: 1px solid #ddd;
    }

    tr:nth-child(even) {
      background-color: #f2f2f2;
    }

    /* Map styles */
    #main-map {
      height: 400px;
      width: 100%;
      z-index: 1;
    }

    /* Ensure Leaflet styles work properly */
    .leaflet-container {
      height: 100%;
      width: 100%;
    }

    /* Hero image styles */
    .hero-image-container {
      position: relative;
      width: 100%;
      height: 50vh;
      overflow: hidden;
    }

    .hero-image-container img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    .hero-image-container:hover img {
      transform: scale(1.05);
    }

    .hero-image-container .absolute {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.4);
      color: white;
      padding: 2rem;
      text-align: center;
    }

    /* Location Sheet Panel Styles */
    .location-sheet {
      position: fixed;
      top: 0;
      right: -100%;
      width: 90%;
      max-width: 450px;
      height: 100%;
      background-color: white;
      box-shadow: -2px 0 10px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      overflow-y: auto;
      transition: right 0.3s ease-in-out;
    }

    .location-sheet.open {
      right: 0;
    }

    .location-sheet-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    }

    .location-sheet-overlay.open {
      opacity: 1;
      visibility: visible;
    }

    .location-sheet-header {
      position: relative;
      padding: 1.5rem;
      border-bottom: 1px solid #eee;
    }

    .location-sheet-close {
      position: absolute;
      top: 1rem;
      right: 1rem;
      width: 32px;
      height: 32px;
      background-color: rgba(255, 255, 255, 0.8);
      border: none;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 1.25rem;
      color: #333;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: background-color 0.2s;
    }

    .location-sheet-close:hover {
      background-color: #f0f0f0;
    }

    .location-sheet-title {
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0;
      padding-right: 2rem;
    }

    .location-sheet-content {
      padding: 1.5rem;
    }

    .location-sheet-images {
      margin-bottom: 1.5rem;
    }

    .location-sheet-image-container {
      position: relative;
      width: 100%;
      height: 250px;
      overflow: hidden;
      border-radius: 8px;
      margin-bottom: 0.5rem;
      background-color: #f0f0f0;
    }

    /* Indicateur de chargement pour les images */
    .location-sheet-image-container::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 30px;
      height: 30px;
      margin: -15px 0 0 -15px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #3f51b5;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      z-index: 1;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Masquer l'indicateur de chargement quand l'image est chargée */
    .location-sheet-image-container.image-loaded::after {
      display: none;
    }

    .location-sheet-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: opacity 0.5s ease;
      background-color: #f0f0f0;
      opacity: 0; /* Commencer avec une opacité de 0 pour l'effet de transition */
    }

    .location-sheet-thumbnails {
      display: flex;
      gap: 0.5rem;
      overflow-x: auto;
      padding-bottom: 0.5rem;
      min-height: 60px;
    }

    .location-sheet-thumbnail {
      width: 60px;
      height: 60px;
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;
      border: 2px solid transparent;
      transition: border-color 0.2s ease;
      flex-shrink: 0;
      background-color: #f0f0f0;
    }

    .location-sheet-thumbnail.active {
      border-color: #3f51b5;
    }

    .location-sheet-thumbnail img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: opacity 0.5s ease;
      opacity: 0.7; /* Légèrement transparent par défaut */
      position: relative;
    }

    /* Effet de chargement pour les miniatures */
    .location-sheet-thumbnail::before {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 15px;
      height: 15px;
      margin: -7.5px 0 0 -7.5px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #3f51b5;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      z-index: 1;
    }

    /* La classe loaded sera ajoutée au thumbnail lui-même */
    .location-sheet-thumbnail.thumb-loaded::before {
      display: none;
    }

    .location-sheet-thumbnail.active img {
      opacity: 1; /* Complètement opaque pour la miniature active */
      border: 2px solid #fff;
      box-shadow: 0 0 0 1px #3f51b5;
    }

    .location-sheet-details {
      margin-top: 1.5rem;
    }

    .location-sheet-description {
      margin-bottom: 1rem;
      line-height: 1.6;
    }

    .location-sheet-info {
      display: grid;
      gap: 0.5rem;
      padding: 1rem;
      background-color: #f9f9f9;
      border-radius: 8px;
      font-size: 0.9rem;
    }

    /* Make location names clickable */
    .location-name {
      color: #3f51b5;
      cursor: pointer;
      font-weight: 600;
      text-decoration: none;
      border-bottom: 1px dashed #3f51b5;
      display: inline-block;
    }

    .location-name:hover {
      color: #1a237e;
      border-bottom-style: solid;
    }
  `;

  // JavaScript for the map
  const jsContent = `
    // Variables globales pour éviter les boucles infinies
    let isClosingLocationSheet = false;
    let isOpeningLocationDetail = false;

    // Fonction pour fermer le panneau de détails de localisation
    function closeLocationSheet() {
      // Éviter les appels récursifs
      if (isClosingLocationSheet) {
        console.warn('closeLocationSheet est déjà en cours d\'exécution, ignorant l\'appel récursif');
        return;
      }

      isClosingLocationSheet = true;

      try {
        const locationSheet = document.getElementById('location-sheet');
        const locationSheetOverlay = document.getElementById('location-sheet-overlay');

        if (locationSheet) {
          locationSheet.classList.remove('open');
        }

        if (locationSheetOverlay) {
          locationSheetOverlay.classList.remove('open');
        }
      } finally {
        // Réinitialiser le drapeau après un court délai
        setTimeout(() => {
          isClosingLocationSheet = false;
        }, 100);
      }
    }

    // Fonction pour ouvrir le panneau de détails de localisation
    function openLocationDetail(element) {
      console.log('openLocationDetail called with element:', element);

      try {
        // Show the sheet and overlay immediately
        const locationSheet = document.getElementById('location-sheet');
        const locationSheetOverlay = document.getElementById('location-sheet-overlay');

        if (!locationSheet || !locationSheetOverlay) {
          console.error('Location sheet elements not found');
          return;
        }

        // Show the sheet and overlay immediately
        locationSheet.classList.add('open');
        locationSheetOverlay.classList.add('open');

        // Get location data from the element
        let locationData;
        try {
          locationData = JSON.parse(element.getAttribute('data-location'));
          console.log('Location data:', locationData);
        } catch (e) {
          console.error('Error parsing location data:', e);
          locationData = {
            name: element.textContent || 'Unknown location',
            type: 'attraction'
          };
        }

        // Get DOM elements
        const locationMainImage = document.getElementById('location-main-image');
        const locationThumbnails = document.getElementById('location-thumbnails');
        const locationDescription = document.getElementById('location-description');
        const locationInfo = document.getElementById('location-info');
        const locationTitle = document.querySelector('.location-sheet-title');

        // Update the title
        if (locationTitle) {
          locationTitle.textContent = locationData.name || 'Location Details';
        }

        // Update the description
        if (locationDescription) {
          locationDescription.textContent = locationData.description || 'No description available.';
        }

        // Update the info
        if (locationInfo) {
          let infoHTML = '';
          if (locationData.type) {
            infoHTML += '<div><strong>Type:</strong> ' + (locationData.type.charAt(0).toUpperCase() + locationData.type.slice(1)) + '</div>';
          }
          if (locationData.day) {
            infoHTML += '<div><strong>Day:</strong> ' + locationData.day + '</div>';
          }
          if (locationData.time) {
            infoHTML += '<div><strong>Time:</strong> ' + locationData.time + '</div>';
          }
          if (locationData.address) {
            infoHTML += '<div><strong>Address:</strong> ' + locationData.address + '</div>';
          }
          locationInfo.innerHTML = infoHTML || 'No additional information available.';
        }

        // Set main image with fallbacks
        if (locationMainImage) {
          // Create a function to handle image loading with fallbacks
          const setupImageWithFallbacks = (initialSrc) => {
            // Reset opacity for transition effect
            locationMainImage.style.opacity = '0';

            // Set a timeout to show the image even if events don't fire
            const showImageTimeout = setTimeout(() => {
              locationMainImage.style.opacity = '1';
            }, 1000);

            // Setup onload event
            locationMainImage.onload = function() {
              clearTimeout(showImageTimeout);
              console.log('Image loaded successfully:', locationMainImage.src);
              locationMainImage.style.opacity = '1';

              // Hide loading indicator
              const container = locationMainImage.parentElement;
              if (container) {
                container.classList.add('image-loaded');
              }
            };

            // Setup onerror event with multiple fallback levels
            locationMainImage.onerror = function() {
              clearTimeout(showImageTimeout);
              console.error('Failed to load image:', locationMainImage.src);

              // Check if we've already tried with a random parameter
              if (!locationMainImage.src.includes('random=')) {
                // Try with Unsplash and a random parameter
                const searchTerm = encodeURIComponent(locationData.name + ' ' + (locationData.type || 'attraction'));
                const randomParam = Math.random();
                console.log('Using fallback image with random param:', randomParam);
                locationMainImage.src = 'https://source.unsplash.com/800x600/?' + searchTerm + '&random=' + randomParam;
              } else {
                // If even with random parameter it fails, use a placeholder SVG
                locationMainImage.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='600' viewBox='0 0 800 600'%3E%3Crect width='800' height='600' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='24' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3E" + encodeURIComponent(locationData.name || 'Location') + "%3C/text%3E%3C/svg%3E";
                locationMainImage.onerror = null; // Disable future attempts
                locationMainImage.style.opacity = '1';
              }
            };

            // Set initial source
            locationMainImage.src = initialSrc;
          };

          // Determine initial image source
          let initialSrc;

          // First try to use provided imageUrl
          if (locationData.imageUrl) {
            initialSrc = locationData.imageUrl;
          }
          // Then try first image from imageUrls array if available
          else if (locationData.imageUrls && Array.isArray(locationData.imageUrls) && locationData.imageUrls.length > 0) {
            initialSrc = locationData.imageUrls[0];
          }
          // Finally, use Unsplash with random parameter
          else {
            const searchTerm = encodeURIComponent(locationData.name + ' ' + (locationData.type || 'attraction'));
            const randomParam = Math.random();
            initialSrc = 'https://source.unsplash.com/800x600/?' + searchTerm + '&random=' + randomParam;
          }

          // Setup image with initial source and fallbacks
          setupImageWithFallbacks(initialSrc);
        }

        // Set thumbnails with fallbacks
        if (locationThumbnails) {
          // Clear existing thumbnails
          locationThumbnails.innerHTML = '';

          // Create thumbnails
          const thumbnailCount = 3;
          const thumbnailTerms = [
            locationData.name + ' ' + (locationData.type || 'attraction'),
            locationData.name + ' landmark',
            locationData.name + ' tourism'
          ];

          // Use provided imageUrls if available
          const imageUrls = locationData.imageUrls || [];
          const totalImages = Math.max(imageUrls.length, thumbnailCount);

          for (let i = 0; i < totalImages; i++) {
            const thumbnail = document.createElement('div');
            thumbnail.className = 'location-sheet-thumbnail' + (i === 0 ? ' active' : '');

            const img = document.createElement('img');

            // Function to setup image with fallbacks
            const setupThumbnailWithFallbacks = (initialSrc) => {
              // Setup onload event
              img.onload = function() {
                console.log('Thumbnail loaded successfully:', img.src);
                // Add class to parent (thumbnail) to indicate image is loaded
                thumbnail.classList.add('thumb-loaded');
              };

              // Setup onerror event with multiple fallback levels
              img.onerror = function() {
                console.error('Failed to load thumbnail:', img.src);

                // Check if we've already tried with a random parameter
                if (!img.src.includes('random=')) {
                  // Try with Unsplash and a different random parameter
                  const fallbackTerm = encodeURIComponent(locationData.name);
                  const randomParam = Math.random();
                  console.log('Using fallback thumbnail with random param:', randomParam);
                  img.src = 'https://source.unsplash.com/100x100/?' + fallbackTerm + '&random=' + randomParam;
                } else {
                  // If even with random parameter it fails, use a placeholder SVG
                  img.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Crect width='60' height='60' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='10' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3ENo image%3C/text%3E%3C/svg%3E";
                  img.onerror = null; // Disable future attempts
                }
              };

              // Set initial source
              img.src = initialSrc;
            };

            // Determine initial image source
            let initialSrc;

            // Use imageUrls if available
            if (i < imageUrls.length) {
              initialSrc = imageUrls[i];
            } else {
              // Otherwise use Unsplash with random parameter
              const searchTerm = encodeURIComponent(thumbnailTerms[i % thumbnailTerms.length] || locationData.name);
              const randomParam = Math.random();
              initialSrc = 'https://source.unsplash.com/100x100/?' + searchTerm + '&random=' + randomParam;
            }

            img.alt = locationData.name + ' image ' + (i+1);

            // Setup image with initial source and fallbacks
            setupThumbnailWithFallbacks(initialSrc);

            // Add click event to change main image
            thumbnail.addEventListener('click', function() {
              // Update active state
              document.querySelectorAll('.location-sheet-thumbnail').forEach(thumb => {
                thumb.classList.remove('active');
              });
              thumbnail.classList.add('active');

              // Update main image with same fallback approach
              if (locationMainImage) {
                // Reset opacity for transition effect
                locationMainImage.style.opacity = '0';

                // Set a timeout to show the image even if events don't fire
                const showImageTimeout = setTimeout(() => {
                  locationMainImage.style.opacity = '1';
                }, 1000);

                // Setup onload event
                locationMainImage.onload = function() {
                  clearTimeout(showImageTimeout);
                  console.log('Main image loaded successfully from thumbnail:', locationMainImage.src);
                  locationMainImage.style.opacity = '1';
                };

                // Setup onerror event
                locationMainImage.onerror = function() {
                  clearTimeout(showImageTimeout);
                  console.error('Failed to load main image from thumbnail:', locationMainImage.src);

                  // If image fails to load, try with random parameter
                  if (!locationMainImage.src.includes('random=')) {
                    const searchTerm = encodeURIComponent(locationData.name + ' ' + (locationData.type || 'attraction'));
                    const randomParam = Math.random();
                    console.log('Using fallback main image with random param:', randomParam);
                    locationMainImage.src = 'https://source.unsplash.com/800x600/?' + searchTerm + '&random=' + randomParam;
                  } else {
                    // If even with random parameter it fails, use a placeholder SVG
                    locationMainImage.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='600' viewBox='0 0 800 600'%3E%3Crect width='800' height='600' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='24' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3E" + encodeURIComponent(locationData.name || 'Location') + "%3C/text%3E%3C/svg%3E";
                    locationMainImage.onerror = null; // Disable future attempts
                    locationMainImage.style.opacity = '1';
                  }
                };

                // Set source
                locationMainImage.src = img.src;
              }
            });

            thumbnail.appendChild(img);
            locationThumbnails.appendChild(thumbnail);
          }
        }
      } catch (error) {
        console.error('Error in openLocationDetail:', error);
      }
    }

    // Wait for the page to fully load
    window.addEventListener('load', function() {
      console.log('Initializing map...');

      // Make sure Leaflet is loaded
      if (typeof L === 'undefined') {
        console.error('Leaflet library not loaded. Loading it now...');

        // Dynamically load Leaflet if it's not already loaded
        const leafletCSS = document.createElement('link');
        leafletCSS.rel = 'stylesheet';
        leafletCSS.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
        leafletCSS.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=';
        leafletCSS.crossOrigin = '';
        document.head.appendChild(leafletCSS);

        const leafletScript = document.createElement('script');
        leafletScript.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
        leafletScript.integrity = 'sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=';
        leafletScript.crossOrigin = '';
        leafletScript.onload = initMap;
        document.head.appendChild(leafletScript);
      } else {
        // Leaflet is already loaded, initialize the map
        initMap();
      }
    });

    function initMap() {
      console.log('Leaflet loaded, initializing map...');

      // Initialize the map
      const mapContainer = document.getElementById('main-map');
      if (!mapContainer) {
        console.error('Map container not found');
        return;
      }

      const destination = mapContainer.getAttribute('data-destination');
      console.log('Destination:', destination);

      try {
        // Create the map with a default view using the coordinates from the trip plan
        const map = L.map('main-map').setView([${tripPlan.destination.coordinates.lat}, ${tripPlan.destination.coordinates.lng}], 13);

        // Add the OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        console.log('Map created');

        // Add markers for all POIs
        const markers = [];
        const pois = ${JSON.stringify(tripPlan.pois)};

        pois.forEach((poi, index) => {
          try {
            const { name, lat, lng, day, type, description } = poi;

            if (lat && lng) {
              // Create marker
              const marker = L.marker([parseFloat(lat), parseFloat(lng)]).addTo(map);

              // Add popup
              marker.bindPopup(
                '<strong>' + name + '</strong><br>' +
                day + '<br>' +
                description
              );

              markers.push(marker);

              console.log('Added marker for:', name);
            }
          } catch (e) {
            console.error('Error adding POI marker:', e);
          }
        });

        // If we have markers, fit the map to show all of them
        if (markers.length > 0) {
          console.log('Fitting map to markers:', markers.length);
          const group = new L.featureGroup(markers);
          map.fitBounds(group.getBounds().pad(0.1));
        }
      } catch (error) {
        console.error('Error initializing map:', error);
      }
    }
  `;

  return {
    htmlContent,
    cssContent,
    jsContent,
  };
}

/**
 * Generate clarification HTML when validation fails
 */
async function generateClarificationHtml(
  query: string,
  clarifyingQuestion: any,
  validationResult: ValidationResult,
): Promise<AdvancedTripPlanningOutput> {
  const htmlContent = `
    <div id="app" class="min-h-screen flex flex-col items-center justify-center relative overflow-hidden">
      <!-- Background image -->
      <div class="absolute inset-0 z-0">
        <img src="https://source.unsplash.com/1600x900/?travel,question" class="w-full h-full object-cover" style="filter: blur(5px) brightness(0.8);" />
        <div class="absolute inset-0 bg-blue-900 bg-opacity-20"></div>
      </div>

      <!-- Content container -->
      <div class="relative z-10 max-w-3xl w-full mx-auto p-8 rounded-xl bg-white bg-opacity-95 shadow-2xl">
        <div class="text-center">
          <div class="flex justify-center mb-6">
            <div class="text-6xl animate-bounce">🤔</div>
          </div>

          <h1 class="text-3xl font-bold mb-4 text-blue-600">Need More Information</h1>
          <p class="text-xl mb-6 text-gray-700">To create the perfect travel guide for you, I need a bit more detail.</p>

          <div class="p-6 bg-blue-50 border border-blue-200 rounded-lg mb-6 text-left">
            <div class="flex items-start">
              <div class="flex-shrink-0 mr-3">
                <svg class="h-5 w-5 text-blue-400 mt-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div>
                <p class="text-sm text-blue-700">
                  <strong>Your request:</strong> "${query}"
                </p>
              </div>
            </div>
          </div>

          <div class="mb-6 text-left">
            <h2 class="text-xl font-semibold text-gray-800 mb-3">
              ${clarifyingQuestion.question}
            </h2>

            ${
              clarifyingQuestion.suggestions &&
              clarifyingQuestion.suggestions.length > 0
                ? `
              <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-sm font-medium text-gray-700 mb-2">💡 Suggestions:</h3>
                <ul class="text-sm text-gray-600 space-y-1">
                  ${clarifyingQuestion.suggestions.map((suggestion: string) => `<li>• ${suggestion}</li>`).join('')}
                </ul>
              </div>
            `
                : ''
            }
          </div>

          <div class="text-center">
            <p class="text-sm text-gray-500 mb-4">
              Confidence: ${Math.round(validationResult.confidence * 100)}% |
              ${validationResult.reasoning}
            </p>
            <button
              onclick="window.location.reload()"
              class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-200 transform hover:scale-105"
            >
              ✨ Try Again
            </button>
          </div>
        </div>
      </div>
    </div>
  `;

  const cssContent = `
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f8fafc;
      color: #334155;
      min-height: 100vh;
    }

    @keyframes bounce {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-10px); }
    }

    .animate-bounce {
      animation: bounce 2s ease-in-out infinite;
    }

    .transition {
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 200ms;
    }

    .transform {
      transform: translateX(0) translateY(0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY(1);
    }

    .hover\\:scale-105:hover {
      transform: scale(1.05);
    }
  `;

  const jsContent = `
    console.log("Validation failed for query: ${query.replace(/"/g, '\\"')}");
    console.log("Confidence: ${validationResult.confidence}");
    console.log("Reasoning: ${validationResult.reasoning.replace(/"/g, '\\"')}");

    // Add subtle animation to the question emoji
    const emojiElement = document.querySelector('#app .animate-bounce');
    if (emojiElement) {
      console.log('Question emoji animation initialized');
    }
  `;

  return {
    htmlContent,
    cssContent,
    jsContent,
  };
}

/**
 * Generate error HTML in case the workflow fails
 */
async function generateErrorHtml(
  query: string,
  error: any,
): Promise<AdvancedTripPlanningOutput> {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';

  const htmlContent = `
    <div id="app" class="min-h-screen flex flex-col items-center justify-center relative overflow-hidden">
      <!-- Background image -->
      <div class="absolute inset-0 z-0">
        <img src="https://source.unsplash.com/1600x900/?travel,error" class="w-full h-full object-cover" style="filter: blur(5px) brightness(0.7);" />
        <div class="absolute inset-0 bg-red-900 bg-opacity-30"></div>
      </div>

      <!-- Content container -->
      <div class="relative z-10 max-w-3xl w-full mx-auto p-8 rounded-xl bg-white bg-opacity-95 shadow-2xl">
        <div class="text-center">
          <div class="flex justify-center mb-6">
            <div class="text-6xl">🧳❌</div>
          </div>

          <h1 class="text-3xl font-bold mb-4 text-red-600">Oops! Travel Plans Interrupted</h1>
          <p class="text-xl mb-6">We couldn't generate your travel itinerary. Please try again with a different query.</p>

          <div class="p-6 bg-red-50 border border-red-200 rounded-lg mb-6 text-left">
            <h3 class="font-bold text-red-800 mb-2">What Happened:</h3>
            <p class="mb-4">We encountered an error while creating your travel plan for "${query}". This might be due to:</p>
            <ul class="list-disc pl-5 mb-4">
              <li>Temporary service unavailability</li>
              <li>Limited information about this destination</li>
              <li>Connection issues with our travel data providers</li>
            </ul>
          </div>

          <div class="mt-6">
            <h3 class="font-bold text-lg mb-3">Suggestions:</h3>
            <ul class="text-left list-disc pl-6 space-y-2">
              <li>Try specifying a clear destination (e.g., "Trip to Paris for 5 days")</li>
              <li>Make sure your destination is a real place</li>
              <li>Specify the number of days for your trip</li>
              <li>Try a different destination or duration</li>
            </ul>
          </div>

          <details class="mt-6 text-left">
            <summary class="cursor-pointer text-sm text-gray-600">Technical details</summary>
            <pre class="mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto font-mono">${errorMessage}</pre>
          </details>
        </div>
      </div>
    </div>
  `;

  const cssContent = `
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f8fafc;
      color: #334155;
      min-height: 100vh;
    }

    .list-disc {
      list-style-type: disc;
    }

    @keyframes shake {
      0%, 100% { transform: rotate(0deg); }
      25% { transform: rotate(-5deg); }
      75% { transform: rotate(5deg); }
    }
  `;

  const jsContent = `
    console.error("Error generating travel itinerary: ${errorMessage.replace(/"/g, '\\"')}");

    // Add a subtle animation to the error emoji
    const emojiElement = document.querySelector('#app .text-6xl');
    if (emojiElement) {
      emojiElement.style.animation = 'shake 2s ease-in-out infinite';
    }
  `;

  return {
    htmlContent,
    cssContent,
    jsContent,
  };
}
/**
 * Fonction utilitaire pour récupérer des images Tavily pour un lieu (POI)
 */
async function getLocationImagesWithTavily(
  location: string,
  city: string,
  country: string,
): Promise<string[]> {
  console.log(
    `[Tavily] Recherche d'images pour ${location} à ${city}, ${country}`,
  );

  const mockSession = {
    user: { id: 'system', type: 'guest' as const },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  };
  const mockDataStream: DataStreamWriter = {
    writeMessageAnnotation: () => {},
    write: () => '',
    writeData: () => '',
    writeSource: () => '',
    merge: () => '',
    onError: () => '',
  };

  const searchTool = web_search({
    session: mockSession,
    dataStream: mockDataStream,
  });

  const queries = [
    `${location} ${city} ${country} high resolution photo`,
    `${location} ${city} ${country} landmark image`,
    `${location} ${city} ${country} travel`,
  ];

  console.log('[Tavily] Requêtes de recherche:', queries);

  const searchResults = await Promise.all(
    queries.map((query) =>
      (searchTool.execute as any)({
        queries: [query],
        maxResults: [5],
        topics: ['general'],
        searchDepth: ['advanced'],
      }),
    ),
  );

  console.log('[Tavily] Résultats de recherche reçus:', searchResults.length);

  const imageUrls: string[] = [];
  for (const result of searchResults) {
    if (
      result?.searches?.[0]?.images &&
      Array.isArray(result.searches[0].images)
    ) {
      for (const image of result.searches[0].images) {
        if (typeof image === 'string') {
          console.log('[Tavily] Image trouvée (string):', image);
          imageUrls.push(image);
        } else if (image && typeof image === 'object' && image.url) {
          console.log('[Tavily] Image trouvée (object):', image.url);
          imageUrls.push(image.url);
        }
      }
    }
  }

  // Filtrer les doublons et les images de mauvaise qualité
  const filtered = imageUrls.filter(
    (url, idx, arr) =>
      url &&
      arr.indexOf(url) === idx &&
      !url.includes('icon') &&
      !url.includes('thumb') &&
      !url.includes('small'),
  );

  console.log('[Tavily] Images filtrées:', filtered.length);
  console.log('[Tavily] URLs des images:', filtered);

  // Retourner jusqu'à 3 images
  return filtered.slice(0, 3);
}
