import { useState, useEffect, useCallback } from 'react';

type Position = {
  latitude: number | null;
  longitude: number | null;
  accuracy: number | null;
  timestamp?: number;
};

type LocationError = {
  code: number;
  message: string;
  PERMISSION_DENIED: number;
  POSITION_UNAVAILABLE: number;
  TIMEOUT: number;
};

type UseLocationReturn = {
  position: Position | null;
  error: string | null;
  isLoading: boolean;
  getPosition: () => Promise<Position | null>;
};

const useLocation = (): UseLocationReturn => {
  const [position, setPosition] = useState<Position | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const getPosition = useCallback(async (): Promise<Position | null> => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by your browser');
      return null;
    }

    setIsLoading(true);
    setError(null);

    return new Promise((resolve) => {
      navigator.geolocation.getCurrentPosition(
        (pos) => {
          const positionData: Position = {
            latitude: pos.coords.latitude,
            longitude: pos.coords.longitude,
            accuracy: pos.coords.accuracy,
            timestamp: pos.timestamp,
          };
          setPosition(positionData);
          setIsLoading(false);
          resolve(positionData);
        },
        (err: GeolocationPositionError) => {
          let errorMessage = 'Unable to retrieve your location';
          
          switch (err.code) {
            case err.PERMISSION_DENIED:
              errorMessage = 'Location access was denied';
              break;
            case err.POSITION_UNAVAILABLE:
              errorMessage = 'Location information is unavailable';
              break;
            case err.TIMEOUT:
              errorMessage = 'Location request timed out';
              break;
          }
          
          setError(errorMessage);
          setIsLoading(false);
          resolve(null);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000, // 10 seconds
          maximumAge: 60000, // 1 minute
        }
      );
    });
  }, []);

  // Get location on mount if permission was previously granted
  useEffect(() => {
    const checkPermissionAndGetLocation = async () => {
      if (navigator.permissions) {
        try {
          const permissionStatus = await navigator.permissions.query({ 
            name: 'geolocation' as PermissionName 
          });
          
          if (permissionStatus.state === 'granted') {
            await getPosition();
          }
        } catch (e) {
          // Permission query not supported, continue without auto-fetching
        }
      }
    };

    checkPermissionAndGetLocation();
  }, [getPosition]);

  return {
    position,
    error,
    isLoading,
    getPosition,
  };
};

export default useLocation;
