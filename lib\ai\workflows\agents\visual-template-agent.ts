import {
  TripType,
  VisualTheme,
  type TripClassification,
} from './classifier-agent';

/**
 * Interface for visual template
 */
export interface VisualTemplate {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
    headings: string;
  };
  fonts: {
    heading: string;
    body: string;
  };
  layout: {
    heroStyle: 'fullscreen' | 'large' | 'medium' | 'small';
    mapSize: 'large' | 'medium' | 'small';
    sectionOrder: string[];
    highlightedSections: string[];
  };
  icons: {
    type: 'solid' | 'outline' | 'duotone' | 'emoji';
    set: string;
  };
  images: {
    style: 'photography' | 'illustration' | 'minimal' | 'vintage';
    filter: string;
    borderRadius: string;
  };
  animations: {
    level: 'none' | 'subtle' | 'moderate' | 'playful';
    transitions: string;
  };
  specialElements: {
    callouts: boolean;
    timelines: boolean;
    cards: boolean;
    tabs: boolean;
    accordions: boolean;
    galleries: boolean;
    charts: boolean;
    customElements: string[];
  };
  css: {
    headerCSS: string;
    bodyCSS: string;
    sectionCSS: string;
    mapCSS: string;
    imageCSS: string;
    buttonCSS: string;
    cardCSS: string;
    customCSS: string;
  };
  headerImageKeywords: string[];
}

/**
 * VisualTemplateAgent is responsible for generating visual templates
 * based on the trip classification.
 */
export class VisualTemplateAgent {
  /**
   * Generate a visual template based on the trip classification
   */
  async generateVisualTemplate(
    classification: TripClassification,
    destination: string,
  ): Promise<VisualTemplate> {
    try {
      console.log(
        'Generating visual template for:',
        classification.primaryType,
      );

      // Get template based on trip type and visual theme
      const template = this.getTemplateForType(
        classification.primaryType,
        classification.visualTheme,
      );

      // Customize template based on classification
      const customizedTemplate = this.customizeTemplate(
        template,
        classification,
        destination,
      );

      // Get header image keywords
      customizedTemplate.headerImageKeywords =
        await this.getHeaderImageKeywords(classification, destination);

      return customizedTemplate;
    } catch (error) {
      console.error('Error generating visual template:', error);

      // Return default template if generation fails
      return this.getDefaultTemplate();
    }
  }

  /**
   * Get template for specific trip type and visual theme
   */
  private getTemplateForType(
    tripType: TripType,
    visualTheme: VisualTheme,
  ): VisualTemplate {
    // Base template
    const baseTemplate = this.getDefaultTemplate();

    // Customize based on trip type
    switch (tripType) {
      case TripType.ADVENTURE:
        return {
          ...baseTemplate,
          colors: {
            primary: '#ff7e00',
            secondary: '#2d5d7b',
            accent: '#ffd100',
            background: '#f8f9fa',
            text: '#333333',
            headings: '#1a1a1a',
          },
          fonts: {
            heading: "'Montserrat', sans-serif",
            body: "'Open Sans', sans-serif",
          },
          layout: {
            heroStyle: 'fullscreen',
            mapSize: 'large',
            sectionOrder: [
              'map',
              'activities',
              'itinerary',
              'accommodations',
              'dining',
              'tips',
            ],
            highlightedSections: ['activities', 'map'],
          },
          icons: {
            type: 'solid',
            set: 'adventure',
          },
          images: {
            style: 'photography',
            filter: 'vibrant',
            borderRadius: '8px',
          },
          animations: {
            level: 'moderate',
            transitions: 'slide-up fade-in',
          },
        };

      case TripType.CYCLING:
        return {
          ...baseTemplate,
          colors: {
            primary: '#4caf50',
            secondary: '#2196f3',
            accent: '#ff9800',
            background: '#f5f5f5',
            text: '#333333',
            headings: '#1a1a1a',
          },
          fonts: {
            heading: "'Roboto Condensed', sans-serif",
            body: "'Roboto', sans-serif",
          },
          layout: {
            heroStyle: 'large',
            mapSize: 'large',
            sectionOrder: [
              'map',
              'routes',
              'itinerary',
              'accommodations',
              'dining',
              'tips',
            ],
            highlightedSections: ['routes', 'map'],
          },
          icons: {
            type: 'solid',
            set: 'cycling',
          },
          images: {
            style: 'photography',
            filter: 'natural',
            borderRadius: '4px',
          },
          animations: {
            level: 'subtle',
            transitions: 'slide-up',
          },
          specialElements: {
            ...baseTemplate.specialElements,
            timelines: true,
            charts: true,
            customElements: [
              'elevation-profile',
              'route-stats',
              'bike-rental-info',
            ],
          },
        };

      case TripType.BEACH:
        return {
          ...baseTemplate,
          colors: {
            primary: '#03a9f4',
            secondary: '#00bcd4',
            accent: '#ff9800',
            background: '#f5f9ff',
            text: '#333333',
            headings: '#1a1a1a',
          },
          fonts: {
            heading: "'Lato', sans-serif",
            body: "'Lato', sans-serif",
          },
          layout: {
            heroStyle: 'fullscreen',
            mapSize: 'medium',
            sectionOrder: [
              'beaches',
              'activities',
              'itinerary',
              'accommodations',
              'dining',
              'tips',
            ],
            highlightedSections: ['beaches', 'activities'],
          },
          icons: {
            type: 'solid',
            set: 'beach',
          },
          images: {
            style: 'photography',
            filter: 'bright',
            borderRadius: '12px',
          },
          animations: {
            level: 'playful',
            transitions: 'fade-in slide-up',
          },
        };

      // Add more trip types as needed...

      default:
        // Customize based on visual theme if no specific trip type template
        switch (visualTheme) {
          case VisualTheme.MODERN:
            return {
              ...baseTemplate,
              colors: {
                primary: '#2196f3',
                secondary: '#f50057',
                accent: '#ffeb3b',
                background: '#ffffff',
                text: '#212121',
                headings: '#000000',
              },
              fonts: {
                heading: "'Poppins', sans-serif",
                body: "'Roboto', sans-serif",
              },
              layout: {
                heroStyle: 'large',
                mapSize: 'medium',
                sectionOrder: [
                  'itinerary',
                  'map',
                  'accommodations',
                  'dining',
                  'activities',
                  'tips',
                ],
                highlightedSections: ['itinerary', 'map'],
              },
              icons: {
                type: 'outline',
                set: 'modern',
              },
              images: {
                style: 'photography',
                filter: 'clean',
                borderRadius: '0px',
              },
              animations: {
                level: 'subtle',
                transitions: 'fade-in',
              },
            };

          case VisualTheme.LUXURY:
            return {
              ...baseTemplate,
              colors: {
                primary: '#9c27b0',
                secondary: '#673ab7',
                accent: '#ffc107',
                background: '#f8f8f8',
                text: '#333333',
                headings: '#1a1a1a',
              },
              fonts: {
                heading: "'Playfair Display', serif",
                body: "'Lora', serif",
              },
              layout: {
                heroStyle: 'large',
                mapSize: 'medium',
                sectionOrder: [
                  'accommodations',
                  'dining',
                  'activities',
                  'itinerary',
                  'map',
                  'tips',
                ],
                highlightedSections: ['accommodations', 'dining'],
              },
              icons: {
                type: 'solid',
                set: 'luxury',
              },
              images: {
                style: 'photography',
                filter: 'elegant',
                borderRadius: '8px',
              },
              animations: {
                level: 'subtle',
                transitions: 'fade-in',
              },
            };

          // Add more visual themes as needed...

          default:
            return baseTemplate;
        }
    }
  }

  /**
   * Customize template based on classification
   */
  private customizeTemplate(
    template: VisualTemplate,
    classification: TripClassification,
    _destination: string,
  ): VisualTemplate {
    const customized = { ...template };

    // Customize layout based on customizations
    if (classification.customizations.mapFocus) {
      customized.layout.mapSize = 'large';
      if (!customized.layout.highlightedSections.includes('map')) {
        customized.layout.highlightedSections.push('map');
      }
    }

    if (classification.customizations.foodFocus) {
      if (!customized.layout.highlightedSections.includes('dining')) {
        customized.layout.highlightedSections.push('dining');
      }
      // Move dining higher in the section order
      const diningIndex = customized.layout.sectionOrder.indexOf('dining');
      if (diningIndex > 0) {
        customized.layout.sectionOrder.splice(diningIndex, 1);
        customized.layout.sectionOrder.splice(2, 0, 'dining');
      }
    }

    if (classification.customizations.accommodationFocus) {
      if (!customized.layout.highlightedSections.includes('accommodations')) {
        customized.layout.highlightedSections.push('accommodations');
      }
      // Move accommodations higher in the section order
      const accommodationsIndex =
        customized.layout.sectionOrder.indexOf('accommodations');
      if (accommodationsIndex > 0) {
        customized.layout.sectionOrder.splice(accommodationsIndex, 1);
        customized.layout.sectionOrder.splice(2, 0, 'accommodations');
      }
    }

    // Add special elements based on primary activity
    if (classification.primaryActivity) {
      customized.specialElements.customElements.push(
        `${classification.primaryActivity.toLowerCase().replace(/\s+/g, '-')}-focus`,
      );
    }

    return customized;
  }

  /**
   * Get header image keywords based on classification and destination
   */
  private async getHeaderImageKeywords(
    classification: TripClassification,
    destination: string,
  ): Promise<string[]> {
    const keywords = [
      destination,
      classification.primaryType,
      classification.primaryActivity,
    ];

    // Add specific keywords based on trip type
    switch (classification.primaryType) {
      case TripType.CYCLING:
        keywords.push('cycling routes');
        keywords.push('bicycle tourism');
        keywords.push('cycling path');
        keywords.push('bike tour');
        break;
      case TripType.HIKING:
        keywords.push('hiking trails');
        keywords.push('mountains');
        keywords.push('nature path');
        break;
      case TripType.BEACH:
        keywords.push('coastline');
        keywords.push('ocean view');
        keywords.push('beach sunset');
        break;
      // Add more trip types as needed...
    }

    // Add keywords from classification
    keywords.push(...classification.keywords);

    // Filter out duplicates and empty strings
    return [...new Set(keywords.filter((k) => k))];
  }

  /**
   * Get default template
   */
  private getDefaultTemplate(): VisualTemplate {
    return {
      colors: {
        primary: '#3f51b5',
        secondary: '#f44336',
        accent: '#4caf50',
        background: '#ffffff',
        text: '#333333',
        headings: '#1a1a1a',
      },
      fonts: {
        heading: "'Roboto', sans-serif",
        body: "'Open Sans', sans-serif",
      },
      layout: {
        heroStyle: 'large',
        mapSize: 'medium',
        sectionOrder: [
          'itinerary',
          'map',
          'accommodations',
          'dining',
          'activities',
          'tips',
        ],
        highlightedSections: ['itinerary'],
      },
      icons: {
        type: 'solid',
        set: 'default',
      },
      images: {
        style: 'photography',
        filter: 'none',
        borderRadius: '8px',
      },
      animations: {
        level: 'subtle',
        transitions: 'fade-in',
      },
      specialElements: {
        callouts: true,
        timelines: false,
        cards: true,
        tabs: true,
        accordions: true,
        galleries: true,
        charts: false,
        customElements: [],
      },
      css: {
        headerCSS: `
          header {
            padding: 2rem 0;
            text-align: center;
          }
          h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
          }
        `,
        bodyCSS: `
          body {
            font-family: 'Open Sans', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
          }
        `,
        sectionCSS: `
          section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          h2 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: #1a1a1a;
          }
        `,
        mapCSS: `
          .map-container {
            height: 400px;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 1.5rem;
          }
        `,
        imageCSS: `
          img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            display: block;
          }
        `,
        buttonCSS: `
          button {
            background-color: #3f51b5;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
          }
          button:hover {
            background-color: #303f9f;
          }
        `,
        cardCSS: `
          .card {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
          }
          .card-header {
            padding: 1rem;
            background-color: #f5f5f5;
            font-weight: bold;
          }
          .card-body {
            padding: 1rem;
          }
        `,
        customCSS: '',
      },
      headerImageKeywords: [],
    };
  }
}
