import { useEffect, useRef, useState, type RefObject } from 'react';

export function useScrollToBottom<T extends HTMLElement>(): [
  RefObject<T>,
  RefObject<T>,
  boolean,
  () => void,
] {
  const containerRef = useRef<T>(null);
  const endRef = useRef<T>(null);
  const [isAtBottom, setIsAtBottom] = useState(true);

  const scrollToBottom = () => {
    endRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' });
  };

  useEffect(() => {
    const container = containerRef.current;
    const end = endRef.current;

    if (container && end) {
      // Fonction pour vérifier si l'utilisateur est en bas
      const isUserAtBottom = () => {
        if (container) {
          const { scrollTop, scrollHeight, clientHeight } = container;
          return scrollHeight - scrollTop - clientHeight < 100;
        }
        return true;
      };

      // Détecter quand l'utilisateur n'est pas en bas
      const handleScroll = () => {
        setIsAtBottom(isUserAtBottom());
      };

      // Vérifier si l'utilisateur est en bas avant de défiler
      const observer = new MutationObserver(() => {
        // Ne défiler automatiquement que si l'utilisateur est déjà en bas
        if (isUserAtBottom()) {
          // Utiliser requestAnimationFrame pour s'assurer que le défilement se produit après le rendu
          requestAnimationFrame(() => {
            end.scrollIntoView({ behavior: 'instant', block: 'end' });
          });
        }
      });

      observer.observe(container, {
        childList: true,
        subtree: true,
        attributes: true,
        characterData: true,
      });

      container.addEventListener('scroll', handleScroll);

      // Exécuter handleScroll une fois au début pour initialiser isAtBottom
      handleScroll();

      return () => {
        observer.disconnect();
        container.removeEventListener('scroll', handleScroll);
      };
    }
  }, []); // Pas de dépendance pour éviter les re-rendus inutiles

  return [containerRef, endRef, isAtBottom, scrollToBottom];
}
