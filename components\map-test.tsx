"use client";

import React, { useState } from 'react';
import MapSearch from './map-search';

const MapTest: React.FC = () => {
  const [query, setQuery] = useState<string>('');
  const [showMap, setShowMap] = useState<boolean>(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setShowMap(true);
  };

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Test de la carte</h1>
      
      <form onSubmit={handleSubmit} className="mb-4">
        <div className="flex gap-2">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Entrez un lieu (ex: Paris, Tour Eiffel, etc.)"
            className="flex-1 p-2 border border-gray-300 rounded"
          />
          <button 
            type="submit"
            className="bg-blue-500 text-white px-4 py-2 rounded"
          >
            Rechercher
          </button>
        </div>
      </form>
      
      {showMap && query && <MapSearch query={query} />}
    </div>
  );
};

export default MapTest;
