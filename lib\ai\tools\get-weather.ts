import { tool } from 'ai';
import { z } from 'zod';

// Fonction pour géocoder un lieu en coordonnées
async function geocodeLocation(location: string) {
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(location)}&format=json&limit=1`,
    );
    const data = await response.json();

    if (data && data.length > 0) {
      return {
        latitude: Number.parseFloat(data[0].lat),
        longitude: Number.parseFloat(data[0].lon),
        displayName: data[0].display_name,
      };
    }
    return null;
  } catch (error) {
    console.error('Geocoding error:', error);
    return null;
  }
}

export const getWeather = tool({
  description: 'Get the current weather for a city or location',
  parameters: z.object({
    city: z
      .string()
      .describe('City name or location (e.g. "Paris", "New York")'),
  }),
  execute: async ({ city }) => {
    // Géocoder le nom de la ville en coordonnées
    const geoData = await geocodeLocation(city);

    if (!geoData) {
      return { error: `Could not find coordinates for location: ${city}` };
    }

    const { latitude, longitude, displayName } = geoData;

    const response = await fetch(
      `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&current=temperature_2m&hourly=temperature_2m&daily=sunrise,sunset&timezone=auto`,
    );

    const weatherData = await response.json();

    // Ajouter le nom complet du lieu aux données météo
    return {
      ...weatherData,
      location: {
        name: city,
        displayName: displayName,
      },
    };
  },
});
