import Image from 'next/image';
import { useState } from 'react';
import { ImageGallery } from './image-gallery';

interface SearchResult {
  url: string;
  title: string;
  content: string;
  published_date?: string;
}

interface ImageResult {
  url: string;
  description: string;
}

interface QueryResult {
  query: string;
  results: SearchResult[];
  images?: ImageResult[] | string[];
}

export function SearchResults({ results }: { results: QueryResult[] }) {
  if (!results || !Array.isArray(results)) return null;

  return (
    <div className="search-results-container mt-4">
      {results.map((query: QueryResult, _: number) => (
        <div key={`query-${query.query}`} className="mb-6">
          <h3 className="text-lg font-semibold mb-3">
            Résultats pour: &quot;{query.query}&quot;
          </h3>

          {/* Afficher les images si disponibles */}
          {query.images && query.images.length > 0 && (
            <div className="mb-4">
              <h4 className="text-md font-medium mb-2">
                Images pour &quot;{query.query}&quot;
              </h4>
              <ImageGallery
                images={query.images}
                query={query.query}
                maxPreviewImages={4}
              />
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {query.results.map((item: SearchResult) => (
              <SearchResultCard key={item.url} result={item} />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}

function SearchResultCard({ result }: { result: SearchResult }) {
  const [imageError, setImageError] = useState(0); // 0: initial, 1: direct error, 2: proxy error
  const defaultImage = '/images/placeholder-article.jpg';

  const domain = new URL(result.url).hostname;
  let imageUrl: string;

  if (imageError === 0) {
    imageUrl = `https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${result.url}&size=128`;
  } else if (imageError === 1) {
    // Essayer avec le proxy
    imageUrl = `/api/proxy-image?url=${encodeURIComponent(`https://t2.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${result.url}&size=128`)}`;
  } else {
    // Utiliser l'image par défaut
    imageUrl = defaultImage;
  }

  return (
    <a
      href={result.url}
      target="_blank"
      rel="noopener noreferrer"
      className="block bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden"
    >
      <div className="relative h-40 bg-gray-100 dark:bg-gray-700">
        <Image
          src={imageUrl}
          alt={result.title}
          fill
          className="object-contain p-4"
          onError={(e) => {
            const imgElement = e.target as HTMLImageElement;

            if (imageError === 0) {
              // Première erreur, essayer avec le proxy
              setImageError(1);

              // Nettoyer l'URL si elle contient déjà des caractères encodés
              let urlToProxy = imageUrl;
              if (urlToProxy.includes('%')) {
                try {
                  urlToProxy = decodeURIComponent(urlToProxy);
                } catch (err) {
                  // Ignorer les erreurs de décodage
                }
              }

              imgElement.src = `/api/proxy-image?url=${encodeURIComponent(urlToProxy)}`;
            } else if (imageError === 1) {
              // Deuxième erreur, utiliser l'image par défaut
              setImageError(2);
              imgElement.src = defaultImage;
              // Désactiver les futures tentatives
              imgElement.onerror = null;
            }
          }}
        />
      </div>
      <div className="p-4">
        <h4 className="font-medium text-blue-600 dark:text-blue-400 mb-2 line-clamp-2">
          {result.title}
        </h4>
        <p className="text-gray-600 dark:text-gray-300 text-sm line-clamp-3">
          {result.content}
        </p>
        <div className="mt-3 text-xs text-gray-500 dark:text-gray-400 truncate">
          {domain}
        </div>
      </div>
    </a>
  );
}
