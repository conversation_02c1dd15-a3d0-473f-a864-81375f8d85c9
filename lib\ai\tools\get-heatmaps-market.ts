import { z } from 'zod';
import { tool } from 'ai';

// Define and export the tool configuration
export const getHeatmapsMarket = tool({
  description:
    'Display a stock market heatmap with sector visualization and performance',
  parameters: z.object({
    // No parameters needed for the heatmap as it shows a predefined view
  }),
  execute: async () => {
    // Return a simple object that will be used by the frontend to render the component
    return {
      type: 'heatmaps_market',
      data: {},
    };
  },
});
