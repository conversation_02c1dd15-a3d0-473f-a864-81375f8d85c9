// Composant pour afficher les paramètres d'utilisation de la recherche extrême
// Compatible avec les pages de paramètres

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Lightning, TrendingUp, Calendar, AlertTriangle } from 'lucide-react';
import { EXTREME_SEARCH_LIMITS } from '../extreme-search-standalone/config/constants';
import {
  cn,
  formatDate,
  calculateUsagePercentage,
  getUsageColor,
} from '../extreme-search-standalone/utils';

// Types pour les données d'utilisation
interface ExtremeSearchUsageData {
  currentUsage: number;
  limit: number;
  remaining: number;
  isPro: boolean;
  canSearch: boolean;
  usagePercentage: number;
  resetDate: Date;
}

interface ExtremeSearchUsageHistoryItem {
  month: string;
  usage: number;
  limit: number;
}

// Props du composant
interface ExtremeSearchUsageSettingsProps {
  usageData?: ExtremeSearchUsageData;
  historyData?: ExtremeSearchUsageHistoryItem[];
  isLoading?: boolean;
  onUpgrade?: () => void;
  className?: string;
}

export const ExtremeSearchUsageSettings: React.FC<
  ExtremeSearchUsageSettingsProps
> = ({ usageData, historyData, isLoading = false, onUpgrade, className }) => {
  const [showHistory, setShowHistory] = useState(false);

  // Calculs dérivés
  const usagePercentage = usageData
    ? calculateUsagePercentage(usageData.currentUsage, usageData.limit)
    : 0;
  const isNearLimit = usagePercentage >= 80;
  const isAtLimit = usageData ? !usageData.canSearch : false;

  return (
    <div className={cn('space-y-6', className)}>
      {/* Carte principale d'utilisation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightning className="h-5 w-5 text-purple-600" />
            Extreme Search Usage
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Statistiques principales */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Utilisation actuelle */}
            <div className="text-center">
              <div className="text-2xl font-bold">
                {isLoading ? (
                  <Skeleton className="h-8 w-12 mx-auto" />
                ) : (
                  <span className={getUsageColor(usagePercentage)}>
                    {usageData?.currentUsage || 0}
                  </span>
                )}
              </div>
              <p className="text-sm text-muted-foreground">This month</p>
            </div>

            {/* Limite */}
            <div className="text-center">
              <div className="text-2xl font-bold">
                {isLoading ? (
                  <Skeleton className="h-8 w-16 mx-auto" />
                ) : (
                  <span>
                    {usageData?.limit === -1
                      ? '∞'
                      : usageData?.limit ||
                        EXTREME_SEARCH_LIMITS.FREE_MONTHLY_LIMIT}
                  </span>
                )}
              </div>
              <p className="text-sm text-muted-foreground">Monthly limit</p>
            </div>

            {/* Restant */}
            <div className="text-center">
              <div className="text-2xl font-bold">
                {isLoading ? (
                  <Skeleton className="h-8 w-12 mx-auto" />
                ) : (
                  <span className="text-green-600">
                    {usageData?.remaining === -1
                      ? '∞'
                      : usageData?.remaining || 0}
                  </span>
                )}
              </div>
              <p className="text-sm text-muted-foreground">Remaining</p>
            </div>
          </div>

          {/* Barre de progression */}
          {!isLoading && usageData && usageData.limit !== -1 && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Usage Progress</span>
                <span>{usagePercentage.toFixed(1)}% used</span>
              </div>
              <Progress
                value={usagePercentage}
                className={cn(
                  'h-3',
                  isAtLimit && 'bg-red-100',
                  isNearLimit && !isAtLimit && 'bg-orange-100',
                )}
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>
                  {usageData.currentUsage} of {usageData.limit} extreme searches
                  used this month
                </span>
                <span>{usageData.remaining} remaining</span>
              </div>
            </div>
          )}

          {/* Alertes */}
          {!isLoading && isAtLimit && (
            <div className="p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex items-center gap-2 text-red-800 dark:text-red-200">
                <AlertTriangle className="h-4 w-4" />
                <span className="text-sm font-medium">
                  Monthly extreme search limit reached!
                </span>
              </div>
              <p className="text-xs text-red-700 dark:text-red-300 mt-1">
                Upgrade to Pro for unlimited extreme searches with advanced AI
                analysis.
              </p>
              {onUpgrade && (
                <Button size="sm" className="mt-2" onClick={onUpgrade}>
                  Upgrade to Pro
                </Button>
              )}
            </div>
          )}

          {!isLoading && isNearLimit && !isAtLimit && (
            <div className="p-3 bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded-lg">
              <div className="flex items-center gap-2 text-orange-800 dark:text-orange-200">
                <Lightning className="h-4 w-4" />
                <span className="text-sm font-medium">
                  Almost at monthly extreme search limit
                </span>
              </div>
              <p className="text-xs text-orange-700 dark:text-orange-300 mt-1">
                Upgrade to Pro for unlimited extreme searches and premium
                features.
              </p>
            </div>
          )}

          {/* Badge Pro */}
          {usageData?.isPro && (
            <div className="flex items-center justify-center">
              <Badge
                variant="secondary"
                className="bg-purple-100 text-purple-800"
              >
                <Lightning className="h-3 w-3 mr-1" />
                Pro - Unlimited Searches
              </Badge>
            </div>
          )}

          {/* Date de réinitialisation */}
          {usageData?.resetDate && (
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>Usage resets on {formatDate(usageData.resetDate)}</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Historique d'utilisation */}
      {historyData && historyData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-600" />
                Usage History
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowHistory(!showHistory)}
              >
                {showHistory ? 'Hide' : 'Show'} History
              </Button>
            </CardTitle>
          </CardHeader>

          {showHistory && (
            <CardContent>
              <div className="space-y-3">
                {historyData.map((item, index) => {
                  const percentage = calculateUsagePercentage(
                    item.usage,
                    item.limit,
                  );
                  return (
                    <div
                      key={index}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-3">
                        <span className="text-sm font-medium min-w-[80px]">
                          {new Date(`${item.month}-01`).toLocaleDateString(
                            'en-US',
                            {
                              month: 'short',
                              year: 'numeric',
                            },
                          )}
                        </span>
                        <div className="flex-1 min-w-[100px]">
                          <Progress value={percentage} className="h-2" />
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground min-w-[60px] text-right">
                        {item.usage}/{item.limit}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          )}
        </Card>
      )}
    </div>
  );
};

// Hook pour utiliser les données d'utilisation
export const useExtremeSearchUsage = () => {
  const [usageData, setUsageData] = useState<ExtremeSearchUsageData | null>(
    null,
  );
  const [historyData, setHistoryData] = useState<
    ExtremeSearchUsageHistoryItem[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUsageData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Remplacez par vos vraies actions/API calls
      const [usage, history] = await Promise.all([
        fetch('/api/extreme-search/usage').then((r) => r.json()),
        fetch('/api/extreme-search/history').then((r) => r.json()),
      ]);

      setUsageData(usage);
      setHistoryData(history);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to fetch usage data',
      );
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUsageData();
  }, []);

  return {
    usageData,
    historyData,
    isLoading,
    error,
    refetch: fetchUsageData,
  };
};
