[1mdiff --git a/lib/db/schema.ts b/lib/db/schema.ts[m
[1mindex 1228aab..3d0c6bf 100644[m
[1m--- a/lib/db/schema.ts[m
[1m+++ b/lib/db/schema.ts[m
[36m@@ -109,7 +109,7 @@[m [mexport const document = pgTable([m
     createdAt: timestamp('createdAt').notNull(),[m
     title: text('title').notNull(),[m
     content: text('content'),[m
[31m-    kind: varchar('text', { enum: ['text', 'code', 'image', 'sheet'] })[m
[32m+[m[32m    text: varchar('text', { enum: ['text', 'code', 'image', 'sheet', 'html'] })[m
       .notNull()[m
       .default('text'),[m
     userId: uuid('userId')[m
[36m@@ -123,7 +123,11 @@[m [mexport const document = pgTable([m
   },[m
 );[m
 [m
[31m-export type Document = InferSelectModel<typeof document>;[m
[32m+[m[32m// Le type Document généré par InferSelectModel utilise 'text' comme nom de propriété[m
[32m+[m[32m// mais nous voulons utiliser 'kind' dans notre code[m
[32m+[m[32mexport type Document = Omit<InferSelectModel<typeof document>, 'text'> & {[m
[32m+[m[32m  kind: string;[m
[32m+[m[32m};[m
 [m
 export const suggestion = pgTable([m
   'Suggestion',[m
