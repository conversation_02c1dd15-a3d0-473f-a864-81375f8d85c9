// lib/blobUploader.mock.ts

export async function mockPut(filename: string, data: any, options: any) {
  console.log('Mock blob.put called with:', filename);

  // Vérifier que access est défini sur 'public'
  if (options?.access !== 'public') {
    throw new Error('Vercel Blob: access must be "public"');
  }

  return {
    url: `https://mock-blob-storage.com/${filename}`,
    downloadUrl: `https://mock-blob-storage.com/${filename}?download`,
    pathname: filename,
    contentType: options?.contentType || 'application/octet-stream',
    contentDisposition: options?.contentDisposition || 'inline',
  };
}
