import { Button } from './ui/button';
import { ChevronDownIcon } from './icons';
import { motion } from 'framer-motion';
import { useArtifactSelector } from '@/hooks/use-artifact';

interface ScrollToBottomButtonProps {
  onClick: () => void;
  isVisible: boolean;
}

export function ScrollToBottomButton({
  onClick,
  isVisible,
}: ScrollToBottomButtonProps) {
  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      className={`absolute bottom-full -translate-x-1/2 mb-2 z-50 ${
        isArtifactVisible ? 'left-[26%]' : 'left-[48%]'
      }`}
    >
      <Button
        onClick={onClick}
        className="rounded-full p-2 shadow-lg bg-primary hover:bg-primary/90"
        aria-label="Défiler vers le bas"
        size="icon"
      >
        <ChevronDownIcon size={16} />
      </Button>
    </motion.div>
  );
}
