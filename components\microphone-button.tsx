import { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Mic } from 'lucide-react';
import { toast } from 'sonner';
import { useMicVAD, utils } from '@ricky0123/vad-react';

// Store for persisting mic state across renders
const globalMicState = {
  isActive: false,
  audioSegments: [] as Float32Array[],
  vadInstance: null as any,
};

interface MicrophoneButtonProps {
  onTranscription: (text: string) => void;
  disabled?: boolean;
  onSubmit?: () => void;
}

export function MicrophoneButton({
  onTranscription,
  disabled,
  onSubmit,
}: MicrophoneButtonProps) {
  const [isListening, setIsListening] = useState(globalMicState.isActive);
  const [isCollecting, setIsCollecting] = useState(false);
  const [transcriptionComplete, setTranscriptionComplete] = useState(false);
  const silenceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const vadInstanceRef = useRef<any>(null);
  const isProcessingRef = useRef<boolean>(false);
  const lastProcessTimeRef = useRef<number>(0);

  // Configurer VAD pour une détection plus rapide
  const vad = useMicVAD({
    startOnLoad: false,
    onSpeechEnd: async (audio) => {
      if (!globalMicState.isActive) return;

      globalMicState.audioSegments.push(audio);

      if (!isCollecting) {
        setIsCollecting(true);
        toast.info('Écoute en cours...', { duration: 1000 });
      }

      // Réinitialiser le timer de silence
      if (silenceTimerRef.current) {
        clearTimeout(silenceTimerRef.current);
      }

      // Réduire le délai d'attente après la fin de la parole
      silenceTimerRef.current = setTimeout(() => {
        processAudioSegments();
      }, 1000); // Réduit à 1 seconde au lieu de 2
    },
    // Paramètres optimisés pour une détection rapide
    positiveSpeechThreshold: 0.8, // Plus sensible
    negativeSpeechThreshold: 0.75, // Plus sensible
    minSpeechFrames: 5, // Moins de frames nécessaires (plus rapide)
    preSpeechPadFrames: 5, // Moins de padding (moins de latence)
    redemptionFrames: 25, // Moins de frames de rédemption (plus réactif)
  });

  // Référencer l'instance VAD
  useEffect(() => {
    vadInstanceRef.current = vad;
    globalMicState.vadInstance = vad;

    if (globalMicState.isActive && !vad.listening) {
      vad.start();
      setIsListening(true);
    }

    return () => {
      if (!globalMicState.isActive && vad.listening) {
        vad.pause();
      }
    };
  }, [vad]);

  // Optimiser la fonction de transcription
  const handleAudioTranscription = useCallback(
    async (audioBlob: Blob) => {
      const formData = new FormData();
      formData.append('audio', audioBlob);

      try {
        // Utiliser AbortController pour limiter le temps d'attente
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15s timeout

        const response = await fetch('/api/transcribe', {
          method: 'POST',
          body: formData,
          signal: controller.signal,
          // Priorité élevée pour la requête
          priority: 'high',
          // Pas de cache
          cache: 'no-store',
        });

        clearTimeout(timeoutId);

        const data = await response.json();
        if (response.ok) {
          // Appeler onTranscription immédiatement
          if (data.transcription?.trim()) {
            onTranscription(data.transcription);
            toast.success('Transcription terminée', { duration: 1500 });
            setTranscriptionComplete(true);
          } else {
            toast.info('Aucun texte détecté', { duration: 1500 });
          }
        } else {
          toast.error(`Échec: ${data.error || 'Erreur de transcription'}`, {
            duration: 2000,
          });
        }
      } catch (error: any) {
        if (error.name === 'AbortError') {
          toast.error('Transcription trop longue', { duration: 2000 });
        } else {
          toast.error("Erreur d'envoi audio", { duration: 2000 });
          console.error('Error sending audio:', error);
        }
      }
    },
    [onTranscription],
  );

  // Optimiser la fonction de traitement des segments audio
  const processAudioSegments = useCallback(async () => {
    // Éviter le traitement simultané et la surcharge
    if (isProcessingRef.current) return;

    // Vérifier s'il y a des segments à traiter
    if (globalMicState.audioSegments?.length === 0) {
      setIsCollecting(false);
      return;
    }

    // Éviter les traitements trop fréquents (au moins 500ms entre chaque)
    const now = Date.now();
    if (now - lastProcessTimeRef.current < 500) {
      return;
    }

    lastProcessTimeRef.current = now;
    isProcessingRef.current = true;

    toast.info('Transcription en cours...', { duration: 2000 });

    // Copier et vider les segments immédiatement pour permettre la collecte continue
    const segmentsToProcess = [...globalMicState.audioSegments];
    globalMicState.audioSegments = [];

    try {
      // Traitement optimisé des segments audio
      const totalLength = segmentsToProcess.reduce(
        (sum, segment) => sum + segment.length,
        0,
      );

      const combinedAudio = new Float32Array(totalLength);
      let offset = 0;
      for (const segment of segmentsToProcess) {
        combinedAudio.set(segment, offset);
        offset += segment.length;
      }

      const wav = utils.encodeWAV(combinedAudio);
      const blob = new Blob([wav], { type: 'audio/wav' });

      setIsCollecting(false);
      await handleAudioTranscription(blob);
    } catch (error) {
      console.error('Error processing audio:', error);
      toast.error('Erreur de traitement audio');
    } finally {
      isProcessingRef.current = false;
    }
  }, [handleAudioTranscription]);

  // Gérer la fin de la transcription
  useEffect(() => {
    if (transcriptionComplete && onSubmit) {
      // Utiliser requestAnimationFrame pour la performance
      const frameId = requestAnimationFrame(() => {
        onSubmit();
        setTranscriptionComplete(false);
      });
      return () => cancelAnimationFrame(frameId);
    }
  }, [transcriptionComplete, onSubmit]);

  // Écouter les changements de visibilité et de focus
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && globalMicState.isActive) {
        if (vadInstanceRef.current && !vadInstanceRef.current.listening) {
          vadInstanceRef.current.start();
          setIsListening(true);
        }
      }
    };

    const handleFocus = () => {
      if (globalMicState.isActive) {
        if (vadInstanceRef.current && !vadInstanceRef.current.listening) {
          vadInstanceRef.current.start();
          setIsListening(true);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange, {
      passive: true,
    });
    window.addEventListener('focus', handleFocus, { passive: true });

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  // Nettoyage
  useEffect(() => {
    return () => {
      if (silenceTimerRef.current) {
        clearTimeout(silenceTimerRef.current);
      }
    };
  }, []);

  // Fonction optimisée pour activer/désactiver le microphone
  const toggleListening = () => {
    if (isListening) {
      // Si des données sont en cours de collecte, les traiter avant de désactiver
      if (isCollecting && globalMicState.audioSegments.length > 0) {
        processAudioSegments();
      }

      vad.pause();
      setIsListening(false);
      globalMicState.isActive = false;
      toast.info('Microphone désactivé', { duration: 1500 });

      if (silenceTimerRef.current) {
        clearTimeout(silenceTimerRef.current);
        silenceTimerRef.current = null;
      }
    } else {
      globalMicState.audioSegments = [];
      setIsCollecting(false);

      vad.start();
      setIsListening(true);
      globalMicState.isActive = true;
      toast.info('Microphone activé - Parlez maintenant', { duration: 1500 });
    }
  };

  return (
    <div className="relative">
      {isListening && (
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 animate-ping rounded-full bg-orange-400 opacity-75" />
          <div className="absolute inset-[-4px] animate-pulse rounded-full bg-orange-300 opacity-50" />
          <div className="absolute inset-[-8px] animate-pulse delay-75 rounded-full bg-orange-200 opacity-25" />
        </div>
      )}
      <Button
        disabled={disabled}
        type="button"
        variant="ghost"
        size="icon"
        className={`rounded-md p-2 h-fit relative z-10 ${
          isListening ? 'text-orange-500 hover:text-orange-600' : ''
        }`}
        onClick={toggleListening}
      >
        <Mic className="size-5" />
      </Button>
    </div>
  );
}
