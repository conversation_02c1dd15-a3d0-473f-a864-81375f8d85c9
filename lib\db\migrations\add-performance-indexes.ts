import { sql } from 'drizzle-orm';
import { pgTable } from 'drizzle-orm/pg-core';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { config } from 'dotenv';

// Importer les types nécessaires
type IndexResult = { command: string };

// Charger les variables d'environnement
config();

// Vérifier que l'URL de la base de données est définie
if (!process.env.POSTGRES_URL) {
  throw new Error('POSTGRES_URL environment variable is not defined');
}

// Connexion à la base de données Supabase
// Utiliser la même configuration que dans le reste de l'application
const migrationClient = postgres(process.env.POSTGRES_URL, {
  max: 1,
  ssl: process.env.POSTGRES_URL.includes('supabase') ? { rejectUnauthorized: false } : false
});
const db = drizzle(migrationClient);

// Fonction principale pour exécuter la migration
export async function addPerformanceIndexes() {
  console.log('Adding performance indexes...');

  try {
    // Index sur chat.userId pour accélérer getChatsByUserId
    const result1 = await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_chat_userId ON "Chat" ("userId");
    `) as IndexResult[];
    console.log('Added index on Chat.userId:', result1[0]?.command || 'OK');

    // Index sur chat.createdAt pour accélérer les requêtes de tri et de pagination
    const result2 = await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_chat_createdAt ON "Chat" ("createdAt");
    `) as IndexResult[];
    console.log('Added index on Chat.createdAt:', result2[0]?.command || 'OK');

    // Index composite sur chat.userId et chat.createdAt pour optimiser getChatsByUserId
    const result3 = await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_chat_userId_createdAt ON "Chat" ("userId", "createdAt");
    `) as IndexResult[];
    console.log('Added composite index on Chat.userId and Chat.createdAt:', result3[0]?.command || 'OK');

    // Index sur message.chatId pour accélérer getMessagesByChatId
    const result4 = await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_message_chatId ON "Message_v2" ("chatId");
    `) as IndexResult[];
    console.log('Added index on Message_v2.chatId:', result4[0]?.command || 'OK');

    // Index sur message.createdAt pour accélérer les requêtes de tri
    const result5 = await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_message_createdAt ON "Message_v2" ("createdAt");
    `) as IndexResult[];
    console.log('Added index on Message_v2.createdAt:', result5[0]?.command || 'OK');

    // Index composite sur message.chatId et message.createdAt pour optimiser getMessagesByChatId
    const result6 = await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_message_chatId_createdAt ON "Message_v2" ("chatId", "createdAt");
    `) as IndexResult[];
    console.log('Added composite index on Message_v2.chatId and Message_v2.createdAt:', result6[0]?.command || 'OK');

    // Index sur vote.chatId pour accélérer getVotesByChatId
    const result7 = await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_vote_chatId ON "Vote_v2" ("chatId");
    `) as IndexResult[];
    console.log('Added index on Vote_v2.chatId:', result7[0]?.command || 'OK');

    // Index sur vote.messageId pour accélérer les requêtes de vote
    const result8 = await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_vote_messageId ON "Vote_v2" ("messageId");
    `) as IndexResult[];
    console.log('Added index on Vote_v2.messageId:', result8[0]?.command || 'OK');

    // Index sur document.id pour accélérer getDocumentsById
    const result9 = await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_document_id ON "Document" ("id");
    `) as IndexResult[];
    console.log('Added index on Document.id:', result9[0]?.command || 'OK');

    // Index sur document.createdAt pour accélérer les requêtes de tri
    const result10 = await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_document_createdAt ON "Document" ("createdAt");
    `) as IndexResult[];
    console.log('Added index on Document.createdAt:', result10[0]?.command || 'OK');

    // Index composite sur document.id et document.createdAt pour optimiser deleteDocumentsByIdAfterTimestamp
    const result11 = await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_document_id_createdAt ON "Document" ("id", "createdAt");
    `) as IndexResult[];
    console.log('Added composite index on Document.id and Document.createdAt:', result11[0]?.command || 'OK');

    // Index sur suggestion.documentId pour accélérer getSuggestionsByDocumentId
    const result12 = await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_suggestion_documentId ON "Suggestion" ("documentId");
    `) as IndexResult[];
    console.log('Added index on Suggestion.documentId:', result12[0]?.command || 'OK');

    console.log('All performance indexes added successfully');
  } catch (error) {
    console.error('Error adding performance indexes:', error);
    throw error;
  } finally {
    try {
      await migrationClient.end();
      console.log('Database connection closed successfully');
    } catch (err) {
      console.error('Error closing database connection:', err);
    }
  }
}

// Exécuter la migration si ce fichier est appelé directement
if (require.main === module) {
  addPerformanceIndexes()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
