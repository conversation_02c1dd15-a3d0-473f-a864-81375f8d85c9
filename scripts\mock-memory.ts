// Script pour créer un mock du service de mémoire pendant les tests
export function setupMemoryMock() {
  // Créer un mock complet du client Mem0
  return {
    add: async () => {
      console.log('[MOCK] Memory add called');
      return [{ id: 'mock-memory-id', content: 'mock content' }];
    },
    search: async () => {
      console.log('[MOCK] Memory search called');
      return [{ id: 'mock-result-id', content: 'mock search result' }];
    },
    // Ajouter d'autres méthodes mockées si nécessaire
  };
}
