'use client';

import React, { useEffect, useRef, useState } from 'react';
import type { Place } from '@/lib/types';
import LocationSidebar from './LocationSidebar';

interface MapComponentProps {
  places: Place[];
  query: string;
}

const MapComponent: React.FC<MapComponentProps> = ({ places, query }) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const markersRef = useRef<any[]>([]);
  const [mapInitialized, setMapInitialized] = useState(false);

  // Fonction pour mettre à jour les marqueurs sans réinitialiser la carte
  const updateMapMarkers = () => {
    console.log('Updating map markers');

    if (!mapInstanceRef.current) {
      console.error('Map instance not available for updating markers');
      return;
    }

    try {
      // @ts-ignore
      const L = window.L;
      if (!L) {
        console.error('Leaflet not loaded');
        return;
      }

      const map = mapInstanceRef.current;

      // Supprimer les marqueurs existants
      markersRef.current.forEach((marker) => {
        marker.remove();
      });
      markersRef.current = [];

      // Ajouter les nouveaux marqueurs
      const newMarkers = places.map((place, index) => {
        console.log(
          `Adding marker ${index} at [${place.latitude}, ${place.longitude}]`,
        );

        const marker = L.marker([place.latitude, place.longitude])
          .addTo(map)
          .bindPopup(`
            <strong>${place.title}</strong><br>
            ${place.address}<br>
            ${place.category}<br>
            ${place.rating ? `Rating: ${place.rating}/5` : ''}
            ${place.phoneNumber ? `<br>Phone: ${place.phoneNumber}` : ''}
            ${place.website ? `<br><a href="${place.website}" target="_blank">Website</a>` : ''}
          `);
        return marker;
      });

      markersRef.current = newMarkers;

      // Si nous avons plusieurs marqueurs, ajuster la vue pour les montrer tous
      if (newMarkers.length > 1) {
        console.log('Fitting map to bounds of all markers');
        const group = L.featureGroup(newMarkers);
        map.fitBounds(group.getBounds().pad(0.1));
      } else if (newMarkers.length === 1) {
        // Si nous n'avons qu'un seul marqueur, centrer la carte sur celui-ci
        const place = places[0];
        map.setView([place.latitude, place.longitude], 13);
      }

      // Forcer une mise à jour de la taille pour s'assurer que la carte s'affiche correctement
      setTimeout(() => {
        map.invalidateSize();
        console.log('Map size invalidated after updating markers');
      }, 100);

      console.log(`Updated ${newMarkers.length} markers`);
    } catch (error) {
      console.error('Error updating map markers:', error);
      throw error; // Propager l'erreur pour que la carte soit réinitialisée
    }
  };

  useEffect(() => {
    console.log('MapComponent useEffect triggered', {
      placesLength: places.length,
      hasMapRef: !!mapRef.current,
      mapInitialized,
    });

    // Skip if no places
    if (places.length === 0) {
      console.log('Skipping map initialization: no places');
      return;
    }

    // Attendre que le DOM soit prêt et que la référence soit disponible
    const checkRef = () => {
      if (!mapRef.current) {
        console.log('Map ref not available yet, waiting...');
        // Réessayer dans 100ms
        setTimeout(checkRef, 100);
        return;
      }

      console.log('Map ref is now available, proceeding with initialization');
      initMap();
    };

    // Fonction pour initialiser la carte une fois que la référence est disponible
    const initMap = () => {

    // Si la carte est déjà initialisée et que nous avons les mêmes places, ne pas réinitialiser
    if (mapInitialized && mapInstanceRef.current) {
      console.log('Map already initialized, updating markers only');
      try {
        // Mettre à jour les marqueurs sans réinitialiser toute la carte
        updateMapMarkers();
        return;
      } catch (error) {
        console.error('Error updating markers, will reinitialize map:', error);
        // En cas d'erreur, continuer pour réinitialiser la carte
      }
    }

    // Load Leaflet CSS
    if (!document.querySelector('link[href*="leaflet.css"]')) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
      link.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=';
      link.crossOrigin = '';
      document.head.appendChild(link);
    }

    // Load Leaflet JS
    const loadLeaflet = async () => {
      try {
        // @ts-ignore
        if (typeof window !== 'undefined' && !window.L) {
          console.log('Loading Leaflet script...');
          await new Promise<void>((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
            script.crossOrigin = '';
            script.onload = () => {
              console.log('Leaflet script loaded successfully');
              resolve();
            };
            script.onerror = (e) => {
              console.error('Error loading Leaflet script:', e);
              reject(e);
            };
            document.head.appendChild(script);
          });
        } else {
          console.log('Leaflet already loaded or window not defined');
        }

        // Petit délai pour s'assurer que Leaflet est bien chargé
        setTimeout(() => {
          initializeMap();
        }, 500);
      } catch (error) {
        console.error('Error in loadLeaflet:', error);
      }
    };

    const initializeMap = () => {
      console.log('Initializing map...');

      if (typeof window === 'undefined') {
        console.error('Window is not defined');
        return;
      }

      // @ts-ignore
      const L = window.L;
      if (!L) {
        console.error('Leaflet not loaded');
        return;
      }

      if (!mapRef.current) {
        console.error('Map container ref is not available');
        return;
      }

      try {
        console.log('Places data:', places);

        // Cleanup any existing map instance
        if (mapInstanceRef.current) {
          console.log('Cleaning up existing map instance');
          mapInstanceRef.current.remove();
          mapInstanceRef.current = null;
        }

        // Get the first place coordinates or default to Paris
        const firstPlace = places[0];
        const initialLat = firstPlace?.latitude || 48.8566;
        const initialLng = firstPlace?.longitude || 2.3522;

        console.log(`Setting initial view to: [${initialLat}, ${initialLng}]`);

        // Create the map
        const map = L.map(mapRef.current).setView([initialLat, initialLng], 13);
        mapInstanceRef.current = map;

        console.log('Map created');

        // Add the OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution:
            '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          maxZoom: 19,
        }).addTo(map);

        console.log('Tile layer added');

        // Add markers for all places
        markersRef.current = places.map((place, index) => {
          console.log(
            `Adding marker ${index} at [${place.latitude}, ${place.longitude}]`,
          );

          const marker = L.marker([place.latitude, place.longitude])
            .addTo(map)
            .bindPopup(`
              <strong>${place.title}</strong><br>
              ${place.address}<br>
              ${place.category}<br>
              ${place.rating ? `Rating: ${place.rating}/5` : ''}
              ${place.phoneNumber ? `<br>Phone: ${place.phoneNumber}` : ''}
              ${place.website ? `<br><a href="${place.website}" target="_blank">Website</a>` : ''}
            `);
          return marker;
        });

        console.log(`Added ${markersRef.current.length} markers`);

        // If we have multiple markers, fit the map to show all of them
        if (markersRef.current.length > 1) {
          console.log('Fitting map to bounds of all markers');
          const group = L.featureGroup(markersRef.current);
          map.fitBounds(group.getBounds().pad(0.1));
        }

        // Force a resize to ensure the map renders correctly
        setTimeout(() => {
          map.invalidateSize();
          console.log('Map size invalidated');
        }, 100);

        setMapInitialized(true);
        console.log('Map initialization complete');
      } catch (error) {
        console.error('Error initializing map:', error);
      }
    };

    loadLeaflet();

    // Cleanup function
    return () => {
      // Cleanup map instance when component unmounts
      if (mapInstanceRef.current) {
        console.log('Cleaning up map instance on unmount');
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
      setMapInitialized(false);
    };
  }, [places, mapInitialized]);

  // Ne réinitialisons pas la carte à chaque changement de places
  // Cela évite le clignotement
  // useEffect(() => {
  //   setMapInitialized(false);
  // }, [places]);

  if (places.length === 0) {
    return null;
  }

  return (
    <div className="mt-4 rounded-lg overflow-hidden shadow-lg">
      <div className="bg-white dark:bg-gray-800 p-4">
        <h2 className="text-lg font-semibold mb-2 text-black dark:text-white">
          Map Results for: {query}
        </h2>
        <div
          ref={mapRef}
          className="h-[400px] w-full rounded-lg border border-gray-200 dark:border-gray-700"
        />
      </div>
      <LocationSidebar places={places} />
    </div>
  );
};

export default MapComponent;
