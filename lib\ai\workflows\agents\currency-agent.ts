import { z } from 'zod';
import { generateObject, type LanguageModelV1 } from 'ai';
import type {
  DestinationInfo,
  CurrencyInfo,
} from '../types';

/**
 * CurrencyAgent is responsible for detecting and providing currency information
 * for travel destinations using LLM inference.
 */
export class CurrencyAgent {
  private model: LanguageModelV1;
  private cache: Map<string, CurrencyInfo>;

  constructor(model: LanguageModelV1) {
    this.model = model;
    this.cache = new Map();
  }

  /**
   * Get currency information for a destination
   */
  async getCurrencyForDestination(
    destinationInfo: DestinationInfo,
  ): Promise<CurrencyInfo> {
    console.log('🔍 [CurrencyAgent] Getting currency for destination:', {
      destination: destinationInfo.destination,
      country: destinationInfo.country,
    });

    const cacheKey = `${destinationInfo.destination.toLowerCase()}_${destinationInfo.country?.toLowerCase() || ''}`;

    // Check cache first
    const cachedCurrency = this.cache.get(cacheKey);
    if (cachedCurrency) {
      console.log('💾 [CurrencyAgent] Using cached currency:', cachedCurrency);
      return cachedCurrency;
    }

    try {
      // First, get basic currency information using web search
      const webSearchResults =
        await this.searchCurrencyInformation(destinationInfo);

      // Search for images of currency denominations
      const currencyImages = await this.searchCurrencyImages(destinationInfo);

      // Then use LLM to structure and enhance the information
      const { object: currencyData } = await generateObject({
        model: this.model,
        system: `You are an expert in world currencies and financial systems. 
        Your task is to analyze web search results about a country's currency and provide detailed, structured information about all current bills and coins in circulation.
        Use the provided web search results to ensure accuracy and up-to-date information.
        
        IMPORTANT: Do NOT assign imageUrl values in your response. The images will be matched separately to ensure accuracy.`,
        prompt: `Destination: ${destinationInfo.destination}
        ${destinationInfo.country ? `Country: ${destinationInfo.country}` : ''}
        
        Web search results about the currency:
        ${webSearchResults}
        
        Available currency images:
        ${currencyImages}
        
        Based on the web search results above, please provide the complete official currency information for this location, including:
        1. Basic currency details (code, symbol, name, country)
        2. All current banknotes (bills) in circulation with their exact values, colors, and descriptions
        3. All current coins in circulation with their exact values, materials, and descriptions
        
        Focus on the denominations that are currently in active use by travelers and locals. Use the web search results to ensure accuracy.
        
        IMPORTANT NOTES:
        - Provide exact denomination values (e.g., 1, 5, 10, 20, 50, 100 for bills)
        - Include accurate colors and materials based on the search results
        - Do NOT include imageUrl fields - these will be added separately
        - Ensure all denominations are currently in circulation (not obsolete)`,
        schema: z.object({
          code: z
            .string()
            .length(3)
            .describe('ISO 4217 currency code (e.g., USD, EUR)'),
          symbol: z.string().describe('Currency symbol (e.g., $, €)'),
          name: z.string().describe('Full name of the currency in English'),
          country: z.string().describe('Country name in English'),
          denominations: z
            .object({
              bills: z
                .array(
                  z.object({
                    value: z.number().describe('Face value of the bill'),
                    type: z
                      .literal('bill')
                      .describe('Type indicator for bills'),
                    description: z
                      .string()
                      .optional()
                      .describe(
                        'Brief description of the bill (color, notable features)',
                      ),
                    color: z
                      .string()
                      .optional()
                      .describe('Primary color of the bill'),
                    material: z
                      .string()
                      .optional()
                      .describe('Material (e.g., paper, polymer)'),
                    size: z
                      .string()
                      .optional()
                      .describe(
                        'Size description (e.g., small, medium, large)',
                      ),
                    imageUrl: z
                      .string()
                      .optional()
                      .describe('URL of the bill image if available'),
                  }),
                )
                .describe('Array of all current banknotes in circulation'),
              coins: z
                .array(
                  z.object({
                    value: z.number().describe('Face value of the coin'),
                    type: z
                      .literal('coin')
                      .describe('Type indicator for coins'),
                    description: z
                      .string()
                      .optional()
                      .describe('Brief description of the coin'),
                    color: z
                      .string()
                      .optional()
                      .describe('Primary color/material of the coin'),
                    material: z
                      .string()
                      .optional()
                      .describe('Material (e.g., copper, silver, nickel)'),
                    size: z
                      .string()
                      .optional()
                      .describe(
                        'Size description (e.g., small, medium, large)',
                      ),
                    imageUrl: z
                      .string()
                      .optional()
                      .describe('URL of the coin image if available'),
                  }),
                )
                .describe('Array of all current coins in circulation'),
            })
            .describe('Complete denomination information for bills and coins'),
        }),
      });

      console.log('✅ [CurrencyAgent] Retrieved currency data:', currencyData);

      // Search for specific images for each denomination
      if (currencyData.denominations) {
        console.log(
          '🔍 [CurrencyAgent] Searching for specific denomination images...',
        );
        const updatedDenominations =
          await this.searchSpecificDenominationImages(
            destinationInfo,
            currencyData.code,
            currencyData.denominations,
          );

        currencyData.denominations = updatedDenominations;
        console.log(
          '✅ [CurrencyAgent] Updated currency data with specific images',
        );
      }

      // Cache the result
      this.cache.set(cacheKey, currencyData);

      return currencyData;
    } catch (error) {
      console.error('Error detecting currency with LLM:', error);
      throw new Error(
        'Failed to determine currency information. Please try again.',
      );
    }
  }

  /**
   * Search for currency information using Serper API
   */
  private async searchCurrencyInformation(
    destinationInfo: DestinationInfo,
  ): Promise<string> {
    const apiKey = process.env.SERPER_API as string;
    if (!apiKey) {
      console.warn(
        'Serper API key not found, falling back to LLM-only approach',
      );
      return 'No web search results available. Please use your knowledge to provide currency information.';
    }

    try {
      const country = destinationInfo.country || destinationInfo.destination;

      // Search for current banknotes and coins information
      const searchQueries = [
        `${country} currency banknotes bills current circulation 2024`,
        `${country} coins currency denominations current 2024`,
        `${country} money bills coins official currency guide`,
      ];

      let allResults = '';

      for (const query of searchQueries) {
        console.log(`🔍 [CurrencyAgent] Searching: ${query}`);

        const response = await fetch('https://google.serper.dev/search', {
          method: 'POST',
          headers: {
            'X-API-KEY': apiKey,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            q: query,
            num: 5, // Limit results to avoid too much data
          }),
        });

        if (!response.ok) {
          console.error(`Serper API error: ${response.status}`);
          continue;
        }

        const data = await response.json();

        if (data.organic) {
          const results = data.organic
            .slice(0, 3)
            .map((result: any) => {
              return `Title: ${result.title}\nSnippet: ${result.snippet}\nSource: ${result.link}`;
            })
            .join('\n\n');

          allResults += `\n--- Results for "${query}" ---\n${results}\n`;
        }

        // Add a small delay to respect rate limits
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      console.log('✅ [CurrencyAgent] Web search completed');
      return (
        allResults ||
        'No relevant web search results found. Please use your knowledge to provide currency information.'
      );
    } catch (error) {
      console.error('Error searching currency information:', error);
      return 'Web search failed. Please use your knowledge to provide currency information.';
    }
  }

  /**
   * Search for currency images using Serper API
   */
  private async searchCurrencyImages(
    destinationInfo: DestinationInfo,
  ): Promise<string> {
    const apiKey = process.env.SERPER_API as string;
    if (!apiKey) {
      return 'No currency images available.';
    }

    try {
      const country = destinationInfo.country || destinationInfo.destination;

      // Search for currency images
      const imageQuery = `${country} currency banknotes coins money bills official`;

      console.log(`🖼️ [CurrencyAgent] Searching images: ${imageQuery}`);

      const response = await fetch('https://google.serper.dev/images', {
        method: 'POST',
        headers: {
          'X-API-KEY': apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: imageQuery,
          num: 10, // Get more images to have options
        }),
      });

      if (!response.ok) {
        console.error(`Serper Images API error: ${response.status}`);
        return 'No currency images available.';
      }

      const data = await response.json();

      if (data.images && data.images.length > 0) {
        const imageResults = data.images
          .slice(0, 8)
          .map((image: any, index: number) => {
            return `Image ${index + 1}: ${image.imageUrl} (Title: ${image.title})`;
          })
          .join('\n');

        console.log('✅ [CurrencyAgent] Currency images found');
        return imageResults;
      }

      return 'No currency images found.';
    } catch (error) {
      console.error('Error searching currency images:', error);
      return 'Currency image search failed.';
    }
  }

  /**
   * Search for specific denomination images
   */
  private async searchSpecificDenominationImages(
    destinationInfo: DestinationInfo,
    currencyCode: string,
    denominations: { bills: any[]; coins: any[] },
  ): Promise<{ bills: any[]; coins: any[] }> {
    const apiKey = process.env.SERPER_API as string;
    if (!apiKey) {
      console.warn('Serper API key not found, skipping specific image search');
      return denominations;
    }

    const country = destinationInfo.country || destinationInfo.destination;

    try {
      // Search for specific bill images
      const updatedBills = await Promise.all(
        denominations.bills.map(async (bill) => {
          try {
            const billQuery = `${country} ${currencyCode} ${bill.value} banknote bill currency official`;
            console.log(
              `🔍 [CurrencyAgent] Searching for specific bill: ${billQuery}`,
            );

            const response = await fetch('https://google.serper.dev/images', {
              method: 'POST',
              headers: {
                'X-API-KEY': apiKey,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                q: billQuery,
                num: 3, // Get fewer but more specific results
              }),
            });

            if (response.ok) {
              const data = await response.json();
              if (data.images && data.images.length > 0) {
                // Find the most relevant image based on title and URL matching
                const relevantImage =
                  data.images.find((img: any) => {
                    const title = (img.title || '').toLowerCase();
                    const url = (img.imageUrl || '').toLowerCase();
                    const valueStr = bill.value.toString();

                    return (
                      // Check title for exact value match
                      title.includes(valueStr) ||
                      title.includes(
                        `${valueStr} ${currencyCode.toLowerCase()}`,
                      ) ||
                      title.includes(
                        `${currencyCode.toLowerCase()} ${valueStr}`,
                      ) ||
                      title.includes(
                        `${valueStr}${currencyCode.toLowerCase()}`,
                      ) ||
                      // Check URL for value patterns
                      url.includes(valueStr) ||
                      url.includes(
                        `${valueStr}${currencyCode.toLowerCase()}`,
                      ) ||
                      // Check for banknote/bill keywords with value
                      (title.includes('banknote') &&
                        title.includes(valueStr)) ||
                      (title.includes('bill') && title.includes(valueStr))
                    );
                  }) || data.images[0]; // Fallback to first image

                // Validate image URL before assigning
                const validatedImageUrl = await this.validateImageUrl(
                  relevantImage.imageUrl,
                );

                return {
                  ...bill,
                  imageUrl: validatedImageUrl,
                };
              }
            }

            // Add small delay to respect rate limits
            await new Promise((resolve) => setTimeout(resolve, 200));

            // Log that no specific image was found for this bill
            console.log(
              `ℹ️ [CurrencyAgent] No specific image found for ${currencyCode} ${bill.value} bill`,
            );
            return bill;
          } catch (error) {
            console.error(`Error searching for bill ${bill.value}:`, error);
            return bill;
          }
        }),
      );

      // Search for specific coin images
      const updatedCoins = await Promise.all(
        denominations.coins.map(async (coin) => {
          try {
            const coinQuery = `${country} ${currencyCode} ${coin.value} coin currency official`;
            console.log(
              `🔍 [CurrencyAgent] Searching for specific coin: ${coinQuery}`,
            );

            const response = await fetch('https://google.serper.dev/images', {
              method: 'POST',
              headers: {
                'X-API-KEY': apiKey,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                q: coinQuery,
                num: 3, // Get fewer but more specific results
              }),
            });

            if (response.ok) {
              const data = await response.json();
              if (data.images && data.images.length > 0) {
                // Find the most relevant image based on title and URL matching
                const relevantImage =
                  data.images.find((img: any) => {
                    const title = (img.title || '').toLowerCase();
                    const url = (img.imageUrl || '').toLowerCase();
                    const valueStr = coin.value.toString();
                    const centValue =
                      coin.value < 1 ? (coin.value * 100).toString() : null;

                    return (
                      // Check title for exact value match
                      title.includes(valueStr) ||
                      title.includes(
                        `${valueStr} ${currencyCode.toLowerCase()}`,
                      ) ||
                      title.includes(
                        `${currencyCode.toLowerCase()} ${valueStr}`,
                      ) ||
                      title.includes(
                        `${valueStr}${currencyCode.toLowerCase()}`,
                      ) ||
                      // Check for cent values if applicable
                      (centValue &&
                        (title.includes(centValue) ||
                          title.includes(`${centValue}c`))) ||
                      // Check URL for value patterns
                      url.includes(valueStr) ||
                      url.includes(
                        `${valueStr}${currencyCode.toLowerCase()}`,
                      ) ||
                      // Check for coin keywords with value
                      (title.includes('coin') && title.includes(valueStr)) ||
                      (centValue &&
                        title.includes('coin') &&
                        title.includes(centValue))
                    );
                  }) || data.images[0]; // Fallback to first image

                // Validate image URL before assigning
                const validatedImageUrl = await this.validateImageUrl(
                  relevantImage.imageUrl,
                );

                return {
                  ...coin,
                  imageUrl: validatedImageUrl,
                };
              }
            }

            // Add small delay to respect rate limits
            await new Promise((resolve) => setTimeout(resolve, 200));

            // Log that no specific image was found for this coin
            console.log(
              `ℹ️ [CurrencyAgent] No specific image found for ${currencyCode} ${coin.value} coin`,
            );
            return coin;
          } catch (error) {
            console.error(`Error searching for coin ${coin.value}:`, error);
            return coin;
          }
        }),
      );

      console.log(
        '✅ [CurrencyAgent] Specific denomination images search completed',
      );
      return {
        bills: updatedBills,
        coins: updatedCoins,
      };
    } catch (error) {
      console.error('Error in specific denomination image search:', error);
      return denominations;
    }
  }

  /**
   * Validate if an image URL is accessible and returns a valid image
   */
  private async validateImageUrl(
    imageUrl: string,
  ): Promise<string | undefined> {
    if (!imageUrl) return undefined;

    try {
      // Check if URL is valid
      const url = new URL(imageUrl);

      // Only allow https URLs for security
      if (url.protocol !== 'https:') {
        console.warn(`[CurrencyAgent] Skipping non-HTTPS image: ${imageUrl}`);
        return undefined;
      }

      // Check if the URL points to a valid image format
      const validExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
      const hasValidExtension = validExtensions.some((ext) =>
        url.pathname.toLowerCase().includes(ext),
      );

      // If no extension in path, check if it's from a known image service
      const knownImageServices = [
        'images.unsplash.com',
        'cdn.pixabay.com',
        'images.pexels.com',
        'upload.wikimedia.org',
        'commons.wikimedia.org',
        'cdn.britannica.com',
        'www.worldatlas.com',
      ];

      const isFromKnownService = knownImageServices.some((service) =>
        url.hostname.includes(service),
      );

      if (!hasValidExtension && !isFromKnownService) {
        console.warn(
          `[CurrencyAgent] Skipping potentially invalid image URL: ${imageUrl}`,
        );
        return undefined;
      }

      // Try to fetch the image with a HEAD request to check if it exists
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(imageUrl, {
        method: 'HEAD',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const contentType = response.headers.get('content-type');
        if (contentType?.startsWith('image/')) {
          console.log(`✅ [CurrencyAgent] Validated image URL: ${imageUrl}`);
          return imageUrl;
        } else {
          console.warn(
            `[CurrencyAgent] URL does not return an image: ${imageUrl}`,
          );
          return undefined;
        }
      } else {
        console.warn(
          `[CurrencyAgent] Image URL not accessible (${response.status}): ${imageUrl}`,
        );
        return undefined;
      }
    } catch (error) {
      console.warn(
        `[CurrencyAgent] Error validating image URL ${imageUrl}:`,
        error,
      );
      return undefined;
    }
  }
}

// Currency detection now uses web search combined with LLM analysis
