import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { generateAdvancedTripPlan } from '@/lib/ai/workflows/advanced-trip-planning';
import { createDataStreamResponse, type DataStreamWriter } from 'ai';
import { saveDocument } from '@/lib/db/queries';
import { generateUUID } from '@/lib/utils';

// Increase timeout for complex trip planning operations with comprehensive web search
export const maxDuration = 240; // 4 minutes (increased for comprehensive web search)

/**
 * API route handler for advanced trip planning
 */
export async function POST(req: NextRequest) {
  try {
    // Get the user session
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse the request body
    const body = await req.json();
    const { query } = body;

    if (!query) {
      return NextResponse.json(
        { error: 'Missing required parameter: query' },
        { status: 400 },
      );
    }

    // Generate a unique ID for the artifact
    const artifactId = generateUUID();

    // Create the artifact in the database
    await saveDocument({
      id: artifactId,
      userId: session.user.id,
      kind: 'html',
      title: `Advanced Trip Plan: ${query}`,
      content: '',
    });

    // Return a data stream response
    return createDataStreamResponse({
      execute: async (dataStream: DataStreamWriter) => {
        try {
          // Send initial data to the stream
          dataStream.writeData({ type: 'kind', content: 'html' });
          dataStream.writeData({ type: 'id', content: artifactId });
          dataStream.writeData({
            type: 'title',
            content: `Advanced Trip Plan: ${query}`,
          });
          dataStream.writeData({ type: 'clear', content: '' });

          // Generate the trip plan
          const result = await generateAdvancedTripPlan({
            query,
            dataStream,
          });

          // Update the artifact with the final content
          const finalContent = JSON.stringify({
            htmlContent: result.htmlContent,
            cssContent: result.cssContent,
            jsContent: result.jsContent,
          });

          // Update the document with the final content
          await saveDocument({
            id: artifactId,
            userId: session.user.id,
            kind: 'html',
            title: `Advanced Trip Plan: ${query}`,
            content: finalContent,
          });

          // Send explicit completion signal first
          dataStream.writeData({
            type: 'completion',
            content: 'workflow_complete',
          });

          // Send finish signal
          dataStream.writeData({ type: 'finish', content: '' });

          console.log('Advanced trip planning completed successfully');
        } catch (error) {
          console.error('Error generating advanced trip plan:', error);
          throw error;
        }
      },
    });
  } catch (error) {
    console.error('Error in advanced trip planning API route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
