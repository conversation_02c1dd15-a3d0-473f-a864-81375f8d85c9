// lib/blobUploader.ts
import { put as defaultPut } from '@vercel/blob';

/**
 * Fonction d'upload avec injection de dépendance.
 * @param putFn - La fonction d'upload (par défaut : vercelBlob.put).
 */
export function createUploader(putFn = defaultPut) {
  return async (filename: string, data: any, options: any) => {
    // S'assurer que l'option access est définie sur 'public'
    const optionsWithAccess = {
      ...options,
      access: 'public',
    };

    return await putFn(filename, data, optionsWithAccess);
  };
}
