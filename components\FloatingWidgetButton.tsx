'use client';

import React from 'react';
import { ExternalLink } from 'lucide-react';
import {
  useFloatingWidgets,
  generateWidgetId,
  WIDGET_CONFIGS,
} from './FloatingWidgetManager';

// Import all TradingView components
import HeatmapsMarket from './HeatmapsMarket';
import CryptoCoinsHeatmap from './CryptoCoinsHeatmap';
import ETFHeatmap from './ETFHeatmap';
import ForexCrossRates from './ForexCrossRates';
import ForexHeatmap from './ForexHeatmap';
import CryptocurrencyMarket from './CryptocurrencyMarket';
import SymbolInfo from './SymbolInfo';
import TechnicalAnalysis from './TechnicalAnalysis';
import CompanyProfile from './CompanyProfile';
import EconomicCalendar from './EconomicCalendar';
import StockScreener from './StockScreener';
import StockFinancials from './StockFinancials';
import StockPrice from './StockPrice';
import MarketTrending from './MarketTrending';
import FinancialChart from './FinancialChart';
import StockNews from './StockNews';

interface FloatingWidgetButtonProps {
  widgetType: string;
  toolName: string;
  result?: any;
  toolInvocation?: any;
  className?: string;
}

export default function FloatingWidgetButton({
  widgetType,
  toolName,
  result,
  toolInvocation,
  className = '',
}: FloatingWidgetButtonProps) {
  const { openWidget } = useFloatingWidgets();

  const handleOpenFloating = () => {
    let component: React.ReactNode;
    let config = WIDGET_CONFIGS.etfHeatmap; // default
    let widgetId = generateWidgetId(widgetType);
    let title = 'Trading Widget';

    // Determine component and configuration based on widget type
    switch (widgetType) {
      case 'heatmaps_market':
        component = <HeatmapsMarket />;
        config = WIDGET_CONFIGS.etfHeatmap;
        title = 'Market Heatmap';
        break;

      case 'crypto_coins_heatmap':
        component = <CryptoCoinsHeatmap />;
        config = WIDGET_CONFIGS.cryptoMarket;
        title = 'Crypto Coins Heatmap';
        break;

      case 'etf_heatmap':
        component = <ETFHeatmap />;
        config = WIDGET_CONFIGS.etfHeatmap;
        title = 'ETF Heatmap';
        break;

      case 'forex_cross_rates':
        component = <ForexCrossRates />;
        config = WIDGET_CONFIGS.forexCrossRates;
        title = 'Forex Cross Rates';
        break;

      case 'forex_heatmap':
        component = <ForexHeatmap />;
        config = WIDGET_CONFIGS.forexHeatmap;
        title = 'Forex Heatmap';
        break;

      case 'cryptocurrency_market':
        component = <CryptocurrencyMarket />;
        config = WIDGET_CONFIGS.cryptoMarket;
        title = 'Cryptocurrency Market';
        break;

      case 'economic_calendar':
        component = <EconomicCalendar />;
        config = WIDGET_CONFIGS.economicCalendar;
        title = 'Economic Calendar';
        break;

      case 'symbol_info':
        const symbolInfoSymbol =
          result?.data?.symbol || toolInvocation?.args?.symbol;
        component = <SymbolInfo symbol={symbolInfoSymbol} />;
        config = WIDGET_CONFIGS.symbolInfo;
        title = `Symbol Info - ${symbolInfoSymbol || 'Unknown'}`;
        widgetId = generateWidgetId(widgetType, symbolInfoSymbol);
        break;

      case 'technical_analysis':
        const taSymbol = result?.data?.symbol || toolInvocation?.args?.symbol;
        const taInterval =
          result?.data?.interval || toolInvocation?.args?.interval;
        component = (
          <TechnicalAnalysis symbol={taSymbol} interval={taInterval} />
        );
        config = WIDGET_CONFIGS.technicalAnalysis;
        title = `Technical Analysis - ${taSymbol || 'Unknown'}`;
        widgetId = generateWidgetId(widgetType, taSymbol);
        break;

      case 'company_profile':
        const cpSymbol = result?.data?.symbol || toolInvocation?.args?.symbol;
        component = <CompanyProfile symbol={cpSymbol} />;
        config = WIDGET_CONFIGS.companyProfile;
        title = `Company Profile - ${cpSymbol || 'Unknown'}`;
        widgetId = generateWidgetId(widgetType, cpSymbol);
        break;

      case 'stock_screener':
        component = <StockScreener />;
        config = WIDGET_CONFIGS.stockScreener;
        title = 'Stock Screener';
        break;

      case 'stock_financials':
        const sfSymbol = result?.ticker || toolInvocation?.args?.ticker;
        component = <StockFinancials symbol={sfSymbol} />;
        config = WIDGET_CONFIGS.stockFinancials;
        title = `Stock Financials - ${sfSymbol || 'Unknown'}`;
        widgetId = generateWidgetId(widgetType, sfSymbol);
        break;

      case 'stock_price':
        const spSymbol = result?.ticker || toolInvocation?.args?.ticker;
        component = <StockPrice symbol={spSymbol} />;
        config = WIDGET_CONFIGS.stockPrice;
        title = `Stock Price - ${spSymbol || 'Unknown'}`;
        widgetId = generateWidgetId(widgetType, spSymbol);
        break;

      case 'market_trending':
        component = <MarketTrending />;
        config = WIDGET_CONFIGS.marketTrending;
        title = 'Market Trending';
        break;

      case 'financial_chart':
        const fcSymbol = result?.ticker || toolInvocation?.args?.ticker;
        component = <FinancialChart ticker={fcSymbol} />;
        config = WIDGET_CONFIGS.financialChart;
        title = `Financial Chart - ${fcSymbol || 'Unknown'}`;
        widgetId = generateWidgetId(widgetType, fcSymbol);
        break;

      case 'stock_news':
        const snSymbol = result?.ticker || toolInvocation?.args?.ticker;
        component = <StockNews ticker={snSymbol} />;
        config = WIDGET_CONFIGS.stockNews;
        title = `Stock News - ${snSymbol || 'Unknown'}`;
        widgetId = generateWidgetId(widgetType, snSymbol);
        break;

      default:
        console.warn(`Unknown widget type: ${widgetType}`);
        return;
    }

    openWidget({
      id: widgetId,
      title,
      component,
      position: config.position,
      size: config.size,
    });
  };

  return (
    <button
      type="button"
      onClick={handleOpenFloating}
      className={`inline-flex items-center space-x-1 px-2 py-1 text-xs bg-blue-50 hover:bg-blue-100 text-blue-600 border border-blue-200 rounded transition-colors ${className}`}
      title="Open in floating window"
    >
      <ExternalLink size={12} />
      <span>Float</span>
    </button>
  );
}
