/**
 * Mock Configuration
 * 
 * This module provides configuration options for mocks in the test environment.
 * It allows enabling/disabling mocks via environment variables.
 */

/**
 * Mock configuration options
 */
export interface MockConfig {
  // Whether to use mock AI provider
  useMockAI: boolean;
  
  // Whether to use mock weather tool
  useMockWeather: boolean;
  
  // Whether to use mock authentication
  useMockAuth: boolean;
  
  // Whether to use mock database
  useMockDatabase: boolean;
  
  // Delay in milliseconds to simulate network latency
  mockLatency: number;
}

/**
 * Get mock configuration from environment variables
 * @returns The mock configuration
 */
export function getMockConfig(): MockConfig {
  return {
    useMockAI: process.env.USE_MOCK_AI !== 'false',
    useMockWeather: process.env.USE_MOCK_WEATHER !== 'false',
    useMockAuth: process.env.USE_MOCK_AUTH !== 'false',
    useMockDatabase: process.env.USE_MOCK_DATABASE !== 'false',
    mockLatency: Number.parseInt(process.env.MOCK_LATENCY || '500', 10)
  };
}

/**
 * Get a specific mock configuration option
 * @param option The configuration option to get
 * @returns The value of the configuration option
 */
export function getMockConfigOption<K extends keyof MockConfig>(option: K): MockConfig[K] {
  return getMockConfig()[option];
}

/**
 * Add a delay to simulate network latency
 * @param ms Optional milliseconds to delay (defaults to mockLatency from config)
 * @returns A promise that resolves after the delay
 */
export async function mockDelay(ms?: number): Promise<void> {
  const delay = ms ?? getMockConfig().mockLatency;
  return new Promise(resolve => setTimeout(resolve, delay));
}

/**
 * Mock response wrapper
 * @param data The data to return
 * @param options Options for the mock response
 * @returns A promise that resolves to the data after a delay
 */
export async function mockResponse<T>(
  data: T, 
  options: { 
    delay?: number;
    error?: boolean;
    errorMessage?: string;
    errorStatus?: number;
  } = {}
): Promise<T> {
  // Add a delay to simulate network latency
  await mockDelay(options.delay);
  
  // If error is true, throw an error
  if (options.error) {
    const error = new Error(options.errorMessage || 'Mock error');
    (error as any).status = options.errorStatus || 500;
    throw error;
  }
  
  return data;
}
