import { z } from 'zod';
import { generateObject, type LanguageModelV1, type DataStreamWriter } from 'ai';
import type { DestinationInfo, PointOfInterest } from '../types';
import { web_search } from '@/lib/ai/tools/web-search';

/**
 * PoiAgent is responsible for generating accurate points of interest
 * with correct coordinates for the destination.
 */
export class PoiAgent {
  private model: LanguageModelV1;

  constructor(model: LanguageModelV1) {
    this.model = model;
  }

  /**
   * Generate points of interest with accurate coordinates
   */
  async generatePointsOfInterest(destinationInfo: DestinationInfo) {
    try {
      const { destination, country, duration } = destinationInfo;

      // Check if duration is null or undefined
      if (duration === null || duration === undefined) {
        console.error('Duration is null or undefined. Cannot generate POIs.');
        return this.generateFallbackPOIs(destinationInfo);
      }

      // 1. Search for POIs in the destination
      const searchResults = await this.searchPOIs(destination, country);

      // 2. Generate POIs with accurate coordinates
      const { object: poisData } = await generateObject({
        model: this.model,
        system: `You are an expert travel guide with extensive knowledge of global destinations.
        Create a comprehensive list of points of interest (POIs) for the specified destination.
        Include attractions, restaurants, and hotels with ACCURATE coordinates.
        Each POI must have a detailed description (at least 100 characters).
        Categorize each POI correctly as 'attraction', 'restaurant', or 'hotel'.
        Assign each POI to a specific day of the itinerary.
        Include at least 8-10 POIs for each day of the trip.
        VERIFY all coordinates to ensure they are accurate for the specific location.`,
        prompt: `Create a detailed list of points of interest for ${destination}, ${country} for a ${duration}-day trip.

        Search Results:
        ${JSON.stringify(searchResults, null, 2)}

        For each point of interest, provide:
        1. Name
        2. Accurate latitude and longitude coordinates
        3. The day it should be visited (Day 1, Day 2, etc.)
        4. Type (attraction, restaurant, or hotel)
        5. A detailed description (at least 100 characters)

        Include a mix of attractions, restaurants, and hotels for each day.`,
        schema: z.object({
          pois: z
            .array(
              z.object({
                name: z.string().describe('Name of the point of interest'),
                lat: z.string().describe('Latitude coordinate'),
                lng: z.string().describe('Longitude coordinate'),
                day: z.string().describe('Day of visit (e.g., "Day 1")'),
                type: z
                  .enum(['attraction', 'restaurant', 'hotel'])
                  .describe('Type of POI'),
                description: z
                  .string()
                  .min(100)
                  .describe('Detailed description of the POI'),
              }),
            )
            .min(duration * 8)
            .describe('Points of interest for the trip'),
        }),
      });

      return poisData;
    } catch (error) {
      console.error('Error generating POIs:', error);

      // Return fallback POIs if generation fails
      return this.generateFallbackPOIs(destinationInfo);
    }
  }

  /**
   * Search for POIs in the destination using the web search tool
   */
  private async searchPOIs(destination: string, country: string): Promise<any> {
    try {
      // Create a mock session and dataStream for the web_search tool
      const mockSession = {
        user: { id: 'system', type: 'guest' as const },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };
      // Create a mock DataStreamWriter
      const mockDataStream: DataStreamWriter = {
        writeMessageAnnotation: () => {},
        write: () => '',
        writeData: () => '',
        writeSource: () => '',
        merge: () => '',
        onError: () => '',
      };

      // Use the web_search tool to get information about POIs
      const searchTool = web_search({
        session: mockSession,
        dataStream: mockDataStream,
      });
      const results = await (searchTool.execute as any)({
        queries: [
          `top attractions in ${destination} ${country} with coordinates`,
          `best restaurants in ${destination} ${country}`,
          `recommended hotels in ${destination} ${country}`,
        ],
        maxResults: [10, 5, 5],
        topics: ['general', 'general', 'general'],
      });

      return results;
    } catch (error) {
      console.error('Error searching POIs:', error);
      return { results: [] };
    }
  }

  /**
   * Generate fallback POIs in case the main generation fails
   */
  private generateFallbackPOIs(destinationInfo: DestinationInfo): {
    pois: PointOfInterest[];
  } {
    const { destination, country, duration, coordinates } = destinationInfo;

    // Use a default duration of 3 days if duration is null
    const actualDuration = duration !== null ? duration : 3;
    console.log(
      `Using fallback duration of ${actualDuration} days for POI generation`,
    );

    // Create basic POIs based on the destination
    const pois: PointOfInterest[] = [];

    // Add some default POIs for each day
    for (let day = 1; day <= actualDuration; day++) {
      // Add attractions
      for (let i = 1; i <= 5; i++) {
        pois.push({
          name: `Attraction ${i} - Day ${day}`,
          lat: this.adjustCoordinate(coordinates.lat, 0.01),
          lng: this.adjustCoordinate(coordinates.lng, 0.01),
          day: `Day ${day}`,
          type: 'attraction',
          description: `A popular attraction in ${destination}. Visitors can enjoy the local culture and history. This is a must-visit location for tourists interested in the heritage of ${country}.`,
        });
      }

      // Add restaurants
      for (let i = 1; i <= 2; i++) {
        pois.push({
          name: `Restaurant ${i} - Day ${day}`,
          lat: this.adjustCoordinate(coordinates.lat, 0.01),
          lng: this.adjustCoordinate(coordinates.lng, 0.01),
          day: `Day ${day}`,
          type: 'restaurant',
          description: `A delicious restaurant serving local cuisine. The menu features traditional dishes from ${country} prepared with fresh, local ingredients. The atmosphere is welcoming and the service is excellent.`,
        });
      }

      // Add hotel
      pois.push({
        name: `Hotel - Day ${day}`,
        lat: this.adjustCoordinate(coordinates.lat, 0.01),
        lng: this.adjustCoordinate(coordinates.lng, 0.01),
        day: `Day ${day}`,
        type: 'hotel',
        description: `A comfortable hotel located in a convenient area of ${destination}. The rooms are clean and well-appointed, and the staff is friendly and helpful. Amenities include free Wi-Fi, breakfast, and a fitness center.`,
      });
    }

    return { pois };
  }

  /**
   * Helper function to adjust coordinates slightly to create variation
   */
  private adjustCoordinate(coord: string, maxAdjustment: number): string {
    const baseCoord = Number.parseFloat(coord);
    const adjustment = (Math.random() * 2 - 1) * maxAdjustment;
    return (baseCoord + adjustment).toFixed(6);
  }
}
