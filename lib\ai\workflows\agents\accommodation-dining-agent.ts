import { z } from 'zod';
import { generateObject, type LanguageModelV1 } from 'ai';
import { myProvider } from '../../providers';
import { web_search } from '../../tools/web-search';
import type { DestinationInfo } from '../types';
import type { UserPreferences } from './preference-agent';
import type { DataStreamWriter } from 'ai';

/**
 * Interface for accommodation options - Enhanced with ultra-detailed information
 */
export interface AccommodationOption {
  name: string;
  type: string;
  description: string;
  priceRange: string;
  detailedPricing: {
    basePrice: string;
    seasonalVariations: {
      season: string;
      priceChange: string;
      dates: string;
    }[];
    taxes: string;
    additionalFees: {
      fee: string;
      amount: string;
      mandatory: boolean;
    }[];
    discounts: string[];
  };
  contactInfo: {
    address: string;
    phone: string;
    email: string;
    website: string;
    bookingPlatforms: {
      platform: string;
      url: string;
      fees: string;
    }[];
  };
  checkInOut: {
    checkInTime: string;
    checkOutTime: string;
    earlyCheckIn: string;
    lateCheckOut: string;
    process: string[];
    documentsRequired: string[];
  };
  amenities: {
    name: string;
    description: string;
    cost: string;
    hours?: string;
  }[];
  policies: {
    cancellation: string;
    pets: string;
    children: string;
    smoking: string;
    parties: string;
  };
  safetyFeatures: {
    feature: string;
    description: string;
    location: string;
  }[];
  culturalEtiquette: {
    situation: string;
    expectedBehavior: string;
    tipping: string;
  }[];
  accessibility: {
    wheelchairAccess: string;
    elevators: string;
    adaptedRooms: string;
    assistanceServices: string[];
  };
  location: string;
  coordinates: {
    lat: string;
    lng: string;
  };
  rating: string;
  specialFeatures: string[];
  insiderTips: string[];
  // Legacy fields for backward compatibility
  imageUrl?: string;
  bookingInfo?: string;
  suitableFor?: string[];
  sustainability?: string[];
  nearbyAttractions?: string[];
  reviews?: {
    rating: string;
    comment: string;
    source: string;
  }[];
}

/**
 * Interface for dining options
 */
export interface DiningOption {
  name: string;
  cuisine: string;
  description: string;
  priceRange: string;
  location: string;
  rating?: string;
  coordinates: {
    lat: string;
    lng: string;
  };
}

/**
 * Interface for accommodation and dining recommendations
 */
export interface AccommodationDiningRecommendations {
  accommodations: {
    recommended: AccommodationOption[];
    alternatives: {
      name: string;
      type: string;
      description: string;
      priceRange: string;
    }[];
    neighborhoodGuide: {
      name: string;
      description: string;
      bestFor: string[];
    }[];
    alternativeOptions: {
      type: string;
      description: string;
      priceRange: string;
    }[];
  };
  dining: {
    breakfast: DiningOption[];
    lunch: DiningOption[];
    dinner: DiningOption[];
    cafes: {
      name: string;
      description: string;
      location: string;
      priceRange: string;
    }[];
    specialOccasion: {
      name: string;
      description: string;
      priceRange: string;
    }[];
    localSpecialties: {
      dish: string;
      description: string;
      whereToTry: string[];
    }[];
    streetFood: {
      name: string;
      description: string;
      location: string;
      price: string;
    }[];
    markets: {
      name: string;
      description: string;
      location: string;
      specialties: string[];
    }[];
  };
  culinaryExperiences: {
    cookingClasses: {
      name: string;
      description: string;
      duration: string;
      price: string;
    }[];
    foodTours: {
      name: string;
      description: string;
      duration: string;
      price: string;
    }[];
    tastings: {
      name: string;
      description: string;
      price: string;
    }[];
    diningEvents: {
      name: string;
      description: string;
      price: string;
    }[];
  };
}

/**
 * AccommodationDiningAgent - Completely rewritten for reliability and rich data
 * Uses Tavily for web search and simplified schemas for Gemini API compatibility
 */
export class AccommodationDiningAgent {
  private model: LanguageModelV1;

  constructor(model: LanguageModelV1) {
    this.model = model;
  }

  /**
   * Get comprehensive accommodation and dining recommendations
   */
  async getRecommendations(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
  ): Promise<AccommodationDiningRecommendations> {
    try {
      console.log(
        '🏨 Getting accommodation and dining recommendations for:',
        destinationInfo.destination,
      );

      // Step 1: Gather comprehensive data using Tavily
      const searchData = await this.gatherComprehensiveData(
        destinationInfo,
        userPreferences,
      );

      // Step 2: Generate recommendations with simplified schema
      const recommendations = await this.generateRecommendations(
        destinationInfo,
        userPreferences,
        searchData,
      );

      console.log(
        '✅ Successfully generated accommodation and dining recommendations',
      );
      return recommendations;
    } catch (error) {
      console.error(
        '❌ Error getting accommodation and dining recommendations:',
        error,
      );
      return this.getFallbackRecommendations(destinationInfo);
    }
  }

  /**
   * Gather comprehensive data using Tavily web search
   */
  private async gatherComprehensiveData(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
  ): Promise<any> {
    try {
      console.log('🔍 Gathering comprehensive data using Tavily...');

      // Create search queries for different aspects
      const accommodationQueries = [
        `best hotels ${destinationInfo.destination} ${destinationInfo.country} ${userPreferences.budget.level}`,
        `${userPreferences.accommodation.type.join(' ')} ${destinationInfo.destination} reviews ratings`,
        `luxury boutique hotels ${destinationInfo.destination} unique accommodations`,
        `family friendly hotels ${destinationInfo.destination} amenities`,
        `budget accommodations hostels ${destinationInfo.destination} backpacker`,
      ];

      const diningQueries = [
        `best restaurants ${destinationInfo.destination} ${destinationInfo.country} local cuisine`,
        `${userPreferences.dining.cuisinePreferences.join(' ')} restaurants ${destinationInfo.destination}`,
        `street food markets ${destinationInfo.destination} local specialties`,
        `fine dining michelin restaurants ${destinationInfo.destination}`,
        `cafes breakfast lunch ${destinationInfo.destination} local favorites`,
        `food tours cooking classes ${destinationInfo.destination} culinary experiences`,
      ];

      // Add dietary restriction queries if applicable
      if (userPreferences.dining.dietaryRestrictions.length > 0) {
        diningQueries.push(
          `${userPreferences.dining.dietaryRestrictions.join(' ')} restaurants ${destinationInfo.destination}`,
        );
      }

      // Execute all searches in parallel using web_search
      const allQueries = [...accommodationQueries, ...diningQueries];
      const searchPromises = allQueries.map((query) =>
        this.searchWithWebSearch(query),
      );

      const searchResults = await Promise.all(searchPromises);

      console.log(`✅ Completed ${searchResults.length} web searches`);
      return {
        accommodationResults: searchResults.slice(
          0,
          accommodationQueries.length,
        ),
        diningResults: searchResults.slice(accommodationQueries.length),
        allResults: searchResults,
      };
    } catch (error) {
      console.error('❌ Error gathering comprehensive data:', error);
      return { accommodationResults: [], diningResults: [], allResults: [] };
    }
  }

  /**
   * Search using web_search tool (which uses Tavily internally)
   */
  private async searchWithWebSearch(query: string): Promise<any> {
    try {
      const mockSession = {
        user: { id: 'system', type: 'guest' as const },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };

      const mockDataStream: DataStreamWriter = {
        writeMessageAnnotation: () => {},
        write: () => '',
        writeData: () => '',
        writeSource: () => '',
        merge: () => '',
        onError: () => '',
      };

      const webSearchTool = web_search({
        session: mockSession,
        dataStream: mockDataStream,
      });

      const result = await (webSearchTool.execute as any)({
        queries: [query],
        maxResults: [5],
        topics: ['general'],
        searchDepth: ['advanced'],
      });

      return result;
    } catch (error) {
      console.error(
        `❌ Error searching with web_search for query "${query}":`,
        error,
      );
      return { searches: [] };
    }
  }

  /**
   * Generate recommendations using simplified schema for Gemini API compatibility
   */
  private async generateRecommendations(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
    searchData: any,
  ): Promise<AccommodationDiningRecommendations> {
    try {
      console.log('🤖 Generating recommendations with AI...');

      const { object: recommendations } = await generateObject({
        model: myProvider.languageModel('artifact-model'),
        system: `You are an expert travel consultant with 20+ years of experience specializing in accommodations and dining.
        Create ULTRA-DETAILED, comprehensive recommendations based on real search data.

        OBJECTIVE: Generate the most detailed and practical travel information possible. Every recommendation should include:
        - Exact addresses, phone numbers, websites, booking links
        - Detailed pricing with seasonal variations and hidden costs
        - Specific cultural etiquette and safety tips for each establishment
        - Insider knowledge that only locals would know
        - Step-by-step instructions for reservations, check-in, dining protocols
        - Accessibility details, family-friendly features, dietary accommodations
        - Local customs, tipping practices, dress codes, behavior expectations

        STYLE: Exhaustive detail with maximum practical value - treat this as the ultimate insider's guide.`,

        prompt: `Create comprehensive accommodation and dining recommendations for ${destinationInfo.destination}, ${destinationInfo.country}.

User Preferences:
- Budget: ${userPreferences.budget.level}
- Accommodation types: ${userPreferences.accommodation.type.join(', ')}
- Cuisine preferences: ${userPreferences.dining.cuisinePreferences.join(', ')}
- Dietary restrictions: ${userPreferences.dining.dietaryRestrictions.join(', ')}
- Travel with children: ${userPreferences.travelWithChildren}
- Accessibility needs: ${userPreferences.accessibility.mobilityIssues}

Search Data: ${JSON.stringify(searchData, null, 2)}

🎯 QUANTITÉ REQUISE - GÉNÉRER BEAUCOUP DE RECOMMANDATIONS:
- ACCOMMODATIONS RECOMMENDED: Minimum 8-12 hôtels/hébergements variés (luxe, milieu de gamme, budget, boutique, etc.)
- DINING DINNER: Minimum 10-15 restaurants pour le dîner (différents styles, budgets, cuisines)
- DINING BREAKFAST: Minimum 6-8 options petit-déjeuner
- DINING LUNCH: Minimum 8-10 options déjeuner
- DINING CAFES: Minimum 6-8 cafés
- STREET FOOD: Minimum 8-10 options street food
- LOCAL SPECIALTIES: Minimum 6-8 spécialités locales

EXIGENCES SPÉCIFIQUES - MAXIMUM DE DÉTAILS REQUIS:

🏨 ACCOMMODATIONS:
- Prix exacts par nuit avec variations saisonnières, taxes, frais cachés
- Adresses complètes, numéros de téléphone, sites web, emails
- Processus de réservation détaillé (meilleurs sites, codes promo, annulation)
- Check-in/check-out: horaires, procédures, documents requis, conseils
- Équipements détaillés (WiFi, petit-déjeuner, parking, spa, piscine)
- Politiques spécifiques (animaux, enfants, fumeurs, fêtes)
- Conseils de sécurité (coffres-forts, serrures, quartiers, transport)
- Étiquette locale (pourboires, comportement, dress code)
- Accessibilité complète (ascenseurs, rampes, chambres adaptées)

🍽️ DINING:
- Menus détaillés avec prix exacts, spécialités, ingrédients
- Horaires précis, jours de fermeture, réservations nécessaires
- Codes vestimentaires, étiquette de table, protocoles locaux
- Méthodes de paiement acceptées, pourboires attendus
- Allergies/régimes: options végétariennes, halal, casher, sans gluten
- Ambiance, niveau sonore, adapté aux familles/couples/affaires
- Instructions de commande, service, comportement approprié
- Localisation exacte, transport, stationnement

🎭 CULTURAL ETIQUETTE:
- Salutations appropriées par contexte (hôtel, restaurant, service)
- Comportements à éviter absolument, faux pas culturels
- Pourboires détaillés par service avec montants exacts
- Dress codes spécifiques par type d'établissement
- Protocoles de réservation, annulation, modification
- Interactions avec le personnel (politesse, limites, attentes)

Fournis le MAXIMUM de détails pratiques pour chaque recommandation.`,

        schema: z.object({
          accommodations: z.object({
            recommended: z.array(
              z.object({
                name: z.string(),
                type: z.string(),
                description: z.string(),
                priceRange: z.string(),
                detailedPricing: z.object({
                  basePrice: z.string(),
                  seasonalVariations: z.array(
                    z.object({
                      season: z.string(),
                      priceChange: z.string(),
                      dates: z.string(),
                    }),
                  ),
                  taxes: z.string(),
                  additionalFees: z.array(
                    z.object({
                      fee: z.string(),
                      amount: z.string(),
                      mandatory: z.boolean(),
                    }),
                  ),
                  discounts: z.array(z.string()),
                }),
                contactInfo: z.object({
                  address: z.string(),
                  phone: z.string(),
                  email: z.string(),
                  website: z.string(),
                  bookingPlatforms: z.array(
                    z.object({
                      platform: z.string(),
                      url: z.string(),
                      fees: z.string(),
                    }),
                  ),
                }),
                checkInOut: z.object({
                  checkInTime: z.string(),
                  checkOutTime: z.string(),
                  earlyCheckIn: z.string(),
                  lateCheckOut: z.string(),
                  process: z.array(z.string()),
                  documentsRequired: z.array(z.string()),
                }),
                amenities: z.array(
                  z.object({
                    name: z.string(),
                    description: z.string(),
                    cost: z.string(),
                    hours: z.string().optional(),
                  }),
                ),
                policies: z.object({
                  cancellation: z.string(),
                  pets: z.string(),
                  children: z.string(),
                  smoking: z.string(),
                  parties: z.string(),
                }),
                safetyFeatures: z.array(
                  z.object({
                    feature: z.string(),
                    description: z.string(),
                    location: z.string(),
                  }),
                ),
                culturalEtiquette: z.array(
                  z.object({
                    situation: z.string(),
                    expectedBehavior: z.string(),
                    tipping: z.string(),
                  }),
                ),
                accessibility: z.object({
                  wheelchairAccess: z.string(),
                  elevators: z.string(),
                  adaptedRooms: z.string(),
                  assistanceServices: z.array(z.string()),
                }),
                location: z.string(),
                coordinates: z.object({
                  lat: z.string(),
                  lng: z.string(),
                }),
                rating: z.string(),
                specialFeatures: z.array(z.string()),
                insiderTips: z.array(z.string()),
              }),
            ),
            alternatives: z.array(
              z.object({
                name: z.string(),
                type: z.string(),
                description: z.string(),
                priceRange: z.string(),
              }),
            ),
            neighborhoodGuide: z.array(
              z.object({
                name: z.string(),
                description: z.string(),
                bestFor: z.array(z.string()),
              }),
            ),
            alternativeOptions: z.array(
              z.object({
                type: z.string(),
                description: z.string(),
                priceRange: z.string(),
              }),
            ),
          }),
          dining: z.object({
            breakfast: z.array(
              z.object({
                name: z.string(),
                cuisine: z.string(),
                description: z.string(),
                priceRange: z.string(),
                location: z.string(),
                rating: z.string().optional(),
                coordinates: z.object({
                  lat: z.string(),
                  lng: z.string(),
                }),
              }),
            ),
            lunch: z.array(
              z.object({
                name: z.string(),
                cuisine: z.string(),
                description: z.string(),
                priceRange: z.string(),
                location: z.string(),
                rating: z.string().optional(),
                coordinates: z.object({
                  lat: z.string(),
                  lng: z.string(),
                }),
              }),
            ),
            dinner: z.array(
              z.object({
                name: z.string(),
                cuisine: z.string(),
                description: z.string(),
                priceRange: z.string(),
                location: z.string(),
                rating: z.string().optional(),
                coordinates: z.object({
                  lat: z.string(),
                  lng: z.string(),
                }),
              }),
            ),
            cafes: z.array(
              z.object({
                name: z.string(),
                description: z.string(),
                location: z.string(),
                priceRange: z.string(),
              }),
            ),
            specialOccasion: z.array(
              z.object({
                name: z.string(),
                description: z.string(),
                priceRange: z.string(),
              }),
            ),
            localSpecialties: z.array(
              z.object({
                dish: z.string(),
                description: z.string(),
                whereToTry: z.array(z.string()),
              }),
            ),
            streetFood: z.array(
              z.object({
                name: z.string(),
                description: z.string(),
                location: z.string(),
                price: z.string(),
              }),
            ),
            markets: z.array(
              z.object({
                name: z.string(),
                description: z.string(),
                location: z.string(),
                specialties: z.array(z.string()),
              }),
            ),
          }),
          culinaryExperiences: z.object({
            cookingClasses: z.array(
              z.object({
                name: z.string(),
                description: z.string(),
                duration: z.string(),
                price: z.string(),
              }),
            ),
            foodTours: z.array(
              z.object({
                name: z.string(),
                description: z.string(),
                duration: z.string(),
                price: z.string(),
              }),
            ),
            tastings: z.array(
              z.object({
                name: z.string(),
                description: z.string(),
                price: z.string(),
              }),
            ),
            diningEvents: z.array(
              z.object({
                name: z.string(),
                description: z.string(),
                price: z.string(),
              }),
            ),
          }),
        }),
        temperature: 0.7,
      });

      return recommendations as AccommodationDiningRecommendations;
    } catch (error) {
      console.error('❌ Error generating recommendations:', error);
      throw error;
    }
  }

  /**
   * Create beautiful HTML presentation without carousels
   */
  createHtmlPresentation(
    recommendations: AccommodationDiningRecommendations,
  ): string {
    return `
      <div class="accommodation-dining-recommendations" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px;">

        <!-- Accommodations Section -->
        <section class="accommodations-section" style="margin-bottom: 40px;">
          <h2 style="color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; margin-bottom: 30px;">
            🏨 Accommodations
          </h2>

          <!-- Recommended Hotels -->
          <div class="recommended-hotels" style="margin-bottom: 30px;">
            <h3 style="color: #34495e; margin-bottom: 20px;">✨ Recommended Hotels</h3>
            <div class="hotels-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px;">
              ${recommendations.accommodations.recommended
                .map(
                  (hotel) => `
                <div class="hotel-card" style="border: 1px solid #ddd; border-radius: 12px; padding: 20px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); box-shadow: 0 4px 6px rgba(0,0,0,0.1); transition: transform 0.3s ease;">
                  <h4 style="color: #2c3e50; margin-bottom: 10px; font-size: 1.2em;">${hotel.name}</h4>
                  <div class="hotel-type" style="background: #3498db; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.8em; display: inline-block; margin-bottom: 10px;">${hotel.type}</div>
                  <p style="color: #555; line-height: 1.6; margin-bottom: 15px;">${hotel.description}</p>
                  <div class="hotel-details" style="font-size: 0.9em;">
                    <div style="margin-bottom: 8px;"><strong>📍 Location:</strong> ${hotel.location}</div>
                    <div style="margin-bottom: 8px;"><strong>💰 Price Range:</strong> ${hotel.priceRange}</div>
                    <div style="margin-bottom: 8px;"><strong>⭐ Rating:</strong> ${hotel.rating}</div>
                    <div style="margin-bottom: 8px;"><strong>🎯 Amenities:</strong> ${hotel.amenities.join(', ')}</div>
                    <div><strong>✨ Special Features:</strong> ${hotel.specialFeatures.join(', ')}</div>
                  </div>
                </div>
              `,
                )
                .join('')}
            </div>
          </div>

          <!-- Neighborhood Guide -->
          <div class="neighborhood-guide" style="margin-bottom: 30px;">
            <h3 style="color: #34495e; margin-bottom: 20px;">🗺️ Neighborhood Guide</h3>
            <div class="neighborhoods-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
              ${recommendations.accommodations.neighborhoodGuide
                .map(
                  (neighborhood) => `
                <div class="neighborhood-card" style="border-left: 4px solid #e74c3c; padding: 15px; background: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                  <h4 style="color: #e74c3c; margin-bottom: 8px;">${neighborhood.name}</h4>
                  <p style="color: #555; margin-bottom: 10px; font-size: 0.9em;">${neighborhood.description}</p>
                  <div style="font-size: 0.8em; color: #666;">
                    <strong>Best for:</strong> ${neighborhood.bestFor.join(', ')}
                  </div>
                </div>
              `,
                )
                .join('')}
            </div>
          </div>
        </section>

        <!-- Dining Section -->
        <section class="dining-section" style="margin-bottom: 40px;">
          <h2 style="color: #2c3e50; border-bottom: 3px solid #e67e22; padding-bottom: 10px; margin-bottom: 30px;">
            🍽️ Dining Options
          </h2>

          <!-- Dinner Restaurants -->
          <div class="dinner-restaurants" style="margin-bottom: 30px;">
            <h3 style="color: #34495e; margin-bottom: 20px;">🌟 Dinner Restaurants</h3>
            <div class="restaurants-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px;">
              ${recommendations.dining.dinner
                .map(
                  (restaurant) => `
                <div class="restaurant-card" style="border: 1px solid #ddd; border-radius: 12px; padding: 20px; background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%); box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                  <h4 style="color: #2c3e50; margin-bottom: 10px; font-size: 1.2em;">${restaurant.name}</h4>
                  <div class="cuisine-type" style="background: #e67e22; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.8em; display: inline-block; margin-bottom: 10px;">${restaurant.cuisine}</div>
                  <p style="color: #555; line-height: 1.6; margin-bottom: 15px;">${restaurant.description}</p>
                  <div class="restaurant-details" style="font-size: 0.9em;">
                    <div style="margin-bottom: 8px;"><strong>📍 Location:</strong> ${restaurant.location}</div>
                    <div style="margin-bottom: 8px;"><strong>💰 Price Range:</strong> ${restaurant.priceRange}</div>
                  </div>
                </div>
              `,
                )
                .join('')}
            </div>
          </div>

          <!-- Local Specialties -->
          <div class="local-specialties" style="margin-bottom: 30px;">
            <h3 style="color: #34495e; margin-bottom: 20px;">🥘 Local Specialties</h3>
            <div class="specialties-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 15px;">
              ${recommendations.dining.localSpecialties
                .map(
                  (specialty) => `
                <div class="specialty-card" style="border-left: 4px solid #f39c12; padding: 15px; background: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                  <h4 style="color: #f39c12; margin-bottom: 8px;">${specialty.dish}</h4>
                  <p style="color: #555; margin-bottom: 10px; font-size: 0.9em;">${specialty.description}</p>
                  <div style="font-size: 0.8em; color: #666;">
                    <strong>Where to try:</strong> ${specialty.whereToTry.join(', ')}
                  </div>
                </div>
              `,
                )
                .join('')}
            </div>
          </div>

          <!-- Street Food -->
          <div class="street-food" style="margin-bottom: 30px;">
            <h3 style="color: #34495e; margin-bottom: 20px;">🌮 Street Food</h3>
            <div class="street-food-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
              ${recommendations.dining.streetFood
                .map(
                  (food) => `
                <div class="street-food-card" style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; background: #f8f9fa;">
                  <h4 style="color: #2c3e50; margin-bottom: 8px;">${food.name}</h4>
                  <p style="color: #555; margin-bottom: 8px; font-size: 0.9em;">${food.description}</p>
                  <div style="font-size: 0.8em;">
                    <div><strong>📍 Location:</strong> ${food.location}</div>
                    <div><strong>💰 Price:</strong> ${food.price}</div>
                  </div>
                </div>
              `,
                )
                .join('')}
            </div>
          </div>
        </section>

        <!-- Culinary Experiences Section -->
        <section class="culinary-experiences-section">
          <h2 style="color: #2c3e50; border-bottom: 3px solid #9b59b6; padding-bottom: 10px; margin-bottom: 30px;">
            👨‍🍳 Culinary Experiences
          </h2>

          <div class="experiences-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
            ${recommendations.culinaryExperiences.cookingClasses
              .map(
                (experience) => `
              <div class="experience-card" style="border: 1px solid #ddd; border-radius: 12px; padding: 20px; background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%); box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h4 style="color: #2c3e50; margin-bottom: 10px; font-size: 1.1em;">${experience.name}</h4>
                <p style="color: #555; line-height: 1.6; margin-bottom: 15px;">${experience.description}</p>
                <div class="experience-details" style="font-size: 0.9em;">
                  <div style="margin-bottom: 8px;"><strong>⏱️ Duration:</strong> ${experience.duration}</div>
                  <div><strong>💰 Price:</strong> ${experience.price}</div>
                </div>
              </div>
            `,
              )
              .join('')}

            ${recommendations.culinaryExperiences.foodTours
              .map(
                (tour) => `
              <div class="experience-card" style="border: 1px solid #ddd; border-radius: 12px; padding: 20px; background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%); box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h4 style="color: #2c3e50; margin-bottom: 10px; font-size: 1.1em;">${tour.name}</h4>
                <p style="color: #555; line-height: 1.6; margin-bottom: 15px;">${tour.description}</p>
                <div class="experience-details" style="font-size: 0.9em;">
                  <div style="margin-bottom: 8px;"><strong>⏱️ Duration:</strong> ${tour.duration}</div>
                  <div><strong>💰 Price:</strong> ${tour.price}</div>
                </div>
              </div>
            `,
              )
              .join('')}
          </div>
        </section>
      </div>
    `;
  }

  /**
   * Get enhanced fallback recommendations if generation fails
   */
  private getFallbackRecommendations(
    destinationInfo: DestinationInfo,
  ): AccommodationDiningRecommendations {
    console.log(
      '🔄 Using enhanced fallback recommendations for:',
      destinationInfo.destination,
    );

    return {
      accommodations: {
        recommended: [
          {
            name: `Grand Hotel ${destinationInfo.destination}`,
            type: 'Luxury Hotel',
            description: `A premium hotel in the heart of ${destinationInfo.destination} offering exceptional service and modern amenities.`,
            priceRange: '$200-400 per night',
            location: 'City Center',
            amenities: [
              {
                name: 'Wi-Fi',
                description: 'Free high-speed internet',
                cost: 'Free',
              },
              { name: 'Spa', description: 'Full-service spa', cost: '$50-150' },
              {
                name: 'Fitness Center',
                description: '24/7 gym access',
                cost: 'Free',
              },
              {
                name: 'Restaurant',
                description: 'On-site dining',
                cost: 'Menu prices',
              },
              {
                name: 'Concierge',
                description: 'Personal assistance',
                cost: 'Free',
              },
              {
                name: 'Room Service',
                description: '24/7 room service',
                cost: 'Menu + 15%',
              },
            ],
            rating: '4.5 stars',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
            specialFeatures: [
              'Rooftop terrace',
              'Historic building',
              'Award-winning restaurant',
            ],
            detailedPricing: {
              basePrice: '$200-400 per night',
              seasonalVariations: [
                {
                  season: 'High Season',
                  priceChange: '+30%',
                  dates: 'Jun-Aug',
                },
                { season: 'Low Season', priceChange: '-20%', dates: 'Nov-Mar' },
              ],
              taxes: '12% city tax',
              additionalFees: [
                { fee: 'Resort fee', amount: '$25/night', mandatory: true },
                { fee: 'Parking', amount: '$15/night', mandatory: false },
              ],
              discounts: ['Early booking 10%', 'Extended stay 15%'],
            },
            contactInfo: {
              address: 'Main Street, City Center',
              phone: '******-0123',
              email: '<EMAIL>',
              website: 'www.grandhotel.com',
              bookingPlatforms: [
                {
                  platform: 'Booking.com',
                  url: 'booking.com/hotel',
                  fees: '15%',
                },
                { platform: 'Direct', url: 'www.grandhotel.com', fees: '0%' },
              ],
            },
            checkInOut: {
              checkInTime: '3:00 PM',
              checkOutTime: '11:00 AM',
              earlyCheckIn: 'Available from 1:00 PM for $50',
              lateCheckOut: 'Available until 2:00 PM for $75',
              process: [
                'Present ID and credit card',
                'Sign registration',
                'Receive key cards',
              ],
              documentsRequired: [
                'Government ID',
                'Credit card',
                'Booking confirmation',
              ],
            },
            policies: {
              cancellation: 'Free cancellation 24h before arrival',
              pets: 'Pets allowed with $50 fee',
              children: 'Children under 12 stay free',
              smoking: 'Non-smoking property',
              parties: 'No parties or events allowed',
            },
            safetyFeatures: [
              {
                feature: 'Safe',
                description: 'In-room electronic safe',
                location: 'Each room',
              },
              {
                feature: 'Security',
                description: '24/7 security staff',
                location: 'Lobby',
              },
              {
                feature: 'CCTV',
                description: 'Security cameras',
                location: 'Common areas',
              },
            ],
            culturalEtiquette: [
              {
                situation: 'Check-in',
                expectedBehavior: 'Dress smart casual',
                tipping: 'Bellhop $2-5',
              },
              {
                situation: 'Restaurant',
                expectedBehavior: 'Business casual required',
                tipping: '18-20%',
              },
            ],
            accessibility: {
              wheelchairAccess: 'Full wheelchair accessibility',
              elevators: '3 elevators with braille buttons',
              adaptedRooms: '5 ADA-compliant rooms available',
              assistanceServices: [
                'Hearing assistance',
                'Visual assistance',
                'Mobility support',
              ],
            },
            insiderTips: [
              'Request a room on floors 5-7 for best city views',
              'Book spa treatments in advance',
              'Happy hour at rooftop bar 5-7 PM',
            ],
          },
          {
            name: `Boutique Inn ${destinationInfo.destination}`,
            type: 'Boutique Hotel',
            description: `Charming boutique hotel with unique character and personalized service in ${destinationInfo.destination}.`,
            priceRange: '$120-250 per night',
            location: 'Historic District',
            amenities: [
              {
                name: 'Wi-Fi',
                description: 'Free internet access',
                cost: 'Free',
              },
              {
                name: 'Breakfast',
                description: 'Continental breakfast',
                cost: '$15',
              },
              {
                name: 'Bar',
                description: 'Cozy hotel bar',
                cost: 'Menu prices',
              },
              {
                name: 'Garden',
                description: 'Private garden area',
                cost: 'Free',
              },
              { name: 'Library', description: 'Reading room', cost: 'Free' },
            ],
            rating: '4.3 stars',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
            specialFeatures: [
              'Unique decor',
              'Local art',
              'Intimate atmosphere',
            ],
            detailedPricing: {
              basePrice: '$120-250 per night',
              seasonalVariations: [
                {
                  season: 'Peak Season',
                  priceChange: '+25%',
                  dates: 'Jul-Sep',
                },
                { season: 'Off Season', priceChange: '-15%', dates: 'Jan-Mar' },
              ],
              taxes: '10% local tax',
              additionalFees: [
                { fee: 'City tax', amount: '$3/night', mandatory: true },
                { fee: 'Breakfast', amount: '$15/person', mandatory: false },
              ],
              discounts: ['Direct booking 5%', 'Weekly stay 10%'],
            },
            contactInfo: {
              address: 'Historic District, Old Town',
              phone: '******-0456',
              email: '<EMAIL>',
              website: 'www.boutiqueinn.com',
              bookingPlatforms: [
                { platform: 'Expedia', url: 'expedia.com/hotel', fees: '12%' },
                { platform: 'Direct', url: 'www.boutiqueinn.com', fees: '0%' },
              ],
            },
            checkInOut: {
              checkInTime: '2:00 PM',
              checkOutTime: '12:00 PM',
              earlyCheckIn: 'Subject to availability, no charge',
              lateCheckOut: 'Available until 3:00 PM for $30',
              process: [
                'Check-in at reception',
                'Room orientation',
                'Local recommendations',
              ],
              documentsRequired: ['Photo ID', 'Credit card for incidentals'],
            },
            policies: {
              cancellation: 'Free cancellation 48h before arrival',
              pets: 'Small pets welcome with $25 fee',
              children: 'Children welcome, extra bed $20',
              smoking: 'Designated smoking areas only',
              parties: 'Quiet hours after 10 PM',
            },
            safetyFeatures: [
              {
                feature: 'Room Safe',
                description: 'Digital safe in each room',
                location: 'Rooms',
              },
              {
                feature: 'Fire Safety',
                description: 'Smoke detectors and sprinklers',
                location: 'All areas',
              },
            ],
            culturalEtiquette: [
              {
                situation: 'Arrival',
                expectedBehavior: 'Casual dress acceptable',
                tipping: 'Optional $1-2',
              },
              {
                situation: 'Common areas',
                expectedBehavior: 'Respect quiet atmosphere',
                tipping: 'Not expected',
              },
            ],
            accessibility: {
              wheelchairAccess: 'Ground floor accessible',
              elevators: 'Historic building, stairs only to upper floors',
              adaptedRooms: '2 ground floor accessible rooms',
              assistanceServices: ['Staff assistance available'],
            },
            insiderTips: [
              'Ask for garden view rooms',
              'Complimentary local art tour on Saturdays',
              'Best breakfast served 7-9 AM',
            ],
          },
          {
            name: `Business Hotel ${destinationInfo.destination}`,
            type: 'Business Hotel',
            description: `Modern business hotel with excellent facilities for both business and leisure travelers in ${destinationInfo.destination}.`,
            priceRange: '$150-300 per night',
            location: 'Business District',
            amenities: [
              {
                name: 'Wi-Fi',
                description: 'High-speed internet',
                cost: 'Free',
              },
              {
                name: 'Business Center',
                description: '24/7 business facilities',
                cost: 'Free',
              },
              {
                name: 'Gym',
                description: 'Modern fitness center',
                cost: 'Free',
              },
              {
                name: 'Pool',
                description: 'Indoor swimming pool',
                cost: 'Free',
              },
              {
                name: 'Restaurant',
                description: 'International cuisine',
                cost: 'Menu prices',
              },
            ],
            rating: '4.4 stars',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
            specialFeatures: [
              'Conference rooms',
              'Executive lounge',
              'Airport shuttle',
            ],
            detailedPricing: {
              basePrice: '$150-300 per night',
              seasonalVariations: [
                {
                  season: 'Business Season',
                  priceChange: '+20%',
                  dates: 'Sep-Nov',
                },
                {
                  season: 'Holiday Season',
                  priceChange: '-10%',
                  dates: 'Dec-Jan',
                },
              ],
              taxes: '15% business tax',
              additionalFees: [
                { fee: 'Business center', amount: 'Free', mandatory: false },
                { fee: 'Parking', amount: '$20/night', mandatory: false },
              ],
              discounts: ['Corporate rate 20%', 'Extended stay 12%'],
            },
            contactInfo: {
              address: 'Business District, Financial Center',
              phone: '******-0789',
              email: '<EMAIL>',
              website: 'www.businesshotel.com',
              bookingPlatforms: [
                {
                  platform: 'Hotels.com',
                  url: 'hotels.com/business',
                  fees: '10%',
                },
                {
                  platform: 'Direct',
                  url: 'www.businesshotel.com',
                  fees: '0%',
                },
              ],
            },
            checkInOut: {
              checkInTime: '3:00 PM',
              checkOutTime: '12:00 PM',
              earlyCheckIn: 'Available from 12:00 PM',
              lateCheckOut: 'Available until 4:00 PM for $50',
              process: [
                'Express check-in available',
                'Mobile key option',
                'Concierge service',
              ],
              documentsRequired: [
                'Government ID',
                'Credit card',
                'Business card if applicable',
              ],
            },
            policies: {
              cancellation: 'Free cancellation 24h before arrival',
              pets: 'Service animals only',
              children: 'Children under 16 stay free',
              smoking: 'Non-smoking property',
              parties: 'Business events welcome',
            },
            safetyFeatures: [
              {
                feature: 'Safe',
                description: 'Laptop-sized safe',
                location: 'Each room',
              },
              {
                feature: 'Security',
                description: '24/7 security',
                location: 'All floors',
              },
              {
                feature: 'Access Control',
                description: 'Key card access',
                location: 'Elevators',
              },
            ],
            culturalEtiquette: [
              {
                situation: 'Business meetings',
                expectedBehavior: 'Business attire required',
                tipping: 'Standard rates',
              },
              {
                situation: 'Executive lounge',
                expectedBehavior: 'Smart casual',
                tipping: 'Not required',
              },
            ],
            accessibility: {
              wheelchairAccess: 'Full accessibility',
              elevators: '4 elevators with voice announcements',
              adaptedRooms: '8 ADA-compliant rooms',
              assistanceServices: [
                'Business assistance',
                'Mobility support',
                'Communication aids',
              ],
            },
            insiderTips: [
              'Executive lounge access includes breakfast and evening cocktails',
              'Free airport shuttle every 30 minutes',
              'Best meeting rooms on 15th floor with city views',
            ],
          },
          {
            name: `Eco Lodge ${destinationInfo.destination}`,
            type: 'Eco-Friendly Hotel',
            description: `Sustainable eco-lodge committed to environmental responsibility while providing comfortable accommodation in ${destinationInfo.destination}.`,
            priceRange: '$100-200 per night',
            location: 'Green District',
            amenities: [
              {
                name: 'Wi-Fi',
                description: 'Solar-powered internet',
                cost: 'Free',
              },
              {
                name: 'Organic Garden',
                description: 'On-site organic garden',
                cost: 'Free tours',
              },
              {
                name: 'Bike Rental',
                description: 'Electric bikes available',
                cost: '$15/day',
              },
              {
                name: 'Wellness Center',
                description: 'Yoga and meditation',
                cost: '$20/session',
              },
              {
                name: 'Restaurant',
                description: 'Farm-to-table dining',
                cost: 'Menu prices',
              },
            ],
            rating: '4.6 stars',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
            specialFeatures: [
              'Solar panels',
              'Rainwater harvesting',
              'Local materials',
            ],
            detailedPricing: {
              basePrice: '$100-200 per night',
              seasonalVariations: [
                { season: 'Eco Season', priceChange: '+15%', dates: 'Apr-Jun' },
                { season: 'Winter', priceChange: '-25%', dates: 'Dec-Feb' },
              ],
              taxes: '8% eco tax',
              additionalFees: [
                { fee: 'Carbon offset', amount: '$5/night', mandatory: true },
                {
                  fee: 'Organic breakfast',
                  amount: '$18/person',
                  mandatory: false,
                },
              ],
              discounts: ['Eco-conscious traveler 10%', 'Long stay 20%'],
            },
            contactInfo: {
              address: 'Green District, Sustainable Quarter',
              phone: '******-0321',
              email: '<EMAIL>',
              website: 'www.ecolodge.com',
              bookingPlatforms: [
                {
                  platform: 'EcoBooking',
                  url: 'ecobooking.com/lodge',
                  fees: '8%',
                },
                { platform: 'Direct', url: 'www.ecolodge.com', fees: '0%' },
              ],
            },
            checkInOut: {
              checkInTime: '2:00 PM',
              checkOutTime: '11:00 AM',
              earlyCheckIn: 'Available with eco-tour',
              lateCheckOut: 'Available until 1:00 PM',
              process: [
                'Eco-orientation session',
                'Sustainability guidelines',
                'Local recommendations',
              ],
              documentsRequired: ['Photo ID', 'Eco-pledge signature'],
            },
            policies: {
              cancellation: 'Free cancellation 72h before arrival',
              pets: 'Pets welcome with eco-friendly supplies',
              children: 'Children programs available',
              smoking: 'Outdoor designated areas only',
              parties: 'Eco-friendly events only',
            },
            safetyFeatures: [
              {
                feature: 'Natural Safe',
                description: 'Bamboo safe',
                location: 'Each room',
              },
              {
                feature: 'Emergency Kit',
                description: 'Eco-emergency supplies',
                location: 'All floors',
              },
              {
                feature: 'Natural Lighting',
                description: 'Solar emergency lighting',
                location: 'Corridors',
              },
            ],
            culturalEtiquette: [
              {
                situation: 'Dining',
                expectedBehavior: 'Respect organic principles',
                tipping: '15% to local staff',
              },
              {
                situation: 'Activities',
                expectedBehavior: 'Follow eco-guidelines',
                tipping: 'Optional for guides',
              },
            ],
            accessibility: {
              wheelchairAccess: 'Natural ramp access',
              elevators: 'Solar-powered elevator',
              adaptedRooms: '3 eco-accessible rooms',
              assistanceServices: [
                'Nature guide assistance',
                'Eco-mobility support',
              ],
            },
            insiderTips: [
              'Join morning bird watching tours',
              'Organic garden tours at sunset',
              'Best eco-breakfast served 7-10 AM',
            ],
          },
        ],
        alternatives: [
          {
            name: `Budget Inn ${destinationInfo.destination}`,
            type: 'Budget Hotel',
            description:
              'Clean and affordable accommodation with essential amenities.',
            priceRange: '$60-120 per night',
          },
        ],
        neighborhoodGuide: [
          {
            name: 'City Center',
            description:
              'The heart of the city with main attractions and shopping.',
            bestFor: ['First-time visitors', 'Sightseeing', 'Shopping'],
          },
          {
            name: 'Historic District',
            description:
              'Charming area with traditional architecture and local culture.',
            bestFor: ['Culture enthusiasts', 'History lovers', 'Photography'],
          },
        ],
        alternativeOptions: [
          {
            type: 'Vacation Rentals',
            description: 'Apartments and homes for a local living experience.',
            priceRange: '$80-300 per night',
          },
          {
            type: 'Hostels',
            description:
              'Budget-friendly accommodation with social atmosphere.',
            priceRange: '$25-60 per night',
          },
        ],
      },
      dining: {
        breakfast: [
          {
            name: `Morning Cafe ${destinationInfo.destination}`,
            cuisine: 'Local',
            description:
              'Cozy cafe serving traditional breakfast and excellent coffee.',
            priceRange: '$8-15',
            location: 'City Center',
            rating: '4.2/5',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
        ],
        lunch: [
          {
            name: `Bistro Central ${destinationInfo.destination}`,
            cuisine: 'International',
            description: 'Casual bistro with fresh, seasonal lunch options.',
            priceRange: '$12-25',
            location: 'Shopping District',
            rating: '4.0/5',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
        ],
        dinner: [
          {
            name: `Traditional Restaurant ${destinationInfo.destination}`,
            cuisine: 'Local',
            description:
              'Authentic local cuisine in a charming traditional setting.',
            priceRange: '$25-45',
            location: 'Historic District',
            rating: '4.5/5',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
          {
            name: `Seafood Palace ${destinationInfo.destination}`,
            cuisine: 'Seafood',
            description:
              'Fresh seafood restaurant with ocean-to-table dining experience.',
            priceRange: '$35-65',
            location: 'Waterfront District',
            rating: '4.7/5',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
          {
            name: `Fusion Bistro ${destinationInfo.destination}`,
            cuisine: 'International Fusion',
            description:
              'Creative fusion cuisine blending local and international flavors.',
            priceRange: '$30-55',
            location: 'Arts Quarter',
            rating: '4.4/5',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
          {
            name: `Garden Restaurant ${destinationInfo.destination}`,
            cuisine: 'Mediterranean',
            description:
              'Beautiful garden setting with fresh Mediterranean dishes.',
            priceRange: '$28-48',
            location: 'Garden District',
            rating: '4.6/5',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
          {
            name: `Steakhouse Prime ${destinationInfo.destination}`,
            cuisine: 'Steakhouse',
            description:
              'Premium steakhouse with finest cuts and extensive wine selection.',
            priceRange: '$45-85',
            location: 'Uptown',
            rating: '4.8/5',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
          {
            name: `Vegetarian Haven ${destinationInfo.destination}`,
            cuisine: 'Vegetarian',
            description:
              'Innovative plant-based cuisine with organic local ingredients.',
            priceRange: '$22-38',
            location: 'Green Quarter',
            rating: '4.5/5',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
          {
            name: `Pizza Corner ${destinationInfo.destination}`,
            cuisine: 'Italian',
            description:
              'Authentic wood-fired pizzas and traditional Italian dishes.',
            priceRange: '$18-32',
            location: 'Little Italy',
            rating: '4.3/5',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
          {
            name: `Spice Route ${destinationInfo.destination}`,
            cuisine: 'Asian',
            description:
              'Authentic Asian flavors from various regions and cooking styles.',
            priceRange: '$20-40',
            location: 'Chinatown',
            rating: '4.4/5',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
          {
            name: `Rooftop Grill ${destinationInfo.destination}`,
            cuisine: 'Grill & Bar',
            description:
              'Rooftop dining with panoramic city views and grilled specialties.',
            priceRange: '$32-58',
            location: 'Downtown',
            rating: '4.6/5',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
          {
            name: `Family Tavern ${destinationInfo.destination}`,
            cuisine: 'Comfort Food',
            description:
              'Family-friendly restaurant with hearty comfort food and warm atmosphere.',
            priceRange: '$15-28',
            location: 'Residential Area',
            rating: '4.2/5',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
        ],
        cafes: [
          {
            name: `Artisan Coffee ${destinationInfo.destination}`,
            description:
              'Modern coffee shop with artisanal pastries and light meals.',
            location: 'City Center',
            priceRange: '$5-12',
          },
        ],
        specialOccasion: [
          {
            name: `Fine Dining ${destinationInfo.destination}`,
            description: 'Upscale restaurant perfect for special celebrations.',
            priceRange: '$60-120',
          },
        ],
        localSpecialties: [
          {
            dish: `Traditional ${destinationInfo.destination} Specialty`,
            description:
              'A signature dish of the region made with local ingredients.',
            whereToTry: [
              'Traditional Restaurant',
              'Local Market',
              'Food Tours',
            ],
          },
        ],
        streetFood: [
          {
            name: `${destinationInfo.destination} Street Food`,
            description: 'Popular local street food with authentic flavors.',
            location: 'Market District',
            price: '$3-8',
          },
        ],
        markets: [
          {
            name: `Central Market ${destinationInfo.destination}`,
            description:
              'Historic market with fresh produce and local specialties.',
            location: 'Old Town',
            specialties: [
              'Fresh produce',
              'Local specialties',
              'Artisanal goods',
            ],
          },
        ],
      },
      culinaryExperiences: {
        cookingClasses: [
          {
            name: `${destinationInfo.destination} Cooking Class`,
            description:
              'Learn to prepare traditional local dishes with expert chefs.',
            duration: '3 hours',
            price: '$75-120',
          },
        ],
        foodTours: [
          {
            name: `${destinationInfo.destination} Food Tour`,
            description:
              'Guided tour exploring the best local culinary traditions.',
            duration: '3-4 hours',
            price: '$45-80',
          },
        ],
        tastings: [
          {
            name: `Local Wine & Food Tasting`,
            description: 'Sample regional wines and local delicacies.',
            price: '$35-60',
          },
        ],
        diningEvents: [
          {
            name: `Seasonal Dinner Experience`,
            description:
              'Special multi-course dinner featuring seasonal ingredients.',
            price: '$85-150',
          },
        ],
      },
    };
  }
}
