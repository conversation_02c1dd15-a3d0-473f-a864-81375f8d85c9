'use server';

import type { SearchGroupId } from '@/lib/utils';
import { generateObject, type UIMessage, generateText } from 'ai';
import { z } from 'zod';
import { myProvider } from '@/lib/ai/providers';
import { groq } from '@ai-sdk/groq';

// Importer les fonctions de recherche extrême depuis le fichier dédié
export {
  canUseExtremeSearch,
  incrementExtremeSearchUsage,
  executeExtremeSearch,
  getExtremeSearchStats,
  resetExtremeSearchUsage,
} from './extreme-search-actions';

export async function suggestQuestions(history: any[]) {
  'use server';

  console.log(history);

  const { object } = await generateObject({
    model: myProvider.languageModel('chat-model'),
    temperature: 0,
    maxTokens: 512,
    system: `You are a search engine follow up query/questions generator. You MUST create EXACTLY 3 questions for the search engine based on the message history.

### Question Generation Guidelines:
- Create exactly 3 questions that are open-ended and encourage further discussion
- Questions must be concise (5-10 words each) but specific and contextually relevant
- Each question must contain specific nouns, entities, or clear context markers
- NEVER use pronouns (he, she, him, his, her, etc.) - always use proper nouns from the context
- Questions must be related to tools available in the system
- Questions should flow naturally from previous conversation
- You are here to generate questions for the search engine not to use tools or run tools!!`,
    messages: history,
    schema: z.object({
      questions: z
        .array(z.string())
        .describe('The generated questions based on the message history.'),
    }),
  });

  return {
    questions: object.questions,
  };
}

export async function checkImageModeration(images: any) {
  const { text } = await generateText({
    model: groq('meta-llama/llama-guard-4-12b'),
    messages: [
      {
        role: 'user',
        content: images.map((image: any) => ({
          type: 'image',
          image: image,
        })),
      },
    ],
  });
  return text;
}

export async function generateTitleFromUserMessage({
  message,
}: { message: UIMessage }) {
  const { text: title } = await generateText({
    model: myProvider.languageModel('title-model'),
    system: `\n
    - you will generate a short title based on the first message a user begins a conversation with
    - ensure it is not more than 80 characters long
    - the title should be a summary of the user's message
    - the title should creative and unique
    - do not use quotes or colons`,
    prompt: JSON.stringify(message),
  });

  return title;
}

const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY;

export async function generateSpeech(text: string) {
  const VOICE_ID = 'JBFqnCBsd6RMkjVDRZzb'; // This is the ID for the "George" voice. Replace with your preferred voice ID.
  const url = `https://api.elevenlabs.io/v1/text-to-speech/${VOICE_ID}`;
  const method = 'POST';

  if (!ELEVENLABS_API_KEY) {
    throw new Error('ELEVENLABS_API_KEY is not defined');
  }

  const headers = {
    Accept: 'audio/mpeg',
    'xi-api-key': ELEVENLABS_API_KEY,
    'Content-Type': 'application/json',
  };

  const data = {
    text,
    model_id: 'eleven_turbo_v2_5',
    voice_settings: {
      stability: 0.5,
      similarity_boost: 0.5,
    },
  };

  const body = JSON.stringify(data);

  const input = {
    method,
    headers,
    body,
  };

  const response = await fetch(url, input);

  const arrayBuffer = await response.arrayBuffer();

  const base64Audio = Buffer.from(arrayBuffer).toString('base64');

  return {
    audio: `data:audio/mp3;base64,${base64Audio}`,
  };
}

export async function fetchMetadata(url: string) {
  try {
    const response = await fetch(url, { next: { revalidate: 3600 } }); // Cache for 1 hour
    const html = await response.text();

    const titleMatch = html.match(/<title>(.*?)<\/title>/i);
    const descMatch = html.match(
      /<meta\s+name=["']description["']\s+content=["'](.*?)["']/i,
    );

    const title = titleMatch ? titleMatch[1] : '';
    const description = descMatch ? descMatch[1] : '';

    return { title, description };
  } catch (error) {
    console.error('Error fetching metadata:', error);
    return null;
  }
}

// Map deprecated 'buddy' group ID to 'memory' for backward compatibility
type LegacyGroupId = SearchGroupId | 'buddy';

const groupTools = {
  web: [
    'web_search',
    'greeting',
    'get_weather_data',
    'retrieve',
    'text_translate',
    'nearby_places_search',
    'track_flight',
    'movie_or_tv_search',
    'trending_movies',
    'find_place_on_map',
    'trending_tv',
    'datetime',
    'mcp_search',
  ] as const,
  academic: ['academic_search', 'code_interpreter', 'datetime'] as const,
  youtube: ['youtube_search', 'datetime'] as const,
  reddit: ['reddit_search', 'datetime'] as const,
  analysis: [
    'code_interpreter',
    'stock_chart',
    'currency_converter',
    'datetime',
  ] as const,
  crypto: [
    'coin_data',
    'coin_ohlc',
    'coin_data_by_contract',
    'datetime',
  ] as const,
  chat: [] as const,
  extreme: ['extreme_search'] as const,
  x: ['x_search'] as const,
  memory: ['memory_manager', 'datetime'] as const,
  // Add legacy mapping for backward compatibility
  buddy: ['memory_manager', 'datetime'] as const,
} as const;
