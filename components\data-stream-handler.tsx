'use client';

import { useChat } from '@ai-sdk/react';
import { useEffect, useRef } from 'react';
import { artifactDefinitions, type ArtifactKind } from './artifact';
import type { Suggestion } from '@/lib/db/schema';
import { initialArtifactData, useArtifact } from '@/hooks/use-artifact';

export type DataStreamDelta = {
  type:
    | 'text-delta'
    | 'code-delta'
    | 'sheet-delta'
    | 'image-delta'
    | 'html-delta'
    | 'title'
    | 'id'
    | 'suggestion'
    | 'clear'
    | 'finish'
    | 'completion'
    | 'kind';
  content: string | Suggestion;
};

export function DataStreamHandler({ id }: { id: string }) {
  const { data: dataStream } = useChat({ id });
  const { artifact, setArtifact, setMetadata } = useArtifact();
  const lastProcessedIndex = useRef(-1);

  // Refs to track state without causing re-renders
  const hasFinishedRef = useRef(false);
  const artifactRef = useRef(artifact);
  const lastDocumentIdRef = useRef<string | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  // Keep refs in sync with current state
  useEffect(() => {
    artifactRef.current = artifact;
  }, [artifact.status, artifact.documentId, artifact.kind]); // Update when these specific properties change

  useEffect(() => {
    // Skip processing if we've already finished and the document ID hasn't changed
    if (
      hasFinishedRef.current &&
      artifactRef.current.status === 'idle' &&
      lastDocumentIdRef.current === artifactRef.current.documentId &&
      artifactRef.current.documentId !== 'init'
    ) {
      console.log(
        'DataStreamHandler - Skipping processing, artifact already finished:',
        {
          documentId: artifactRef.current.documentId,
          status: artifactRef.current.status,
        },
      );
      return;
    }

    if (!dataStream?.length) return;

    const newDeltas = dataStream.slice(lastProcessedIndex.current + 1);
    if (newDeltas.length === 0) return;

    // Prévenir les mises à jour trop fréquentes
    if (newDeltas.length > 50) {
      console.warn(
        'DataStreamHandler - Too many deltas, throttling:',
        newDeltas.length,
      );
      return;
    }

    lastProcessedIndex.current = dataStream.length - 1;

    let finishReceived = false;

    // Traitement par lots pour améliorer les performances
    const criticalDeltas: DataStreamDelta[] = [];
    const contentDeltas: DataStreamDelta[] = [];

    (newDeltas as DataStreamDelta[]).forEach((delta: DataStreamDelta) => {
      if (
        delta.type === 'clear' ||
        delta.type === 'id' ||
        delta.type === 'title' ||
        delta.type === 'kind'
      ) {
        criticalDeltas.push(delta);
      } else if (delta.type === 'finish' || delta.type === 'completion') {
        finishReceived = true;
      } else {
        contentDeltas.push(delta);
      }
    });

    // Traiter d'abord les deltas critiques en une seule mise à jour
    if (criticalDeltas.length > 0) {
      setArtifact((draftArtifact) => {
        const base = draftArtifact || { ...initialArtifactData };
        let updated = { ...base };

        criticalDeltas.forEach((delta) => {
          switch (delta.type) {
            case 'clear':
              updated = { ...updated, content: '', status: 'streaming' };
              break;
            case 'id':
              updated = {
                ...updated,
                documentId: delta.content as string,
                status: 'streaming',
              };
              // Mettre à jour la référence de l'ID du document
              lastDocumentIdRef.current = delta.content as string;
              // Réinitialiser l'état de fin lorsqu'un nouvel ID est reçu
              hasFinishedRef.current = false;
              break;
            case 'title':
              updated = {
                ...updated,
                title: delta.content as string,
                status: 'streaming',
              };
              break;
            case 'kind':
              updated = {
                ...updated,
                kind: delta.content as ArtifactKind,
                status: 'streaming',
              };
              break;
          }
        });

        return updated;
      });
    }

    // Traiter ensuite les deltas de contenu
    if (contentDeltas.length > 0) {
      const artifactDefinition = artifactDefinitions.find(
        (def) => def.kind === artifact.kind,
      );

      if (artifactDefinition?.onStreamPart) {
        contentDeltas.forEach((delta) => {
          artifactDefinition.onStreamPart({
            streamPart: delta,
            setArtifact,
            setMetadata,
          });
        });
      }
    }

    if (finishReceived) {
      const currentDocId = artifactRef.current.documentId;
      console.log(
        'DataStreamHandler - Finish/Completion signal received for document:',
        currentDocId,
      );

      // Mark that we've received a finish signal
      hasFinishedRef.current = true;

      // Update artifact status
      setArtifact((draftArtifact) =>
        draftArtifact
          ? { ...draftArtifact, status: 'idle', isVisible: true }
          : initialArtifactData,
      );

      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set new timeout with cleanup
      timeoutRef.current = setTimeout(() => {
        console.log('DataStreamHandler - Workflow completion finalized');
      }, 1000);
    }

    // Cleanup function to clear timeout on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [dataStream, setArtifact, setMetadata, artifact.kind]); // Include all used dependencies

  return null;
}
