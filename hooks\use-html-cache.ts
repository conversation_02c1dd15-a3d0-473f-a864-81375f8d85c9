'use client';

import { useState, useEffect, useCallback } from 'react';

// Type pour les entrées du cache
interface HtmlCacheEntry {
  content: string;
  timestamp: number;
}

// Cache global pour les artifacts HTML
const globalHtmlCache = new Map<string, HtmlCacheEntry>();

// Durée de vie du cache en millisecondes (1 heure)
const CACHE_TTL = 60 * 60 * 1000;

/**
 * Hook pour gérer le cache des artifacts HTML
 * @param documentId ID du document
 * @param initialContent Contenu initial
 * @returns Contenu mis en cache et fonction pour mettre à jour le cache
 */
export function useHtmlCache(documentId: string, initialContent: string) {
  // État local pour le contenu
  const [cachedContent, setCachedContent] = useState<string>(() => {
    // Vérifier si le contenu est déjà dans le cache
    const cached = globalHtmlCache.get(documentId);

    // Si le contenu est dans le cache et n'est pas expiré, l'utiliser
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return cached.content;
    }

    // Sinon, utiliser le contenu initial
    return initialContent;
  });

  // Mettre à jour le cache lorsque le contenu initial change
  useEffect(() => {
    if (initialContent && initialContent !== '{}') {
      console.log('useHtmlCache - updating cache with new content:', {
        documentId,
        contentLength: initialContent.length,
        isValidJson: isValidJson(initialContent),
      });

      setCachedContent(initialContent);

      // Mettre à jour le cache global
      globalHtmlCache.set(documentId, {
        content: initialContent,
        timestamp: Date.now(),
      });
    }
  }, [documentId, initialContent]);

  // Fonction utilitaire pour vérifier si une chaîne est un JSON valide
  function isValidJson(str: string): boolean {
    try {
      const parsed = JSON.parse(str);
      return typeof parsed === 'object' && parsed !== null;
    } catch (e) {
      return false;
    }
  }

  // Fonction pour mettre à jour le cache
  const updateCache = useCallback(
    (newContent: string) => {
      console.log('useHtmlCache - manually updating cache:', {
        documentId,
        contentLength: newContent.length,
        isValidJson: isValidJson(newContent),
      });

      setCachedContent(newContent);

      // Mettre à jour le cache global
      globalHtmlCache.set(documentId, {
        content: newContent,
        timestamp: Date.now(),
      });
    },
    [documentId],
  );

  // Nettoyer les entrées expirées du cache
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      const now = Date.now();

      // Parcourir toutes les entrées du cache
      globalHtmlCache.forEach((entry, key) => {
        // Supprimer les entrées expirées
        if (now - entry.timestamp > CACHE_TTL) {
          globalHtmlCache.delete(key);
        }
      });
    }, CACHE_TTL);

    // Nettoyer l'intervalle lors du démontage du composant
    return () => {
      clearInterval(cleanupInterval);
    };
  }, []);

  return { cachedContent, updateCache };
}
