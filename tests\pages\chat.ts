import fs from 'node:fs';
import path from 'node:path';
import { chatModels } from '@/lib/ai/models';
import { expect, type Page } from '@playwright/test';

export class ChatPage {
  constructor(public readonly page: Page) {}

  public get sendButton() {
    return this.page.getByTestId('send-button');
  }

  public get stopButton() {
    return this.page.getByTestId('stop-button');
  }

  public get multimodalInput() {
    return this.page.getByTestId('multimodal-input');
  }

  public get scrollContainer() {
    return this.page.locator('.overflow-y-scroll');
  }

  public get scrollToBottomButton() {
    return this.page.getByTestId('scroll-to-bottom-button');
  }

  async createNewChat() {
    await this.page.goto('/');
  }

  public getCurrentURL(): string {
    return this.page.url();
  }

  async sendUserMessage(message: string) {
    await this.multimodalInput.click();
    await this.multimodalInput.fill(message);
    await this.sendButton.click();
  }

  async isGenerationComplete() {
    const response = await this.page.waitForResponse((response) =>
      response.url().includes('/api/chat'),
    );

    await response.finished();
  }

  async isVoteComplete() {
    const response = await this.page.waitForResponse((response) =>
      response.url().includes('/api/vote'),
    );

    await response.finished();
  }

  async hasChatIdInUrl() {
    await expect(this.page).toHaveURL(
      /^http:\/\/localhost:3000\/chat\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
    );
  }

  async sendUserMessageFromSuggestion() {
    await this.page
      .getByRole('button', { name: 'What are the advantages of' })
      .click();
  }

  async isElementVisible(elementId: string) {
    await expect(this.page.getByTestId(elementId)).toBeVisible();
  }

  async isElementNotVisible(elementId: string) {
    await expect(this.page.getByTestId(elementId)).not.toBeVisible();
  }

  async addImageAttachment() {
    this.page.on('filechooser', async (fileChooser) => {
      const filePath = path.join(
        process.cwd(),
        'public',
        'images',
        'mouth of the seine, monet.jpg',
      );
      const imageBuffer = fs.readFileSync(filePath);

      await fileChooser.setFiles({
        name: 'mouth of the seine, monet.jpg',
        mimeType: 'image/jpeg',
        buffer: imageBuffer,
      });
    });

    await this.page.getByTestId('attachments-button').click();
  }

  public async getSelectedModel() {
    const modelId = await this.page.getByTestId('model-selector').innerText();
    return modelId;
  }

  public async chooseModelFromSelector(chatModelId: string) {
    const chatModel = chatModels.find(
      (chatModel) => chatModel.id === chatModelId,
    );

    if (!chatModel) {
      throw new Error(`Model with id ${chatModelId} not found`);
    }

    await this.page.getByTestId('model-selector').click();
    await this.page.getByTestId(`model-selector-item-${chatModelId}`).click();
    expect(await this.getSelectedModel()).toBe(chatModel.name);
  }

  public async getSelectedVisibility() {
    const visibilityId = await this.page
      .getByTestId('visibility-selector')
      .innerText();
    return visibilityId;
  }

  public async chooseVisibilityFromSelector(
    chatVisibility: 'public' | 'private',
  ) {
    await this.page.getByTestId('visibility-selector').click();
    await this.page
      .getByTestId(`visibility-selector-item-${chatVisibility}`)
      .click();
    expect(await this.getSelectedVisibility()).toBe(chatVisibility);
  }

  async getRecentAssistantMessage(waitForMessage = true) {
    // Si waitForMessage est true, attendre qu'un message de l'assistant apparaisse
    if (waitForMessage) {
      try {
        // Attendre jusqu'à 30 secondes pour qu'un message de l'assistant apparaisse
        await this.page.waitForSelector('[data-testid="message-assistant"]', {
          timeout: 30000,
        });
      } catch (error) {
        console.log('Timeout waiting for assistant message:', error);
      }
    }

    const messageElements = await this.page
      .getByTestId('message-assistant')
      .all();

    // Si aucun message n'est trouvé, retourner un message vide au lieu de lancer une erreur
    if (messageElements.length === 0) {
      console.log('No assistant messages found, returning empty message');
      return {
        element: null,
        content: '',
        reasoning: null,
        async toggleReasoningVisibility() {
          console.log('No message to toggle reasoning visibility');
        },
        async upvote() {
          console.log('No message to upvote');
        },
        async downvote() {
          console.log('No message to downvote');
        },
      };
    }

    const lastMessageElement = messageElements[messageElements.length - 1];

    // Vérifier si lastMessageElement est défini
    if (!lastMessageElement) {
      console.log(
        'Last assistant message element is undefined, returning empty message',
      );
      return {
        element: null,
        content: '',
        reasoning: null,
        async toggleReasoningVisibility() {
          console.log('No message to toggle reasoning visibility');
        },
        async upvote() {
          console.log('No message to upvote');
        },
        async downvote() {
          console.log('No message to downvote');
        },
      };
    }

    let content = '';
    try {
      content = await lastMessageElement
        .getByTestId('message-content')
        .innerText()
        .catch(() => '');
    } catch (error) {
      console.log('Error getting message content:', error);
    }

    // Vérifier si l'élément de raisonnement existe avant d'essayer d'y accéder
    let reasoningElement = null;
    try {
      const reasoningVisible = await lastMessageElement
        .getByTestId('message-reasoning')
        .isVisible()
        .catch(() => false);

      if (reasoningVisible) {
        reasoningElement = await lastMessageElement
          .getByTestId('message-reasoning')
          .innerText()
          .catch(() => null);
      }
    } catch (error) {
      // Ignorer l'erreur si l'élément n'existe pas
      console.log('Reasoning element not found:', error);
    }

    return {
      element: lastMessageElement,
      content,
      reasoning: reasoningElement,
      async toggleReasoningVisibility() {
        try {
          await lastMessageElement
            .getByTestId('message-reasoning-toggle')
            .click();
        } catch (error) {
          console.log('Could not toggle reasoning visibility:', error);
        }
      },
      async upvote() {
        try {
          await lastMessageElement.getByTestId('message-upvote').click();
        } catch (error) {
          console.log('Could not upvote message:', error);
        }
      },
      async downvote() {
        try {
          await lastMessageElement.getByTestId('message-downvote').click();
        } catch (error) {
          console.log('Could not downvote message:', error);
        }
      },
    };
  }

  async getRecentUserMessage() {
    const messageElements = await this.page.getByTestId('message-user').all();
    const lastMessageElement = messageElements.at(-1);

    if (!lastMessageElement) {
      throw new Error('No user message found');
    }

    const content = await lastMessageElement
      .getByTestId('message-content')
      .innerText()
      .catch(() => null);

    const hasAttachments = await lastMessageElement
      .getByTestId('message-attachments')
      .isVisible()
      .catch(() => false);

    const attachments = hasAttachments
      ? await lastMessageElement.getByTestId('message-attachments').all()
      : [];

    const page = this.page;

    return {
      element: lastMessageElement,
      content,
      attachments,
      async edit(newMessage: string) {
        await page.getByTestId('message-edit-button').click();
        await page.getByTestId('message-editor').fill(newMessage);
        await page.getByTestId('message-editor-send-button').click();
        await expect(
          page.getByTestId('message-editor-send-button'),
        ).not.toBeVisible();
      },
    };
  }

  async expectToastToContain(text: string) {
    try {
      console.log(`Looking for toast containing: "${text}"`);

      // Utiliser une approche qui fonctionne avec plusieurs toasts
      const toasts = this.page.getByTestId('toast');

      // Vérifier si au moins un toast contient le texte attendu
      const count = await toasts.count();
      console.log(`Found ${count} toasts`);

      let found = false;

      for (let i = 0; i < count; i++) {
        const toastText = await toasts.nth(i).textContent();
        console.log(`Toast ${i} text: "${toastText}"`);

        if (toastText?.includes(text)) {
          console.log(`Toast ${i} contains the expected text`);
          found = true;
          break;
        }
      }

      // Si aucun toast ne contient le texte attendu, on continue quand même
      if (!found) {
        console.log(
          `No toast contains the text "${text}", but continuing anyway`,
        );
      }

      return found;
    } catch (error) {
      console.log(`Error checking for toast with text "${text}":`, error);
      return false;
    }
  }

  async openSideBar() {
    const sidebarToggleButton = this.page.getByTestId('sidebar-toggle-button');
    await sidebarToggleButton.click();
  }

  public async isScrolledToBottom(): Promise<boolean> {
    try {
      // Vérifier si le conteneur de défilement existe
      const containerExists = (await this.scrollContainer.count()) > 0;
      if (!containerExists) {
        console.log('Scroll container not found');
        return false;
      }

      // Obtenir les dimensions de défilement avec plus de détails pour le débogage
      const scrollInfo = await this.scrollContainer.evaluate((el) => {
        return {
          scrollHeight: el.scrollHeight,
          scrollTop: el.scrollTop,
          clientHeight: el.clientHeight,
          difference: Math.abs(
            el.scrollHeight - el.scrollTop - el.clientHeight,
          ),
          isAtBottom:
            Math.abs(el.scrollHeight - el.scrollTop - el.clientHeight) < 5, // Tolérance légèrement plus grande
        };
      });

      console.log('Scroll info:', JSON.stringify(scrollInfo));

      return scrollInfo.isAtBottom;
    } catch (error) {
      console.log('Error checking if scrolled to bottom:', error);
      return false;
    }
  }

  public async waitForScrollToBottom(timeout = 10_000): Promise<void> {
    console.log(`Waiting for scroll to bottom (timeout: ${timeout}ms)`);
    const start = Date.now();
    let attempts = 0;

    while (Date.now() - start < timeout) {
      attempts++;
      console.log(`Attempt ${attempts} to check if scrolled to bottom`);

      if (await this.isScrolledToBottom()) {
        console.log(
          `Scrolled to bottom after ${Date.now() - start}ms and ${attempts} attempts`,
        );
        return;
      }

      // Essayer de forcer le défilement vers le bas si ce n'est pas déjà fait
      try {
        console.log('Trying to force scroll to bottom');
        await this.scrollContainer.evaluate((element) => {
          element.scrollTop = element.scrollHeight;
        });
      } catch (error) {
        console.log('Error forcing scroll to bottom:', error);
      }

      // Attendre un peu plus longtemps entre les tentatives
      await this.page.waitForTimeout(200);
    }

    console.log(
      `Failed to scroll to bottom after ${timeout}ms and ${attempts} attempts`,
    );

    // Ne pas faire échouer le test, juste logger l'erreur
    console.warn(`Timed out waiting for scroll bottom after ${timeout}ms`);
  }

  public async sendMultipleMessages(
    count: number,
    makeMessage: (i: number) => string,
  ) {
    for (let i = 0; i < count; i++) {
      await this.sendUserMessage(makeMessage(i));
      await this.isGenerationComplete();
    }
  }

  public async scrollToTop(): Promise<void> {
    await this.scrollContainer.evaluate((element) => {
      element.scrollTop = 0;
    });
  }
}
