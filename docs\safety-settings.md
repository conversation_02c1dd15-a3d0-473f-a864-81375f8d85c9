# Google Generative AI Safety Settings

This document explains the safety settings implemented for Google Generative AI in our chatbot application.

## Overview

Safety settings help filter out harmful or inappropriate content from AI model responses. We've implemented safety settings for all Google Generative AI models used in our application to ensure that the content generated is appropriate and safe.

## API Key Configuration

To use Google Generative AI, you need to set up an API key:

1. Obtain a Google Generative AI API key from the [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Add the API key to your environment variables:

```bash
# Add to your .env file
GOOGLE_GENERATIVE_AI_API_KEY=your_api_key_here
```

## Implementation

The safety settings are implemented in `lib/ai/providers.ts`. We've configured the following safety categories:

1. `HARM_CATEGORY_HATE_SPEECH` - Filters content that expresses, incites, or promotes hate based on race, gender, religion, nationality, sexual orientation, disability status, or caste.
2. `HARM_CATEGORY_DANGEROUS_CONTENT` - Filters content that promotes, facilitates, or encourages harmful activities.
3. `HARM_CATEGORY_HARASSMENT` - Filters content that contains harassment, intimidation, or abuse.
4. `HARM_CATEGORY_SEXUALLY_EXPLICIT` - Filters sexually explicit content.

For each category, we've set the threshold to `BLOCK_MEDIUM_AND_ABOVE`, which means that content with medium or high probability of being harmful will be blocked.

## Threshold Options

The available threshold options are:

- `HARM_BLOCK_THRESHOLD_UNSPECIFIED` - Default threshold (if not specified)
- `BLOCK_LOW_AND_ABOVE` - Block content with low, medium, or high probability of being harmful
- `BLOCK_MEDIUM_AND_ABOVE` - Block content with medium or high probability of being harmful
- `BLOCK_ONLY_HIGH` - Block only content with high probability of being harmful
- `BLOCK_NONE` - Don't block any content based on safety settings

## Safety Ratings

When the model generates a response, it also provides safety ratings that indicate the probability and severity of harmful content in different categories. These ratings can be used to understand why certain content might be blocked.

Example response excerpt:

```json
{
  "safetyRatings": [
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "probability": "NEGLIGIBLE",
      "probabilityScore": 0.11027937,
      "severity": "HARM_SEVERITY_LOW",
      "severityScore": 0.28487435
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "probability": "HIGH",
      "blocked": true,
      "probabilityScore": 0.95422274,
      "severity": "HARM_SEVERITY_MEDIUM",
      "severityScore": 0.43398145
    }
  ]
}
```

## Testing

You can test the safety settings by running:

```bash
# Make sure to include your API key
GOOGLE_GENERATIVE_AI_API_KEY=your_api_key npx tsx tests/safety-settings.test.ts
```

This will test the model with both safe and potentially unsafe prompts to verify that the safety settings are working as expected.

## Customization

If you need to adjust the safety settings, you can modify the `safetySettings` array in `lib/ai/providers.ts`. For example, you might want to use different thresholds for different categories based on your application's requirements:

```typescript
// Example of customized safety settings
const safetySettings = [
  {
    category: 'HARM_CATEGORY_HATE_SPEECH' as const,
    threshold: 'BLOCK_LOW_AND_ABOVE' as const, // More strict for hate speech
  },
  {
    category: 'HARM_CATEGORY_DANGEROUS_CONTENT' as const,
    threshold: 'BLOCK_MEDIUM_AND_ABOVE' as const,
  },
  {
    category: 'HARM_CATEGORY_HARASSMENT' as const,
    threshold: 'BLOCK_MEDIUM_AND_ABOVE' as const,
  },
  {
    category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT' as const,
    threshold: 'BLOCK_ONLY_HIGH' as const, // Less strict for this category
  },
];
```
