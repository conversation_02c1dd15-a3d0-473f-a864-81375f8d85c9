'use client';

import React, { useEffect, useCallback } from 'react';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';
import type { PlaceImage } from '@/lib/types';

interface ImageModalProps {
  images: PlaceImage[];
  currentIndex: number;
  isOpen: boolean;
  onClose: () => void;
  onPrevious: () => void;
  onNext: () => void;
  onImageSelect?: (index: number) => void;
  placeName: string;
}

const ImageModal: React.FC<ImageModalProps> = ({
  images,
  currentIndex,
  isOpen,
  onClose,
  onPrevious,
  onNext,
  onImageSelect,
  placeName,
}) => {
  // Gérer les touches du clavier
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          onPrevious();
          break;
        case 'ArrowRight':
          onNext();
          break;
      }
    },
    [isOpen, onClose, onPrevious, onNext],
  );

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Empêcher le scroll du body
      document.body.style.overflow = 'hidden';
    } else {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, handleKeyDown]);

  if (!isOpen || !images[currentIndex]) return null;

  const currentImage = images[currentIndex];

  return (
    <div
      className="fixed inset-0 z-[9999] bg-black/90 flex items-center justify-center"
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="image-modal-title"
    >
      {/* Bouton de fermeture */}
      <button
        type="button"
        onClick={onClose}
        className="absolute top-4 right-4 z-10 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
        aria-label="Fermer"
      >
        <X size={24} />
      </button>

      {/* Boutons de navigation */}
      {images.length > 1 && (
        <>
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onPrevious();
            }}
            className="absolute left-4 top-1/2 -translate-y-1/2 z-10 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
            aria-label="Image précédente"
          >
            <ChevronLeft size={24} />
          </button>

          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onNext();
            }}
            className="absolute right-4 top-1/2 -translate-y-1/2 z-10 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
            aria-label="Image suivante"
          >
            <ChevronRight size={24} />
          </button>
        </>
      )}

      {/* Conteneur de l'image */}
      <div
        className="relative max-w-[90vw] max-h-[90vh] flex flex-col items-center"
        onClick={(e) => e.stopPropagation()}
        role="img"
        aria-label={`Image de ${placeName}`}
      >
        {/* Image principale */}
        <img
          src={currentImage.url}
          alt={currentImage.description || placeName}
          className="max-w-full max-h-[80vh] object-contain rounded-lg shadow-2xl"
          loading="lazy"
        />

        {/* Informations sur l'image */}
        <div className="mt-4 text-center text-white">
          <h3 id="image-modal-title" className="text-lg font-semibold mb-1">
            {placeName}
          </h3>
          {currentImage.description && (
            <p className="text-sm text-gray-300 mb-2">
              {currentImage.description}
            </p>
          )}
          {images.length > 1 && (
            <p className="text-xs text-gray-400">
              {currentIndex + 1} / {images.length}
            </p>
          )}
        </div>

        {/* Miniatures si plusieurs images */}
        {images.length > 1 && (
          <div className="mt-4 flex gap-2 max-w-full overflow-x-auto pb-2">
            {images.map((image, index) => (
              <button
                key={`thumbnail-${index}-${image.url}`}
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  // Utiliser une fonction de callback pour changer l'index
                  onImageSelect?.(index);
                }}
                className={`flex-shrink-0 w-16 h-12 rounded border-2 overflow-hidden transition-all ${
                  index === currentIndex
                    ? 'border-white scale-110'
                    : 'border-gray-500 hover:border-gray-300'
                }`}
              >
                <img
                  src={image.thumbnail || image.url}
                  alt={`Miniature ${index + 1}`}
                  className="w-full h-full object-cover"
                  loading="lazy"
                />
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageModal;
