/**
 * Test Mocks
 * 
 * This module exports all mock implementations for testing.
 */

// Export mock configuration
export * from './config';

// Export mock AI provider
export * from './ai-provider';

// Export mock weather tool
export * from './weather-tool';

// Export a helper function to initialize all mocks
export function initializeMocks() {
  // Set default environment variables for mocks if not already set
  if (process.env.USE_MOCK_AI === undefined) {
    process.env.USE_MOCK_AI = 'true';
  }
  
  if (process.env.USE_MOCK_WEATHER === undefined) {
    process.env.USE_MOCK_WEATHER = 'true';
  }
  
  if (process.env.USE_MOCK_AUTH === undefined) {
    process.env.USE_MOCK_AUTH = 'true';
  }
  
  if (process.env.USE_MOCK_DATABASE === undefined) {
    process.env.USE_MOCK_DATABASE = 'true';
  }
  
  if (process.env.MOCK_LATENCY === undefined) {
    process.env.MOCK_LATENCY = '500';
  }
  
  console.log('Mocks initialized with configuration:', {
    useMockAI: process.env.USE_MOCK_AI,
    useMockWeather: process.env.USE_MOCK_WEATHER,
    useMockAuth: process.env.USE_MOCK_AUTH,
    useMockDatabase: process.env.USE_MOCK_DATABASE,
    mockLatency: process.env.MOCK_LATENCY
  });
}
