import { tool } from 'ai';
import { z } from 'zod';
import { tavily } from '@tavily/core';
import type { Session } from 'next-auth';
import type { DataStreamWriter } from 'ai';

// Define the Video interface
export interface Video {
  link: string;
  title?: string;
  thumbnail?: string;
}

/**
 * Extracts YouTube video ID from a URL
 * @param url YouTube URL
 * @returns Video ID or empty string if not found
 */
export const getYouTubeVideoId = (url: string): string => {
  const match = url.match(
    /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/,
  );
  return match ? match[1] : '';
};

/**
 * Checks if a URL is a valid YouTube video URL
 * @param url URL to check
 * @returns Boolean indicating if the URL is a valid YouTube video
 */
const isYouTubeVideoUrl = (url: string): boolean => {
  return !!getYouTubeVideoId(url);
};

/**
 * Extracts YouTube video information from search results
 * @param results Search results from Tavily
 * @returns Array of Video objects
 */
const extractYouTubeVideos = (results: any[]): Video[] => {
  const videos: Video[] = [];

  // Process each search result
  for (const result of results) {
    const { url, title } = result;

    // Check if the URL is a YouTube video
    if (isYouTubeVideoUrl(url)) {
      const videoId = getYouTubeVideoId(url);

      // Only add if we have a valid video ID
      if (videoId) {
        videos.push({
          link: url,
          title: title || '',
          thumbnail: `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`,
        });
      }
    }
  }

  return videos;
};

/**
 * YouTube search tool using Tavily
 */
export const youtube_search = ({
  session,
  dataStream,
}: {
  session: Session;
  dataStream: DataStreamWriter;
}) =>
  tool({
    description: 'Search for YouTube videos on a specific topic',
    parameters: z.object({
      query: z.string().describe('The search query for finding YouTube videos'),
      maxResults: z
        .number()
        .default(9)
        .describe('Maximum number of videos to return')
        .optional(),
      language: z
        .string()
        .describe('Preferred language for search results (e.g., "fr", "en")')
        .optional(),
    }),
    execute: async ({
      query,
      maxResults = 9,
      language,
    }: {
      query: string;
      maxResults?: number;
      language?: string;
    }) => {
      // Ensure we have at least 9 videos, but allow more if requested
      maxResults = Math.max(maxResults, 9);
      const apiKey = process.env.TAVILY_API_KEY || '';
      const tvly = tavily({ apiKey });

      // Verify API key is set
      if (!apiKey) {
        console.warn('TAVILY_API_KEY is not set in environment variables');
        return { videos: [] };
      }

      console.log('YouTube Search Query:', query);
      console.log('Max Results:', maxResults);
      console.log('Preferred Language:', language);

      try {
        // Add "youtube" to the search query to improve results
        const enhancedQuery = `${query} youtube video`;

        // Configure search parameters
        const searchParams: any = {
          topic: 'general',
          maxResults: maxResults * 3, // Request more results to filter for YouTube videos
          searchDepth: 'basic',
          includeAnswer: false,
          includeImages: false,
        };

        // Add language parameter if specified
        if (language) {
          searchParams.language = language;
        }

        // Execute search
        const data = await tvly.search(enhancedQuery, searchParams);

        // Extract YouTube videos from results
        const videos = extractYouTubeVideos(data.results || []);

        // Limit to requested number of videos
        const limitedVideos = videos.slice(0, maxResults);

        // Add annotation for query completion
        dataStream.writeMessageAnnotation({
          type: 'youtube_search_completion',
          data: {
            query,
            status: 'completed',
            resultsCount: limitedVideos.length,
          },
        });

        return {
          videos: limitedVideos,
        };
      } catch (error) {
        console.error('Error searching for YouTube videos:', error);
        return { videos: [] };
      }
    },
  });
