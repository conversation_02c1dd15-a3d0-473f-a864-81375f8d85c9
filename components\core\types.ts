// Types pour la recherche extrême

export interface SearchResult {
    title: string;
    url: string;
    content: string;
    publishedDate: string;
    favicon: string;
}

export interface Research {
    text: string;
    toolResults: any[];
    sources: SearchResult[];
    charts: any[];
}

export interface ResearchPlan {
    title: string;
    todos: string[];
}

export interface QueryBlockData {
    queryId: string;
    query: string;
    sources: Array<{ title: string; url: string; favicon?: string }>;
    content: Array<{ title: string; url: string; text: string; favicon?: string }>;
}

export interface CodeExecutionData {
    codeId: string;
    code: string;
    title: string;
    result?: string;
    charts?: any[];
    status: 'running' | 'complete';
}

export interface TimelineItemData {
    id: string;
    type: 'search' | 'code';
    title: string;
    status: 'loading' | 'success' | 'no_results';
    timestamp: number;
    searchData?: QueryBlockData;
    codeData?: CodeExecutionData;
}

export type QueryStatus = 'loading' | 'success' | 'no_results';

export enum SearchCategory {
    NEWS = "news",
    COMPANY = "company",
    RESEARCH_PAPER = "research paper",
    GITHUB = "github",
    FINANCIAL_REPORT = "financial report",
}

export interface ExtremeSearchConfig {
    exaApiKey: string;
    daytonaApiKey: string;
    languageModel: any;
    daytonaSnapshot?: string;
}

// Types pour les annotations de données
export interface StatusAnnotation {
    status: {
        title: string;
        type?: 'code' | 'result';
        code?: string;
        result?: string;
        charts?: any[];
    };
}

export interface PlanAnnotation {
    plan: ResearchPlan[];
}

export interface SearchQueryAnnotation {
    type: 'search_query';
    queryId: string;
    query: string;
}

export interface SourceAnnotation {
    type: 'source';
    queryId: string;
    source: {
        title: string;
        url: string;
        favicon?: string;
    };
}

export interface ContentAnnotation {
    type: 'content';
    queryId: string;
    content: {
        title: string;
        url: string;
        text: string;
        favicon?: string;
    };
}

export type ExtremeSearchAnnotation = 
    | StatusAnnotation 
    | PlanAnnotation 
    | SearchQueryAnnotation 
    | SourceAnnotation 
    | ContentAnnotation;