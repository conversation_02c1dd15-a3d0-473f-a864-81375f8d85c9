# Extreme Search Export ⚡️

Ce dossier contient tout le nécessaire pour réutiliser la fonctionnalité **Extreme Search** avec sa timeline annotée dans n’importe quelle application React + Node/TypeScript.

## Structure du dossier

```text
extreme-search-export/
├─ backend/
│  ├─ extreme-search.ts        # Implémentation complète de l’outil (LLM + Exa + annotations)
│  └─ web-search.ts            # Aide pour les requêtes Exa (appelée par l’outil)
└─ frontend/
   └─ components/
      ├─ extreme-search.tsx           # Composant React qui rend la timeline + auto-scroll
      ├─ tool-invocation-list-view.tsx# Routeur qui instancie ExtremeSearch
      └─ messages.tsx                 # Relais annotations → UI
```

## Intégration d'Extreme Search

Ce dossier contient tout le nécessaire pour implémenter la fonctionnalité Extreme Search (recherche avancée avec timeline en temps réel) dans une application existante.

## Structure des dossiers

```
extreme-search-export/
├── backend/
│   ├── extreme-search.ts    # Logique principale de la recherche
│   └── route.ts            # Exemple de route API Next.js
├── frontend/
│   ├── components/
│   │   └── extreme-search.tsx  # Composant React de la timeline
│   └── ExampleExtremeSearchPage.tsx  # Exemple de page d'utilisation
└── README.md               # Ce fichier
```

## Fonctionnement général

1. **Flux utilisateur** :
   - L'utilisateur saisit une requête
   - Le frontend envoie la requête à l'API `/api/extreme-search`
   - Le backend traite la requête et renvoie des mises à jour en temps réel via SSE
   - La timeline se met à jour automatiquement avec les résultats

2. **Architecture** :
   - **Frontend** : Utilise `@ai-sdk/react` pour gérer le streaming
   - **Backend** : Traite la requête et génère des annotations
   - **Communication** : SSE (Server-Sent Events) pour les mises à jour en temps réel

## Prérequis

- Node.js 18+
- Framework supporté :
  - Next.js (recommandé)
  - Create React App (avec configuration supplémentaire)
  - Autres frameworks React (nécessite adaptation)

## Installation

1. **Backend** :
   ```bash
   # Installer les dépendances
   npm install @ai-sdk/ai zod
   
   # Variables d'environnement requises
   EXA_API_KEY=votre_cle_api_exa
   ```

2. **Frontend** :
   ```bash
   npm install @ai-sdk/react
   ```

## Configuration requise

1. **Clé API Exa** :
   - Inscrivez-vous sur [Exa AI](https://exa.ai/)
   - Créez une clé API dans le tableau de bord
   - Ajoutez-la à votre fichier `.env.local`

2. **Configuration CORS** (si nécessaire) :
   - Assurez-vous que votre API accepte les requêtes de votre domaine frontend

## 1. Back-end

`backend/extreme-search.ts` expose la fonction/outil :

```ts
export const extremeSearchTool = (dataStream?: DataStreamWriter) => ({
  /* … */
})
```

Fonctionnement :
1. Planifie la recherche via `generateObject` (LLM).
2. Supervise jusqu’à `totalTodos` actions avec `generateText`, en fournissant deux sous-outils :
   • `webSearch` (défini dans ce même fichier)  
   • `codeRunner` (exécution Python Daytona lorsque nécessaire).
3. « Stream » des annotations au client avec `dataStream.writeMessageAnnotation(...)` :
   * `status` – étape en cours.
   * `search_query` – nouvelle requête.
   * `source` – URL trouvée.
   * `content` – extrait de la page.
   * `result` – résultat d’exécution de code.

### Dépendances à fournir

* Node 18+, TypeScript.
* Packages : `ai`, `exa-js`, `@daytonaio/sdk`, `zod`.
* Variables d’environnement :
  * `EXA_API_KEY` – clé API Exa.
  * `DAYTONA_API_KEY` – clé Daytona si tu souhaites l’exécution Python.

Si tu ne veux pas la limite d’usage ou la persistance, retire les imports suivants :
* `lib/performance-cache.ts`, `lib/db/*`, `lib/auth.ts`, `lib/constants.ts`.

## 2. Front-end

### Composant cœur : `frontend/components/extreme-search.tsx`

* Reçoit `annotations` (array JSON) + `toolInvocation`.
* Construit `timelineItems` via `useMemo` et les rend dans un conteneur scrollable.
* Effet `useEffect` → auto-scroll quand de nouvelles annotations arrivent.
* Supporte l’expansion/repli et l’affichage de graphiques.

### Guide d'intégration détaillé

### 1. Mise en place du backend

Créez un fichier `app/api/extreme-search/route.ts` dans votre projet Next.js avec le contenu de `backend/route.ts`.

### 2. Configuration du frontend

#### Option 1 : Utilisation avec Next.js (Pages Router)

1. Copiez `frontend/components/extreme-search.tsx` dans votre dossier `components/`
2. Créez une nouvelle page (ex: `pages/search.tsx`) avec le contenu de `ExampleExtremeSearchPage.tsx`

#### Option 2 : Utilisation avec Create React App

1. Installez `@ai-sdk/react` et `eventsource-parser`
2. Copiez le composant `extreme-search.tsx` dans votre dossier `src/components/`
3. Créez un hook personnalisé pour gérer le streaming (exemple fourni ci-dessous)

### 3. Configuration du thème

Le composant utilise Tailwind CSS pour le style. Assurez-vous d'avoir Tailwind configuré dans votre projet, ou remplacez les classes par votre propre système de style.

## Exemple d'utilisation avancée

### Personnalisation de l'interface

Vous pouvez personnaliser l'apparence de la timeline en modifiant le composant `ExtremeSearch`. Par exemple, pour changer les couleurs :

```tsx
<ExtremeSearch
  annotations={annotations}
  toolInvocation={{ id: 'custom-search', name: 'extreme_search' }}
  className="bg-gray-50 rounded-lg p-4"
/>
```

### Gestion des erreurs

Le composant gère automatiquement les erreurs de streaming. Pour une gestion personnalisée :

```tsx
const { error } = useChat({
  api: '/api/extreme-search',
  onError: (err) => {
    console.error('Erreur de streaming:', err);
    // Afficher un message d'erreur à l'utilisateur
  },
});
```

## Dépannage

### Le streaming ne démarre pas
- Vérifiez que la route API est correctement configurée
- Vérifiez les logs du serveur pour des erreurs
- Assurez-vous que la clé API Exa est valide

### La timeline ne s'affiche pas
- Vérifiez que `annotations` contient bien des données
- Inspectez la console navigateur pour des erreurs JavaScript
- Vérifiez que le composant `ExtremeSearch` est correctement importé

## Performances

- Le composant utilise `React.memo` pour optimiser les rendus
- Les mises à jour de la timeline sont optimisées pour les flux fréquents
- Pour de meilleures performances, évitez de surcharger le composant avec des rendus coûteux

## Sécurité

- Ne stockez jamais la clé API côté client
- Validez et assainissez toutes les entrées utilisateur
- Limitez le débit des requêtes pour éviter les abus minimale

```tsx
const [annotations, setAnnotations] = useState<JSONValue[]>([]);

useEffect(() => {
  const es = new EventSource('/api/extreme-search?prompt=...');
  es.onmessage = (e) => {
    setAnnotations((prev) => [...prev, JSON.parse(e.data)]);
  };
  return () => es.close();
}, []);

return (
  <ExtremeSearch annotations={annotations} toolInvocation={{ /* id, name… */ }} />
);
```

`tool-invocation-list-view.tsx` et `messages.tsx` montrent comment intégrer ce composant dans un flux de chat existant ; tu peux les adapter ou appeler directement `ExtremeSearch`.

### Dépendances UI

Les composants utilisent :
* React 18+.
* TailwindCSS + shadcn/ui (`Card`, `Button`, etc.).
Assure-toi de configurer Tailwind ou remplace par tes propres composants.

## 3. Flux de données recommandé

1. **Client** envoie `POST /api/extreme-search` (body: `{ prompt }`).
2. **Serveur** lance `extremeSearchTool(dataStream)` et renvoie un flux (SSE ou fetch streaming).
3. À chaque annotation reçue, le client la pousse dans le state ➜ la timeline se met à jour instantanément.

## 4. Personnalisation rapide

* Pour **désactiver l’exécution Python** : retire le tool `codeRunner`.
* Pour **modifier la stratégie de recherche** : ajuste `searchWeb()` ou le prompt système dans `generateText`.
* Pour **mettre un quota** : réutilise `lib/performance-cache.ts` et `lib/db/extreme_search_usage`.

---

Prêt ! Copie ce répertoire dans ton projet, adapte les chemins d’import et les clés API, puis profite de l’Extreme Search avec timeline annotée 🚀.
