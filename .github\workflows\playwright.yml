name: Playwright Tests
on:
  push:
    branches: [main, master]
  pull_request:
    branches: [main, master]

jobs:
  test:
    timeout-minutes: 30
    runs-on: ubuntu-latest
    env:
      AUTH_SECRET: ${{ secrets.AUTH_SECRET }}
      POSTGRES_URL: ${{ secrets.POSTGRES_URL }}
      # Add mock values for tests
      MEM0_API_KEY: ${{ secrets.MEM0_API_KEY || 'test-api-key' }}
      MEM0_ORG_ID: ${{ secrets.MEM0_ORG_ID || 'test-org-id' }}
      MEM0_PROJECT_ID: ${{ secrets.MEM0_PROJECT_ID || 'test-project-id' }}
      # Redis URL for resumable streams (will use mock in test environment)
      REDIS_KV_URL: ${{ secrets.REDIS_KV_URL || 'redis://localhost:6379' }}
      # Mock Blob token for tests
      BLOB_READ_WRITE_TOKEN: 'mock-blob-token'
      # Indicate we're in test environment
      PLAYWRIGHT: 'True'
      # Disable real memory service calls
      MEMORY_MOCK: 'True'
      # Disable real blob service calls
      BLOB_MOCK: 'True'
      # Ajouter un timeout plus long pour les tests
      PLAYWRIGHT_TIMEOUT: '120000'

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Cache Playwright browsers
        uses: actions/cache@v3
        id: playwright-cache
        with:
          path: ~/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-${{ hashFiles('**/pnpm-lock.yaml') }}

      - name: Install Playwright Browsers
        if: steps.playwright-cache.outputs.cache-hit != 'true'
        run: pnpm exec playwright install --with-deps chromium

      - name: Run Playwright tests
        run: pnpm test

      - uses: actions/upload-artifact@v4
        if: always() && !cancelled()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 7

      # Nouvelle étape de nettoyage
      - name: Clean up test users
        if: always() # S'exécute même si les tests échouent
        run: pnpm exec tsx scripts/cleanup-test-users.ts
