import { memo } from 'react';
import { CrossIcon } from './icons';
import { Button } from './ui/button';
import { initialArtifactData, useArtifact } from '@/hooks/use-artifact';

function PureArtifactCloseButton() {
  const { setArtifact } = useArtifact();

  return (
    <Button
      data-testid="artifact-close-button"
      variant="outline"
      className="h-fit p-2 dark:hover:bg-zinc-700"
      onClick={() => {
        setArtifact((currentArtifact) => {
          // Si l'artifact est en cours de streaming, le cacher simplement
          if (currentArtifact.status === 'streaming') {
            return {
              ...currentArtifact,
              isVisible: false,
            };
          }

          // Pour les artifacts HTML, préserver le contenu, le documentId et le titre
          if (currentArtifact.kind === 'html') {
            console.log('Closing HTML artifact:', {
              kind: currentArtifact.kind,
              documentId: currentArtifact.documentId,
              title: currentArtifact.title,
              contentLength: currentArtifact.content?.length || 0,
            });

            // Simplement cacher l'artifact sans modifier son état
            return {
              ...currentArtifact,
              isVisible: false,
            };
          }

          // Pour les autres types d'artifacts, réinitialiser mais préserver le type
          return {
            ...initialArtifactData,
            kind: currentArtifact.kind,
            status: 'idle',
          };
        });
      }}
    >
      <CrossIcon size={18} />
    </Button>
  );
}

export const ArtifactCloseButton = memo(PureArtifactCloseButton, () => true);
