import { z } from 'zod';
import { tool } from 'ai';

// Define and export the tool configuration
export const getCryptocurrencyMarket = tool({
  description:
    'Display a cryptocurrency market screener showing comprehensive market data, prices, and performance metrics for cryptocurrencies',
  parameters: z.object({
    // No parameters needed for the market screener as it shows a predefined view
  }),
  execute: async () => {
    // Return a simple object that will be used by the frontend to render the component
    return {
      type: 'cryptocurrency_market',
      data: {},
    };
  },
});
