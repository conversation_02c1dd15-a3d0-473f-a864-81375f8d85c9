declare global {
  interface Window {
    loadPyodide: (options: {
      indexURL: string;
      [key: string]: any;
    }) => Promise<PyodideInterface>;
  }

  var loadPyodide: Window['loadPyodide'];

  interface PyodideInterface {
    runPythonAsync: (code: string) => Promise<any>;
    loadPackagesFromImports: (code: string, options?: {
      messageCallback?: (message: string) => void;
    }) => Promise<void>;
    setStdout: (options: {
      batched: (output: string) => void;
    }) => void;
    [key: string]: any;
  }
}

export {};