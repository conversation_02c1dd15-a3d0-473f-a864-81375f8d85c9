import { drizzle } from 'drizzle-orm/postgres-js';
import { sql } from 'drizzle-orm';
import postgres from 'postgres';
import { config } from 'dotenv';

// Charger les variables d'environnement
config();

async function testDatabaseConnection() {
  try {
    // Vérifier que l'URL de la base de données est définie
    if (!process.env.POSTGRES_URL) {
      throw new Error('POSTGRES_URL environment variable is not defined');
    }

    console.log(
      'Trying to connect to database with URL:',
      process.env.POSTGRES_URL.replace(/:[^:]*@/, ':****@'),
    ); // Masquer le mot de passe

    // Créer une connexion à la base de données
    const client = postgres(process.env.POSTGRES_URL, {
      max: 1,
      ssl: process.env.POSTGRES_URL.includes('supabase')
        ? { rejectUnauthorized: false }
        : false,
      connect_timeout: 10, // Timeout de connexion en secondes
    });

    const db = drizzle(client);

    // Tester la connexion avec une requête simple
    console.log('Testing connection...');

    // Utiliser l'instance drizzle pour exécuter une requête SQL
    const result = await db.execute(sql`SELECT current_timestamp as time`);

    console.log('Connection successful!');
    console.log('Current database time:', result[0].time);

    // Fermer la connexion
    await client.end();
    console.log('Connection closed');

    return true;
  } catch (error) {
    console.error('Failed to connect to database:', error);
    return false;
  }
}

// Exécuter le test de connexion
testDatabaseConnection()
  .then((success) => {
    if (success) {
      console.log('Database connection test passed!');
      process.exit(0);
    } else {
      console.error('Database connection test failed!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
