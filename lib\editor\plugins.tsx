import { Plugin, PluginKey } from 'prosemirror-state';
import { DecorationSet } from 'prosemirror-view';

export const suggestionsPluginKey = new PluginKey('suggestions');

export const suggestionsPlugin = new Plugin({
  key: suggestionsPluginKey,
  state: {
    init() {
      return {
        decorations: DecorationSet.empty,
        selected: null,
      };
    },
    apply(tr, pluginState) {
      // Si une mise à jour explicite est fournie via setMeta, l'utiliser
      const meta = tr.getMeta(suggestionsPluginKey);
      if (meta) {
        return meta;
      }

      // Sinon, ajuster les décorations en fonction des modifications du document
      if (tr.docChanged && pluginState.decorations) {
        const newDecorations = pluginState.decorations.map(tr.mapping, tr.doc);
        return {
          ...pluginState,
          decorations: newDecorations,
        };
      }

      return pluginState;
    },
  },
  props: {
    decorations(state) {
      return suggestionsPluginKey.getState(state)?.decorations;
    },
  },
  view() {
    return {
      update: (view) => {
        console.log("Mise à jour de la vue de l'éditeur");
        // Forcer le rafraîchissement des décorations si nécessaire
      },
    };
  },
});
