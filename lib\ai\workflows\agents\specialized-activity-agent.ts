import { z } from 'zod';
import { generateObject, type LanguageModelV1 } from 'ai';
import type { DestinationInfo } from '../types';
import type { UserPreferences } from './preference-agent';
import { TripType, type TripClassification } from './classifier-agent';

/**
 * Interface for cycling route
 */
export interface CyclingRoute {
  name: string;
  description: string;
  distance: string;
  difficulty: 'easy' | 'moderate' | 'challenging' | 'difficult';
  elevationGain: string;
  estimatedTime: string;
  surfaceType: string;
  startPoint: {
    name: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  };
  endPoint: {
    name: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  };
  waypoints: {
    name: string;
    description: string;
    type: 'viewpoint' | 'rest' | 'attraction' | 'food' | 'water' | 'other';
    coordinates: {
      lat: string;
      lng: string;
    };
  }[];
  highlights: string[];
  tips: string[];
  bestTimeToRide: string;
  bikeRentalOptions?: {
    name: string;
    location: string;
    priceRange: string;
    bikeTypes: string[];
    website?: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  }[];
  imageKeywords: string[];
}

/**
 * Interface for hiking trail
 */
export interface HikingTrail {
  name: string;
  description: string;
  distance: string;
  difficulty: 'easy' | 'moderate' | 'challenging' | 'difficult';
  elevationGain: string;
  estimatedTime: string;
  trailType: 'loop' | 'out-and-back' | 'point-to-point' | 'network';
  startPoint: {
    name: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  };
  endPoint?: {
    name: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  };
  waypoints: {
    name: string;
    description: string;
    type: 'viewpoint' | 'rest' | 'attraction' | 'water' | 'other';
    coordinates: {
      lat: string;
      lng: string;
    };
  }[];
  highlights: string[];
  tips: string[];
  bestTimeToHike: string;
  requiredGear: string[];
  imageKeywords: string[];
}

/**
 * Interface for beach activity
 */
export interface BeachActivity {
  beaches: {
    name: string;
    description: string;
    type: 'sandy' | 'rocky' | 'pebble' | 'coral' | 'other';
    facilities: string[];
    activities: string[];
    bestTimeToVisit: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  }[];
  waterActivities: {
    name: string;
    description: string;
    providers?: {
      name: string;
      location: string;
      priceRange: string;
      website?: string;
      coordinates: {
        lat: string;
        lng: string;
      };
    }[];
  }[];
  tips: string[];
  imageKeywords: string[];
}

/**
 * Interface for shopping information
 */
export interface ShoppingInfo {
  areas: {
    name: string;
    description: string;
    type:
      | 'mall'
      | 'street'
      | 'market'
      | 'department-store'
      | 'boutique-district'
      | 'outlet';
    highlights: string[];
    bestFor: string[];
    priceRange: string;
    openingHours: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  }[];
  shops: {
    name: string;
    description: string;
    category: string;
    specialties: string[];
    priceRange: string;
    location: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  }[];
  tips: string[];
  bestTimeToShop: string;
  taxRefundInfo?: string;
  imageKeywords: string[];
}

/**
 * Interface for specialized activity data
 */
export interface SpecializedActivityData {
  cyclingRoutes?: CyclingRoute[];
  hikingTrails?: HikingTrail[];
  beachActivities?: BeachActivity;
  shoppingInfo?: ShoppingInfo;
  customSections: {
    title: string;
    content: string;
    type: 'text' | 'list' | 'table' | 'chart';
  }[];
}

/**
 * SpecializedActivityAgent is responsible for providing detailed information
 * about specific activities like cycling, hiking, beach activities, etc.
 */
export class SpecializedActivityAgent {
  private model: LanguageModelV1;

  constructor(model: LanguageModelV1) {
    this.model = model;
  }

  /**
   * Get specialized activity data based on trip classification
   */
  async getSpecializedActivityData(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
    classification: TripClassification,
  ): Promise<SpecializedActivityData> {
    try {
      console.log(
        'Getting specialized activity data for:',
        classification.primaryType,
      );

      // Check if duration is null or undefined
      if (
        destinationInfo.duration === null ||
        destinationInfo.duration === undefined
      ) {
        console.log(
          'Duration is null or undefined. Using default specialized activity data.',
        );
        // We can still get specialized activity data without a duration
      }

      // Search for activity information
      const searchResults = await this.searchActivityInfo(
        destinationInfo,
        classification.primaryType,
        classification.primaryActivity,
      );

      // Get specialized data based on trip type and keywords
      const primaryType = classification.primaryType;
      const secondaryTypes = classification.secondaryTypes || [];
      const primaryActivity = classification.primaryActivity || '';
      const keywords = classification.keywords || [];

      // Check for specific activities
      const hasCycling =
        primaryType === TripType.CYCLING ||
        secondaryTypes.includes(TripType.CYCLING) ||
        primaryActivity.toLowerCase().includes('cycling') ||
        primaryActivity.toLowerCase().includes('vélo') ||
        keywords.some(
          (k) =>
            k.toLowerCase().includes('cycling') ||
            k.toLowerCase().includes('vélo') ||
            k.toLowerCase().includes('bike'),
        );

      const hasShopping =
        primaryActivity.toLowerCase().includes('shopping') ||
        primaryActivity.toLowerCase().includes('boutique') ||
        keywords.some(
          (k) =>
            k.toLowerCase().includes('shopping') ||
            k.toLowerCase().includes('boutique') ||
            k.toLowerCase().includes('magasin'),
        );

      // Create result with custom sections
      const result: SpecializedActivityData = {
        customSections: await this.getCustomSections(
          destinationInfo,
          classification.primaryType,
          searchResults,
        ),
      };

      // Add specialized data based on detected activities
      if (hasCycling) {
        console.log('Adding cycling routes to specialized activity data');
        result.cyclingRoutes = await this.getCyclingRoutes(
          destinationInfo,
          userPreferences,
          searchResults,
        );
      }

      if (hasShopping) {
        console.log('Adding shopping information to specialized activity data');
        result.shoppingInfo = await this.getShoppingInfo(
          destinationInfo,
          userPreferences,
          searchResults,
        );
      }

      return result;
    } catch (error) {
      console.error('Error getting specialized activity data:', error);

      // Return empty data if generation fails
      return {
        customSections: [],
      };
    }
  }

  /**
   * Search for activity information
   */
  private async searchActivityInfo(
    destinationInfo: DestinationInfo,
    tripType: TripType,
    primaryActivity: string,
  ): Promise<any> {
    try {
      console.log(
        'Searching for activity information:',
        destinationInfo.destination,
        tripType,
        primaryActivity,
      );

      // Return mock search results instead of using web_search
      return [
        {
          results: [
            {
              title: `Activities in ${destinationInfo.destination}`,
              url: `https://example.com/activities/${destinationInfo.destination}`,
              snippet: `Find the best activities in ${destinationInfo.destination}. Popular options include ${primaryActivity || 'sightseeing'}.`,
            },
            {
              title: `Travel Guide for ${destinationInfo.destination}`,
              url: `https://example.com/guide/${destinationInfo.destination}`,
              snippet: `Comprehensive travel guide for ${destinationInfo.destination}, including top attractions, activities, and local tips.`,
            },
          ],
        },
      ];
    } catch (error) {
      console.error('Error searching activity information:', error);
      return { results: [] };
    }
  }

  /**
   * Get cycling routes
   */
  private async getCyclingRoutes(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
    searchResults: any,
  ): Promise<CyclingRoute[]> {
    try {
      // Generate cycling routes using the model
      const { object: cyclingData } = await generateObject({
        model: this.model,
        system: `You are an expert cycling guide who specializes in creating detailed cycling route information.
        Create detailed cycling routes for the destination based on the search results.
        Include accurate information about distance, difficulty, elevation gain, and estimated time.
        Provide specific start and end points with coordinates, and include waypoints along the route.
        Consider the user's preferences and activity level when recommending routes.`,
        prompt: `Create detailed cycling routes for ${destinationInfo.destination}, ${destinationInfo.country}
        based on these user preferences:

        ${JSON.stringify(userPreferences, null, 2)}

        Use the following search results to inform your recommendations:
        ${JSON.stringify(searchResults, null, 2)}

        Provide 2-3 cycling routes with varying difficulties and distances.
        Include specific details about each route, including:
        - Route name and description
        - Distance and difficulty
        - Elevation gain and estimated time
        - Surface type
        - Start and end points with coordinates
        - Waypoints along the route
        - Highlights and tips
        - Best time to ride
        - Bike rental options if available`,
        schema: z.object({
          routes: z.array(
            z.object({
              name: z.string().describe('Name of the cycling route'),
              description: z.string().describe('Description of the route'),
              distance: z.string().describe('Distance of the route'),
              difficulty: z
                .enum(['easy', 'moderate', 'challenging', 'difficult'])
                .describe('Difficulty level'),
              elevationGain: z.string().describe('Elevation gain of the route'),
              estimatedTime: z
                .string()
                .describe('Estimated time to complete the route'),
              surfaceType: z
                .string()
                .describe('Type of surface (road, gravel, etc.)'),
              startPoint: z.object({
                name: z.string().describe('Name of the start point'),
                coordinates: z.object({
                  lat: z.string().describe('Latitude coordinate'),
                  lng: z.string().describe('Longitude coordinate'),
                }),
              }),
              endPoint: z.object({
                name: z.string().describe('Name of the end point'),
                coordinates: z.object({
                  lat: z.string().describe('Latitude coordinate'),
                  lng: z.string().describe('Longitude coordinate'),
                }),
              }),
              waypoints: z.array(
                z.object({
                  name: z.string().describe('Name of the waypoint'),
                  description: z
                    .string()
                    .describe('Description of the waypoint'),
                  type: z
                    .enum([
                      'viewpoint',
                      'rest',
                      'attraction',
                      'food',
                      'water',
                      'other',
                    ])
                    .describe('Type of waypoint'),
                  coordinates: z.object({
                    lat: z.string().describe('Latitude coordinate'),
                    lng: z.string().describe('Longitude coordinate'),
                  }),
                }),
              ),
              highlights: z
                .array(z.string())
                .describe('Highlights of the route'),
              tips: z.array(z.string()).describe('Tips for the route'),
              bestTimeToRide: z
                .string()
                .describe('Best time to ride this route'),
              bikeRentalOptions: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the bike rental place'),
                    location: z
                      .string()
                      .describe('Location of the bike rental'),
                    priceRange: z
                      .string()
                      .describe('Price range for bike rental'),
                    bikeTypes: z
                      .array(z.string())
                      .describe('Types of bikes available'),
                    website: z
                      .string()
                      .optional()
                      .describe('Website of the bike rental place'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .optional()
                .describe('Bike rental options'),
              imageKeywords: z
                .array(z.string())
                .describe('Keywords for images of this route'),
            }),
          ),
        }),
        temperature: 0.7,
      });

      return cyclingData.routes;
    } catch (error) {
      console.error('Error getting cycling routes:', error);
      return [];
    }
  }

  /**
   * Get hiking trails
   */
  private async getHikingTrails(
    _destinationInfo: DestinationInfo,
    _userPreferences: UserPreferences,
    _searchResults: any,
  ): Promise<HikingTrail[]> {
    // Implementation similar to getCyclingRoutes but for hiking trails
    return [];
  }

  /**
   * Get beach activities
   */
  private async getBeachActivities(
    _destinationInfo: DestinationInfo,
    _userPreferences: UserPreferences,
    _searchResults: any,
  ): Promise<BeachActivity> {
    // Implementation similar to getCyclingRoutes but for beach activities
    return {
      beaches: [],
      waterActivities: [],
      tips: [],
      imageKeywords: [],
    };
  }

  /**
   * Get shopping information
   */
  private async getShoppingInfo(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
    searchResults: any,
  ): Promise<ShoppingInfo> {
    try {
      console.log(
        'Getting shopping information for:',
        destinationInfo.destination,
        userPreferences.budget,
      );

      // Skip web search and use mock data
      const combinedResults = searchResults;

      // Generate shopping information using the model
      const { object: shoppingData } = await generateObject({
        model: this.model,
        system: `You are an expert shopping guide who specializes in creating detailed shopping information.
        Create detailed shopping information for the destination based on the search results.
        Include accurate information about shopping areas, malls, boutiques, markets, and department stores.
        Provide specific locations with coordinates, and include highlights and tips.
        Consider the user's preferences and budget when recommending shopping options.`,
        prompt: `Create detailed shopping information for ${destinationInfo.destination}, ${destinationInfo.country}
        based on these user preferences:

        ${JSON.stringify(userPreferences, null, 2)}

        Use the following search results to inform your recommendations:
        ${JSON.stringify(combinedResults, null, 2)}

        Include specific details about:
        - Shopping areas (malls, streets, markets, etc.)
        - Notable shops and boutiques
        - Price ranges and specialties
        - Best times to shop
        - Shopping tips
        - Tax refund information if applicable`,
        schema: z.object({
          areas: z.array(
            z.object({
              name: z.string().describe('Name of the shopping area'),
              description: z.string().describe('Description of the area'),
              type: z
                .enum([
                  'mall',
                  'street',
                  'market',
                  'department-store',
                  'boutique-district',
                  'outlet',
                ])
                .describe('Type of shopping area'),
              highlights: z
                .array(z.string())
                .describe('Highlights of the area'),
              bestFor: z
                .array(z.string())
                .describe('What this area is best for'),
              priceRange: z.string().describe('Price range of the area'),
              openingHours: z.string().describe('Opening hours information'),
              coordinates: z.object({
                lat: z.string().describe('Latitude coordinate'),
                lng: z.string().describe('Longitude coordinate'),
              }),
            }),
          ),
          shops: z.array(
            z.object({
              name: z.string().describe('Name of the shop'),
              description: z.string().describe('Description of the shop'),
              category: z.string().describe('Category of the shop'),
              specialties: z
                .array(z.string())
                .describe('Specialties of the shop'),
              priceRange: z.string().describe('Price range of the shop'),
              location: z.string().describe('Location description'),
              coordinates: z.object({
                lat: z.string().describe('Latitude coordinate'),
                lng: z.string().describe('Longitude coordinate'),
              }),
            }),
          ),
          tips: z.array(z.string()).describe('Shopping tips'),
          bestTimeToShop: z.string().describe('Best time to shop'),
          taxRefundInfo: z
            .string()
            .optional()
            .describe('Tax refund information if applicable'),
          imageKeywords: z
            .array(z.string())
            .describe('Keywords for images related to shopping'),
        }),
        temperature: 0.7,
      });

      return shoppingData;
    } catch (error) {
      console.error('Error getting shopping information:', error);

      // Return basic shopping info if generation fails
      return {
        areas: [
          {
            name: `Main Shopping Area in ${destinationInfo.destination}`,
            description:
              'The main shopping area with various shops and boutiques.',
            type: 'street',
            highlights: ['Various shops', 'Local boutiques'],
            bestFor: ['General shopping', 'Souvenirs'],
            priceRange: 'Moderate',
            openingHours: 'Typically 10:00-19:00, may vary by shop',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
        ],
        shops: [
          {
            name: 'Local Boutique',
            description: 'A local boutique offering unique items.',
            category: 'Fashion',
            specialties: ['Local designs', 'Unique items'],
            priceRange: 'Moderate to High',
            location: 'City Center',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
        ],
        tips: [
          'Most shops are closed on Sundays',
          'Bargaining is not common in department stores but may be acceptable in markets',
          'Look for tax-free shopping options if you are a tourist',
        ],
        bestTimeToShop: 'Weekday mornings tend to be less crowded',
        imageKeywords: [
          'shopping',
          destinationInfo.destination,
          'boutique',
          'market',
        ],
      };
    }
  }

  /**
   * Get custom sections based on trip type
   */
  private async getCustomSections(
    _destinationInfo: DestinationInfo,
    tripType: TripType,
    _searchResults: any,
  ): Promise<
    {
      title: string;
      content: string;
      type: 'text' | 'list' | 'table' | 'chart';
    }[]
  > {
    try {
      // Generate custom sections based on trip type
      const customSections: {
        title: string;
        content: string;
        type: 'text' | 'list' | 'table' | 'chart';
      }[] = [];

      switch (tripType) {
        case TripType.CYCLING:
          customSections.push(
            {
              title: 'Cycling Season',
              content:
                'Information about the best cycling season in the destination.',
              type: 'text',
            },
            {
              title: 'Local Cycling Events',
              content:
                'Information about local cycling events in the destination.',
              type: 'list',
            },
            {
              title: 'Cycling Gear Checklist',
              content: 'Checklist of cycling gear to bring for the trip.',
              type: 'list',
            },
          );
          break;

        case TripType.HIKING:
          customSections.push(
            {
              title: 'Hiking Season',
              content:
                'Information about the best hiking season in the destination.',
              type: 'text',
            },
            {
              title: 'Trail Difficulty Guide',
              content: 'Guide to understanding trail difficulty ratings.',
              type: 'table',
            },
            {
              title: 'Hiking Gear Checklist',
              content: 'Checklist of hiking gear to bring for the trip.',
              type: 'list',
            },
          );
          break;

        case TripType.BEACH:
          customSections.push(
            {
              title: 'Beach Season',
              content:
                'Information about the best beach season in the destination.',
              type: 'text',
            },
            {
              title: 'Beach Safety Tips',
              content: 'Tips for staying safe at the beach.',
              type: 'list',
            },
            {
              title: 'Beach Gear Checklist',
              content: 'Checklist of beach gear to bring for the trip.',
              type: 'list',
            },
          );
          break;

        default:
          // No custom sections for other trip types
          break;
      }

      return customSections;
    } catch (error) {
      console.error('Error getting custom sections:', error);
      return [];
    }
  }
}
