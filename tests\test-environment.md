# Test Environment Configuration

This document describes how to set up a dedicated test environment for running the AI Chatbot tests.

## Overview

A dedicated test environment helps ensure that tests are reliable and don't interfere with development or production environments. It also allows for predefined test data and controlled conditions.

## Environment Setup

### 1. Create a Test Database

Create a separate database for testing:

```bash
# Example for PostgreSQL
createdb ai_chatbot_test
```

### 2. Configure Environment Variables

Create a `.env.test` file with test-specific environment variables:

```
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/ai_chatbot_test

# Authentication
AUTH_SECRET=test-auth-secret
AUTH_URL=http://localhost:3000

# AI Models
AI_PROVIDER=mock
OPENAI_API_KEY=mock-key
ANTHROPIC_API_KEY=mock-key

# Redis
REDIS_KV_URL=redis://localhost:6379

# Test User
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=TestPassword123!
```

### 3. Set Up Test Data

Create a script to seed the test database with predefined data:

```typescript
// scripts/seed-test-db.ts
import { db } from '@/lib/db';
import { chats, messages, documents } from '@/lib/db/schema';

async function seedTestDatabase() {
  // Clear existing data
  await db.delete(messages);
  await db.delete(chats);
  await db.delete(documents);

  // Create test user
  const testUser = {
    id: 'test-user-id',
    email: process.env.TEST_USER_EMAIL || '<EMAIL>',
    // Add other user fields as needed
  };

  // Create test chats
  const testChats = [
    {
      id: 'test-chat-1',
      userId: testUser.id,
      title: 'Test Chat 1',
      createdAt: new Date(),
      updatedAt: new Date(),
      visibilityType: 'private',
    },
    {
      id: 'test-chat-2',
      userId: testUser.id,
      title: 'Test Chat 2',
      createdAt: new Date(),
      updatedAt: new Date(),
      visibilityType: 'public',
    },
  ];

  // Create test messages
  const testMessages = [
    {
      id: 'test-message-1',
      chatId: 'test-chat-1',
      role: 'user',
      content: 'Hello, this is a test message',
      createdAt: new Date(),
    },
    {
      id: 'test-message-2',
      chatId: 'test-chat-1',
      role: 'assistant',
      content: 'Hello! I am an AI assistant. How can I help you today?',
      createdAt: new Date(),
    },
  ];

  // Create test documents
  const testDocuments = [
    {
      id: 'test-document-1',
      userId: testUser.id,
      title: 'Test Document 1',
      content: 'This is a test document',
      kind: 'text',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  // Insert test data
  await db.insert(chats).values(testChats);
  await db.insert(messages).values(testMessages);
  await db.insert(documents).values(testDocuments);

  console.log('Test database seeded successfully');
}

seedTestDatabase().catch(console.error);
```

### 4. Mock External Services

Create mock implementations of external services:

```typescript
// tests/mocks/ai-provider.ts
export const mockAIProvider = {
  generateText: async (prompt: string) => {
    // Return predefined responses based on the prompt
    if (prompt.includes('weather')) {
      return 'The weather in San Francisco is currently 65°F and sunny.';
    }
    if (prompt.includes('quantum')) {
      return 'Quantum computing is a type of computing that uses quantum-mechanical phenomena, such as superposition and entanglement, to perform operations on data...';
    }
    return 'I am a mock AI assistant. This is a predefined response for testing purposes.';
  },
};
```

## Running Tests in the Dedicated Environment

### 1. Start the Test Environment

```bash
# Start the test database
docker-compose -f docker-compose.test.yml up -d

# Seed the test database
pnpm run seed-test-db
```

### 2. Run Tests with Test Environment

```bash
# Set the environment to test
NODE_ENV=test pnpm test
```

## CI/CD Integration

### GitHub Actions Configuration

Add a workflow for running tests in the dedicated test environment:

```yaml
# .github/workflows/test.yml
name: Test

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: ai_chatbot_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:6
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Set up test environment
        run: |
          cp .env.example .env.test
          sed -i 's/DATABASE_URL=.*/DATABASE_URL=postgresql:\/\/postgres:postgres@localhost:5432\/ai_chatbot_test/' .env.test
          sed -i 's/REDIS_KV_URL=.*/REDIS_KV_URL=redis:\/\/localhost:6379/' .env.test
          sed -i 's/AI_PROVIDER=.*/AI_PROVIDER=mock/' .env.test

      - name: Seed test database
        run: NODE_ENV=test pnpm run seed-test-db

      - name: Run tests
        run: NODE_ENV=test pnpm test
```

## Best Practices

1. **Isolate the test environment** from development and production environments
2. **Use mock implementations** for external services to avoid dependencies
3. **Seed the database with predefined data** to ensure consistent test conditions
4. **Reset the database before each test run** to ensure a clean state
5. **Use environment variables** to configure the test environment
6. **Document the test environment setup** to make it easy for others to reproduce
