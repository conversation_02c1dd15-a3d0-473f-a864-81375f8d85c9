'use client';

import { useEffect, useState } from 'react';
import type { JSONValue } from 'ai';

interface Annotation {
  id: string;
  type: 'search_query' | 'source' | 'content' | 'status';
  query?: string;
  queryId?: string;
  source?: {
    url: string;
    title?: string;
  };
  content?: {
    title?: string;
    text?: string;
    url?: string;
  };
  status?: {
    title: string;
  };
  plan?: any[];
  createdAt: string;
}

interface UseStreamingAnnotationsProps {
  toolCallId: string;
  enabled?: boolean;
  externalAnnotations?: JSONValue[]; // Annotations passées depuis le parent
}

interface UseStreamingAnnotationsResult {
  annotations: Annotation[];
  isLoading: boolean;
  error: string | null;
}

/**
 * Hook to process streaming annotations for a given tool call.
 * Les annotations arrivent via le streaming du serveur et sont passées
 * directement via la prop externalAnnotations.
 */
export function useStreamingAnnotations({
  toolCallId,
  enabled = true,
  externalAnnotations = [],
}: UseStreamingAnnotationsProps): UseStreamingAnnotationsResult {
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!enabled || !externalAnnotations) {
      return;
    }

    try {
      // Traiter les annotations externes qui arrivent en temps réel
      const processedAnnotations: Annotation[] = externalAnnotations
        .map((annotation: any, index: number): Annotation | null => {
          if (!annotation || typeof annotation !== 'object') {
            return null;
          }

          // Générer un ID unique si pas présent
          const id = annotation.id || `${toolCallId}-${index}-${Date.now()}`;
          const createdAt = annotation.createdAt || new Date().toISOString();

          // Traiter les différents types d'annotations
          if (annotation.type === 'source' && annotation.source) {
            return {
              id,
              type: 'source' as const,
              queryId: annotation.queryId,
              source: {
                url: annotation.source.url,
                title: annotation.source.title,
              },
              createdAt,
            };
          }

          if (annotation.type === 'content' && annotation.content) {
            return {
              id,
              type: 'content' as const,
              queryId: annotation.queryId,
              content: {
                title: annotation.content.title,
                text: annotation.content.text,
                url: annotation.content.url,
              },
              createdAt,
            };
          }

          if (annotation.status) {
            return {
              id,
              type: 'status' as const,
              status: {
                title: annotation.status.title,
              },
              plan: annotation.plan,
              createdAt,
            };
          }

          // Fallback pour les annotations de recherche
          if (annotation.query || annotation.type === 'search_query') {
            return {
              id,
              type: 'search_query' as const,
              query: annotation.query,
              queryId: annotation.queryId,
              createdAt,
            };
          }

          return null;
        })
        .filter((annotation): annotation is Annotation => annotation !== null);

      setAnnotations(processedAnnotations);
      setIsLoading(false);
      setError(null);

      console.log('🔥 STREAMING ANNOTATIONS - Processed:', {
        toolCallId,
        externalCount: externalAnnotations.length,
        processedCount: processedAnnotations.length,
        annotations: processedAnnotations,
      });
    } catch (err) {
      console.error('Failed to process annotations:', err);
      setError('Failed to process research data');
      setIsLoading(false);
    }
  }, [toolCallId, enabled, externalAnnotations]);

  return {
    annotations,
    isLoading,
    error,
  };
}
