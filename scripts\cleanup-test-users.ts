import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import {
  user,
  document,
  chat,
  message,
  suggestion,
  vote,
} from '../lib/db/schema';
import { like, inArray } from 'drizzle-orm';
import { config } from 'dotenv';

// Charger les variables d'environnement
config();

async function cleanupTestUsers() {
  console.log('Starting cleanup of test users...');

  try {
    // Vérifier que l'URL de la base de données est définie
    if (!process.env.POSTGRES_URL) {
      throw new Error('POSTGRES_URL environment variable is not defined');
    }

    // Créer une connexion à la base de données
    const client = postgres(process.env.POSTGRES_URL);
    const db = drizzle(client);

    // Trouver d'abord les utilisateurs de test
    const testUsers = await db
      .select({ id: user.id, email: user.email })
      .from(user)
      .where(like(user.email, '%@playwright.com'));

    console.log(`Found ${testUsers.length} test users to clean up`);

    if (testUsers.length > 0) {
      const userIds = testUsers.map((u) => u.id);

      // Afficher les IDs des utilisateurs pour le débogage
      console.log('User IDs to clean up:', userIds);

      // 1. Supprimer les suggestions
      const deletedSuggestions = await db
        .delete(suggestion)
        .where(inArray(suggestion.userId, userIds))
        .returning({ id: suggestion.id });
      console.log(`Deleted ${deletedSuggestions.length} suggestions`);

      // 2. Trouver les chats des utilisateurs
      const userChats = await db
        .select({ id: chat.id })
        .from(chat)
        .where(inArray(chat.userId, userIds));

      const chatIds = userChats.map((c) => c.id);
      console.log(`Found ${chatIds.length} chats to clean up`);

      // 3. Supprimer les votes liés aux chats des utilisateurs
      if (chatIds.length > 0) {
        const deletedVotes = await db
          .delete(vote)
          .where(inArray(vote.chatId, chatIds))
          .returning({ chatId: vote.chatId });
        console.log(`Deleted ${deletedVotes.length} votes`);

        // 4. Supprimer les messages des chats
        const deletedMessages = await db
          .delete(message)
          .where(inArray(message.chatId, chatIds))
          .returning({ id: message.id });
        console.log(`Deleted ${deletedMessages.length} messages`);
      }

      // 5. Vérifier s'il reste des documents liés aux utilisateurs
      const remainingDocs = await db
        .select({ id: document.id, userId: document.userId })
        .from(document)
        .where(inArray(document.userId, userIds));

      console.log(`Found ${remainingDocs.length} documents to delete`);

      // 6. Supprimer les documents
      if (remainingDocs.length > 0) {
        const deletedDocs = await db
          .delete(document)
          .where(inArray(document.userId, userIds))
          .returning({ id: document.id });
        console.log(`Deleted ${deletedDocs.length} documents`);
      }

      // 7. Supprimer les chats
      const deletedChats = await db
        .delete(chat)
        .where(inArray(chat.userId, userIds))
        .returning({ id: chat.id });
      console.log(`Deleted ${deletedChats.length} chats`);

      // 8. Vérifier s'il reste des entités liées aux utilisateurs
      const remainingDocs2 = await db
        .select({ id: document.id })
        .from(document)
        .where(inArray(document.userId, userIds));

      if (remainingDocs2.length > 0) {
        console.log(
          `WARNING: Still found ${remainingDocs2.length} documents linked to users`,
        );
        console.log(
          'Document IDs:',
          remainingDocs2.map((d) => d.id),
        );
      }
    }

    // 9. Maintenant supprimer les utilisateurs
    const result = await db
      .delete(user)
      .where(like(user.email, '%@playwright.com'))
      .returning({ id: user.id, email: user.email });

    console.log(`Successfully deleted ${result.length} test users:`);
    result.forEach((u) => console.log(`- ${u.email} (${u.id})`));

    // Fermer la connexion à la base de données
    await client.end();
  } catch (error) {
    console.error('Error cleaning up test users:', error);
    process.exit(1);
  }

  console.log('Cleanup completed successfully');
}

cleanupTestUsers();
