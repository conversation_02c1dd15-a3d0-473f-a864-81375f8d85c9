import { generateDummyPassword } from '@/lib/db/utils';
// Détermine si l'application s'exécute en environnement de production
export const isProductionEnvironment = process.env.NODE_ENV === 'production';
export const isDevelopmentEnvironment = process.env.NODE_ENV === 'development';
// Détermine si l'application s'exécute en environnement de test
// Cette variable sera true dans les cas suivants:
// 1. FORCE_TEST_MODE=true - Pour forcer manuellement le mode test
// 2. PLAYWRIGHT_TEST_BASE_URL est défini - Indique des tests Playwright
// 3. PLAYWRIGHT est défini - Variable utilisée dans les GitHub Actions
// 4. CI_PLAYWRIGHT est défini - Autre variable pour CI/CD
export const isTestEnvironment = Boolean(
  process.env.FORCE_TEST_MODE === 'true' ||
    process.env.PLAYWRIGHT_TEST_BASE_URL ||
    process.env.PLAYWRIGHT ||
    process.env.CI_PLAYWRIGHT,
);

export const guestRegex = /^guest-\d+$/;

export const DUMMY_PASSWORD = generateDummyPassword();

// Search limits for free users
export const SEARCH_LIMITS = {
  DAILY_SEARCH_LIMIT: 10, // Daily searches for free users
  EXTREME_SEARCH_LIMIT: 5, // Monthly extreme searches for free users
} as const;

export const PRICING = {
  PRO_MONTHLY: 15, // Pro plan monthly price in USD
} as const;

export const SNAPSHOT_NAME = 'scira-analysis:1752127473';
