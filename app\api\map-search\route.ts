import { NextResponse } from 'next/server';
import { serperMapSearch } from '@/lib/utils';

export async function GET(request: Request) {
  console.log('Map search API called');

  const { searchParams } = new URL(request.url);
  const query = searchParams.get('query');

  console.log('Map search query:', query);

  if (!query) {
    console.log('Map search API: No query parameter provided');
    return NextResponse.json(
      { error: 'Query parameter is required' },
      { status: 400 },
    );
  }

  try {
    console.log('Map search API: Calling serperMapSearch with query:', query);
    const places = await serperMapSearch(query);

    console.log(`Map search API: Found ${places.length} places`);
    if (places.length > 0) {
      console.log('Map search API: First place:', places[0]);
    } else {
      console.log('Map search API: No places found');
    }

    return NextResponse.json({ places });
  } catch (error) {
    console.error('Error in map search API:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch map results',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
