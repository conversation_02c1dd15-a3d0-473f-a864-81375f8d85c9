import { create } from 'zustand';

export interface Source {
  url: string;
  title: string;
  content?: string;
}

interface SourcesPanelState {
  isOpen: boolean;
  sources: Source[];
  setIsOpen: (isOpen: boolean) => void;
  togglePanel: () => void;
  setSources: (sources: Source[]) => void;
  clearSources: () => void;
}

export const useSourcesPanelStore = create<SourcesPanelState>((set) => ({
  isOpen: false,
  sources: [],
  setIsOpen: (isOpen) => set({ isOpen }),
  togglePanel: () => set((state) => ({ isOpen: !state.isOpen })),
  setSources: (sources) => set({ sources }),
  clearSources: () => set({ sources: [] }),
}));
