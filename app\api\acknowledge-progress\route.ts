import { NextRequest, NextResponse } from 'next/server';
import { acknowledgeProgress } from '@/lib/ai/tools/extreme-search';

export async function POST(request: NextRequest) {
  try {
    const { progressId } = await request.json();
    
    if (!progressId || typeof progressId !== 'string') {
      return NextResponse.json(
        { error: 'Invalid progressId' },
        { status: 400 }
      );
    }

    // Call the acknowledge function from extreme-search
    acknowledgeProgress(progressId);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error acknowledging progress:', error);
    return NextResponse.json(
      { error: 'Failed to acknowledge progress' },
      { status: 500 }
    );
  }
}
