import React from 'react';

// Fonction simple pour détecter et transformer les URLs en liens
function autoLinkUrls(text: string) {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  return text.replace(
    urlRegex,
    '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>',
  );
}

export function ArtifactContent({ content }: { content: string }) {
  // Transformer le contenu pour rendre les URLs cliquables
  const linkedContent = autoLinkUrls(content);

  return (
    <div
      className="artifact-content"
      dangerouslySetInnerHTML={{ __html: linkedContent }}
    />
  );
}
