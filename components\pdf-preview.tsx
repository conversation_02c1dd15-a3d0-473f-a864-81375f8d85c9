'use client';
import { useState } from 'react';
import { LoaderIcon } from './icons';

interface PdfPreviewProps {
  url: string;
  className?: string;
}

export function PdfPreview({ url, className = '' }: PdfPreviewProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setError("Impossible de charger l'aperçu PDF");
    setIsLoading(false);
  };

  // Ajout du paramètre view=FitH pour que le PDF s'adapte à la largeur
  const pdfUrlWithParams = `${url}#view=FitH&toolbar=0&navpanes=0`;

  return (
    <div className={`relative size-full ${className}`}>
      <iframe
        src={pdfUrlWithParams}
        className="size-full"
        onLoad={handleLoad}
        onError={handleError}
        title="Aperçu PDF"
        style={{ display: 'block' }} // Assure que l'iframe utilise tout l'espace disponible
      />

      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/10">
          <div className="animate-spin">
            <LoaderIcon size={24} />
          </div>
        </div>
      )}

      {error && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-xs text-red-500">{error}</div>
        </div>
      )}
    </div>
  );
}
