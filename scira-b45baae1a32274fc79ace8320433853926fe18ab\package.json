{"name": "scira", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "next lint", "knip": "knip"}, "dependencies": {"@ai-sdk/anthropic": "1.2.12", "@ai-sdk/elevenlabs": "0.0.3", "@ai-sdk/google": "1.2.22", "@ai-sdk/groq": "1.2.9", "@ai-sdk/mistral": "1.2.8", "@ai-sdk/openai": "1.3.23", "@ai-sdk/react": "1.2.12", "@ai-sdk/ui-utils": "1.2.11", "@ai-sdk/xai": "1.2.18", "@aws-sdk/client-s3": "^3.835.0", "@aws-sdk/lib-storage": "^3.842.0", "@babel/runtime": ">=7.27.0", "@daytonaio/sdk": "^0.24.5", "@dodopayments/better-auth": "^1.1.1", "@dodopayments/core": "^0.1.14", "@eslint/plugin-kit": "0.3.4", "@foobar404/wave": "^2.0.5", "@hookform/resolvers": "^5.1.1", "@hugeicons/core-free-icons": "^1.0.16", "@hugeicons/react": "^1.0.5", "@neondatabase/serverless": "^1.0.1", "@phosphor-icons/react": "^2.1.10", "@polar-sh/better-auth": "^1.0.8", "@polar-sh/sdk": "^0.34.9", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.3.2", "@react-spring/web": "^10.0.1", "@t3-oss/env-nextjs": "^0.13.8", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.84.1", "@tavily/core": "^0.5.10", "@upstash/qstash": "^2.8.2", "@upstash/redis": "^1.35.3", "@vercel/analytics": "^1.5.0", "@vercel/blob": "^1.1.1", "@vercel/edge-config": "^1.4.0", "@vercel/functions": "^2.2.5", "@vercel/speed-insights": "^1.2.0", "ai": "4.3.19", "axios": "^1.10.0", "better-auth": "https://pkg.pr.new/better-auth/better-auth@8e825ad", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cron-parser": "^5.3.0", "date-fns": "^4.1.0", "dodopayments": "1.43.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.3", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "embla-carousel": "8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "exa-js": "^1.8.26", "framer-motion": "^12.23.9", "geist": "^1.4.2", "he": "^1.2.0", "highlight.js": "^11.11.1", "katex": "^0.16.22", "lisere": "^0.1.1", "lucide-react": "^0.525.0", "luxon": "^3.7.1", "mapbox-gl": "^3.13.0", "marked-react": "^3.0.1", "mem0ai": "^2.1.36", "motion": "^12.23.9", "next": "15.4.5", "next-themes": "0.4.6", "nuqs": "^2.4.3", "postgres": "^3.4.7", "posthog-js": "^1.258.2", "prettier": "^3.6.2", "prismjs": "^1.30.0", "react": "^19", "react-day-picker": "^9.8.1", "react-dom": "^19", "react-hook-form": "^7.61.1", "react-katex": "^3.1.0", "react-latex-next": "^3.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "react-tweet": "^3.2.2", "react-use-measure": "^2.1.7", "recharts": "^2.15.3", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "resend": "^4.7.0", "resumable-stream": "^2.2.1", "server-only": "^0.0.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwind-scrollbar": "4.0.2", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.74"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/canvas-confetti": "^1.9.0", "@types/google.maps": "^3.58.1", "@types/he": "^1.2.3", "@types/katex": "^0.16.7", "@types/lodash": "^4.17.16", "@types/luxon": "^3.6.2", "@types/mapbox-gl": "^3.4.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-katex": "^3.0.4", "@types/react-syntax-highlighter": "^15.5.13", "@types/unist": "^3.0.3", "drizzle-kit": "^0.31.4", "eslint": "^9.32.0", "eslint-config-next": "15.4.5", "postcss": ">=8.5.5", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5"}, "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide", "@vercel/speed-insights", "core-js", "esbuild", "sharp", "sqlite3", "unrs-resolver"]}, "overrides": {"react-is": "^19.0.0-rc-69d4b800-20241021"}, "packageManager": "pnpm@10.12.4"}