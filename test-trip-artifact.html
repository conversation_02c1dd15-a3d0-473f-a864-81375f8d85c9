<!DOCTYPE html>
<html lang="fr">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Voyage à Paris - 5 jours</title>
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
  <style>
    /* Carousel CSS from carousel-templates.ts */
    .restaurant-carousel-wrapper,
    .hotel-carousel-wrapper,
    .poi-carousel-wrapper {
      position: relative;
      overflow: hidden;
      margin: 20px 0;
    }

    .restaurant-carousel-track,
    .hotel-carousel-track,
    .poi-carousel-track {
      display: flex;
      transition: transform 0.3s ease;
    }

    .restaurant-slide,
    .hotel-slide,
    .poi-slide {
      flex: 0 0 100%;
      padding: 0 10px;
      box-sizing: border-box;
    }

    .restaurant-carousel-prev,
    .restaurant-carousel-next,
    .hotel-carousel-prev,
    .hotel-carousel-next,
    .poi-carousel-prev,
    .poi-carousel-next {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(0, 0, 0, 0.7);
      color: white;
      border: none;
      padding: 12px 16px;
      cursor: pointer;
      font-size: 20px;
      z-index: 10;
      border-radius: 6px;
      transition: background-color 0.3s ease;
    }

    .restaurant-carousel-prev:hover,
    .restaurant-carousel-next:hover,
    .hotel-carousel-prev:hover,
    .hotel-carousel-next:hover,
    .poi-carousel-prev:hover,
    .poi-carousel-next:hover {
      background: rgba(0, 0, 0, 0.9);
    }

    .restaurant-carousel-prev,
    .hotel-carousel-prev,
    .poi-carousel-prev {
      left: 10px;
    }

    .restaurant-carousel-next,
    .hotel-carousel-next,
    .poi-carousel-next {
      right: 10px;
    }

    .restaurant-carousel-indicators,
    .hotel-carousel-indicators,
    .poi-carousel-indicators {
      display: flex;
      justify-content: center;
      margin-top: 15px;
      gap: 8px;
    }

    .restaurant-carousel-indicator,
    .hotel-carousel-indicator,
    .poi-carousel-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      border: none;
      background: #ccc;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    .restaurant-carousel-indicator:hover,
    .hotel-carousel-indicator:hover,
    .poi-carousel-indicator:hover {
      background: #999;
    }

    .restaurant-carousel-indicator.active,
    .hotel-carousel-indicator.active,
    .poi-carousel-indicator.active {
      background: #007bff;
    }

    .restaurant-slide .card,
    .hotel-slide .card,
    .poi-slide .card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .restaurant-slide .card:hover,
    .hotel-slide .card:hover,
    .poi-slide .card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    }

    .restaurant-slide .card img,
    .hotel-slide .card img,
    .poi-slide .card img {
      width: 100%;
      height: 200px;
      object-fit: cover;
    }

    .restaurant-slide .card .card-content,
    .hotel-slide .card .card-content,
    .poi-slide .card .card-content {
      padding: 20px;
    }

    .restaurant-slide .card h3,
    .hotel-slide .card h3,
    .poi-slide .card h3 {
      margin: 0 0 10px 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #333;
    }

    .restaurant-slide .card p,
    .hotel-slide .card p,
    .poi-slide .card p {
      margin: 0 0 10px 0;
      color: #666;
      line-height: 1.5;
    }

    .restaurant-slide .card .rating,
    .hotel-slide .card .rating,
    .poi-slide .card .rating {
      color: #f39c12;
      font-weight: 500;
    }

    .restaurant-slide .card .price,
    .hotel-slide .card .price,
    .poi-slide .card .price {
      color: #27ae60;
      font-weight: 600;
      font-size: 1.1rem;
    }

    /* General styles */
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f5f5f5;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .section {
      margin: 30px 0;
    }

    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 20px;
    }

    h2 {
      color: #444;
      border-bottom: 2px solid #007bff;
      padding-bottom: 10px;
    }

    .rating {
      color: #f39c12;
      margin: 8px 0;
      font-weight: 500;
    }
  </style>
</head>

<body>
  <div class="container">
    <h1>🌍 Voyage à Paris</h1>
    <p style="text-align: center; color: #666; font-size: 1.1rem;">Durée: 5 jours • France</p>

    <div class="section">
      <h2>🏨 Hébergements & Accommodations</h2>
      <div id="accommodation-carousel" class="hotel-carousel">
        <div class="hotel-slide">
          <div class="card">
            <img src="https://source.unsplash.com/400x200/?luxury,hotel,paris,boutique"
              alt="Hotel des Grands Boulevards">
            <div class="card-content">
              <h3>🏨 Hotel des Grands Boulevards</h3>
              <div class="type-badge"
                style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem; display: inline-block; margin-bottom: 8px;">
                Boutique Hotel</div>
              <p>Hôtel boutique élégant au cœur de Paris, alliant charme historique et confort moderne dans le 2e
                arrondissement.</p>
              <div class="location" style="color: #666; font-size: 0.9rem; margin: 4px 0;">📍 2e arrondissement, Paris
              </div>
              <div class="rating" style="color: #f39c12; margin: 8px 0;">⭐ 4.5/5 (324 avis)</div>
              <div class="amenities" style="color: #666; font-size: 0.9rem; margin: 8px 0;">🏨 WiFi gratuit, Spa,
                Restaurant</div>
              <div class="price" style="color: #27ae60; font-weight: 600; font-size: 1.1rem;">💰 €180/nuit</div>
            </div>
          </div>
        </div>
        <div class="hotel-slide">
          <div class="card">
            <img src="https://source.unsplash.com/400x200/?palace,hotel,luxury,paris" alt="Le Meurice">
            <div class="card-content">
              <h3>🏨 Le Meurice</h3>
              <div class="type-badge"
                style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem; display: inline-block; margin-bottom: 8px;">
                Palace</div>
              <p>Palace parisien légendaire offrant un service d'exception et des vues imprenables sur les Tuileries.
              </p>
              <div class="location" style="color: #666; font-size: 0.9rem; margin: 4px 0;">📍 1er arrondissement, Paris
              </div>
              <div class="rating" style="color: #f39c12; margin: 8px 0;">⭐ 4.8/5 (892 avis)</div>
              <div class="amenities" style="color: #666; font-size: 0.9rem; margin: 8px 0;">🏨 Spa, Restaurant étoilé,
                Concierge</div>
              <div class="price" style="color: #27ae60; font-weight: 600; font-size: 1.1rem;">💰 €650/nuit</div>
            </div>
          </div>
        </div>
        <div class="hotel-slide">
          <div class="card">
            <img src="https://source.unsplash.com/400x200/?boutique,hotel,modern,paris" alt="Hotel Malte Opera">
            <div class="card-content">
              <h3>🏨 Hotel Malte Opera</h3>
              <div class="type-badge"
                style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem; display: inline-block; margin-bottom: 8px;">
                Hotel 4*</div>
              <p>Hôtel moderne près de l'Opéra, parfait pour découvrir Paris à pied avec un excellent rapport
                qualité-prix.</p>
              <div class="location" style="color: #666; font-size: 0.9rem; margin: 4px 0;">📍 2e arrondissement, Paris
              </div>
              <div class="rating" style="color: #f39c12; margin: 8px 0;">⭐ 4.2/5 (156 avis)</div>
              <div class="amenities" style="color: #666; font-size: 0.9rem; margin: 8px 0;">🏨 WiFi, Bar, Fitness</div>
              <div class="price" style="color: #27ae60; font-weight: 600; font-size: 1.1rem;">💰 €120/nuit</div>
            </div>
          </div>
        </div>
        <div class="hotel-slide">
          <div class="card">
            <img src="https://source.unsplash.com/400x200/?apartment,airbnb,paris" alt="Appartement Marais">
            <div class="card-content">
              <h3>🏨 Appartement Marais</h3>
              <div class="type-badge"
                style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem; display: inline-block; margin-bottom: 8px;">
                Appartement</div>
              <p>Charmant appartement dans le Marais historique, idéal pour une expérience authentiquement parisienne.
              </p>
              <div class="location" style="color: #666; font-size: 0.9rem; margin: 4px 0;">📍 3e arrondissement, Le
                Marais</div>
              <div class="rating" style="color: #f39c12; margin: 8px 0;">⭐ 4.6/5 (89 avis)</div>
              <div class="amenities" style="color: #666; font-size: 0.9rem; margin: 8px 0;">🏨 Cuisine équipée, WiFi,
                Balcon</div>
              <div class="price" style="color: #27ae60; font-weight: 600; font-size: 1.1rem;">💰 €95/nuit</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>📍 Carte</h2>
      <div id="map"
        style="height: 400px; background: #e9ecef; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #666;">
        <p>Carte interactive de Paris (Leaflet sera initialisé ici)</p>
      </div>
    </div>
  </div>

  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
  <script>
    // Carousel JavaScript from carousel-templates.ts
    class HotelCarousel {
      constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        if (!this.container) {
          console.error(`Container with ID "${containerId}" not found.`);
          return;
        }

        this.options = {
          slidesToShow: 1,
          slidesToScroll: 1,
          autoplay: false,
          autoplaySpeed: 5000,
          infinite: true,
          ...options
        };

        this.currentSlide = 0;
        this.totalSlides = 0;
        this.isAnimating = false;
        this.touchStartX = 0;
        this.touchEndX = 0;
        this.autoplayInterval = null;

        this.init();
      }

      init() {
        this.createCarouselStructure();
        this.slides = this.container.querySelectorAll('.hotel-slide');
        this.totalSlides = this.slides.length;

        if (this.totalSlides === 0) {
          console.warn('No hotel slides found');
          return;
        }

        this.updateCarousel();
        this.addEventListeners();

        if (this.options.autoplay) {
          this.startAutoplay();
        }
      }

      createCarouselStructure() {
        const wrapper = document.createElement('div');
        wrapper.className = 'hotel-carousel-wrapper';

        const track = document.createElement('div');
        track.className = 'hotel-carousel-track';

        while (this.container.firstChild) {
          track.appendChild(this.container.firstChild);
        }

        const prevButton = document.createElement('button');
        prevButton.className = 'hotel-carousel-prev';
        prevButton.setAttribute('aria-label', 'Previous accommodation');
        prevButton.innerHTML = '‹';

        const nextButton = document.createElement('button');
        nextButton.className = 'hotel-carousel-next';
        nextButton.setAttribute('aria-label', 'Next accommodation');
        nextButton.innerHTML = '›';

        const indicators = document.createElement('div');
        indicators.className = 'hotel-carousel-indicators';

        wrapper.appendChild(track);
        wrapper.appendChild(prevButton);
        wrapper.appendChild(nextButton);
        wrapper.appendChild(indicators);
        this.container.appendChild(wrapper);

        this.track = track;
        this.prevButton = prevButton;
        this.nextButton = nextButton;
        this.indicators = indicators;
      }

      addEventListeners() {
        this.prevButton.addEventListener('click', () => this.prevSlide());
        this.nextButton.addEventListener('click', () => this.nextSlide());

        this.track.addEventListener('touchstart', (e) => {
          this.touchStartX = e.changedTouches[0].screenX;
        }, { passive: true });

        this.track.addEventListener('touchend', (e) => {
          this.touchEndX = e.changedTouches[0].screenX;
          this.handleSwipe();
        }, { passive: true });

        this.container.addEventListener('keydown', (e) => {
          if (e.key === 'ArrowLeft') {
            this.prevSlide();
          } else if (e.key === 'ArrowRight') {
            this.nextSlide();
          }
        });

        if (this.options.autoplay) {
          this.container.addEventListener('mouseenter', () => this.stopAutoplay());
          this.container.addEventListener('mouseleave', () => this.startAutoplay());
        }

        window.addEventListener('resize', () => {
          this.updateCarousel();
        });
      }

      handleSwipe() {
        const swipeThreshold = 50;
        const diff = this.touchStartX - this.touchEndX;

        if (diff > swipeThreshold) {
          this.nextSlide();
        } else if (diff < -swipeThreshold) {
          this.prevSlide();
        }
      }

      prevSlide() {
        if (this.isAnimating) return;

        this.isAnimating = true;
        this.currentSlide = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
        this.updateCarousel();

        setTimeout(() => {
          this.isAnimating = false;
        }, 300);
      }

      nextSlide() {
        if (this.isAnimating) return;

        this.isAnimating = true;
        this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
        this.updateCarousel();

        setTimeout(() => {
          this.isAnimating = false;
        }, 300);
      }

      updateCarousel() {
        if (!this.slides || this.slides.length === 0) return;

        const slideWidth = this.slides[0].offsetWidth;
        this.track.style.transform = `translateX(-${this.currentSlide * slideWidth}px)`;

        this.slides.forEach((slide, index) => {
          if (index === this.currentSlide) {
            slide.classList.add('active');
            slide.setAttribute('aria-hidden', 'false');
          } else {
            slide.classList.remove('active');
            slide.setAttribute('aria-hidden', 'true');
          }
        });

        this.updateIndicators();

        if (!this.options.infinite) {
          this.prevButton.disabled = this.currentSlide === 0;
          this.nextButton.disabled = this.currentSlide === this.totalSlides - 1;
        }
      }

      updateIndicators() {
        this.indicators.innerHTML = '';

        for (let i = 0; i < this.totalSlides; i++) {
          const indicator = document.createElement('button');
          indicator.className = 'hotel-carousel-indicator';
          indicator.setAttribute('aria-label', `Go to accommodation ${i + 1}`);

          if (i === this.currentSlide) {
            indicator.classList.add('active');
            indicator.setAttribute('aria-current', 'true');
          }

          indicator.addEventListener('click', () => {
            this.goToSlide(i);
          });

          this.indicators.appendChild(indicator);
        }
      }

      goToSlide(index) {
        if (this.isAnimating || index === this.currentSlide) return;

        this.isAnimating = true;
        this.currentSlide = index;
        this.updateCarousel();

        setTimeout(() => {
          this.isAnimating = false;
        }, 300);
      }

      startAutoplay() {
        if (!this.options.autoplay) return;

        this.autoplayInterval = setInterval(() => {
          this.nextSlide();
        }, this.options.autoplaySpeed);
      }

      stopAutoplay() {
        if (this.autoplayInterval) {
          clearInterval(this.autoplayInterval);
          this.autoplayInterval = null;
        }
      }
    }

    // Initialize carousel when DOM is ready
    document.addEventListener('DOMContentLoaded', function () {
      console.log('🏨 Initializing accommodation carousel...');

      if (document.getElementById('accommodation-carousel')) {
        console.log('✅ Accommodation carousel found, initializing...');
        new HotelCarousel('accommodation-carousel', {
          autoplay: false,
          infinite: true
        });
      } else {
        console.warn('❌ Accommodation carousel not found');
      }

      // Initialize map
      if (document.getElementById('map')) {
        const map = L.map('map').setView([48.8566, 2.3522], 12);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        // Add markers for accommodations
        L.marker([48.8708, 2.3387]).addTo(map).bindPopup('Hotel des Grands Boulevards');
        L.marker([48.8656, 2.3281]).addTo(map).bindPopup('Le Meurice');
        L.marker([48.8708, 2.3387]).addTo(map).bindPopup('Hotel Malte Opera');
        L.marker([48.8584, 2.3639]).addTo(map).bindPopup('Appartement Marais');
      }
    });
  </script>
</body>

</html>