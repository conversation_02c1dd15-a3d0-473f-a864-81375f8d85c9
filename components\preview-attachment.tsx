'use client';

import { useState } from 'react';
import type { Attachment } from 'ai';
import { FileIcon, LoaderIcon, FullscreenIcon, TrashIcon } from './icons';
import { PdfPreview } from './pdf-preview';
import Image from 'next/image';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogTitle,
} from '@/components/ui/dialog';

export const PreviewAttachment = ({
  attachment,
  isUploading = false,
  onRemove,
}: {
  attachment: Attachment;
  isUploading?: boolean;
  onRemove?: () => void;
}) => {
  const { name, url, contentType } = attachment;
  const displayName = name || 'Attachment';
  const fileType = contentType ? contentType.split('/')[1] : 'unknown';
  const [isOpen, setIsOpen] = useState(false);

  const renderPreview = (fullSize = false) => {
    if (!contentType) {
      return (
        <div
          className={`flex items-center justify-center ${
            fullSize ? 'size-full' : 'size-full'
          }`}
          aria-label="Generic file icon"
        >
          <FileIcon size={fullSize ? 64 : 32} />
        </div>
      );
    }

    if (contentType.startsWith('image')) {
      return (
        <div
          className={
            fullSize ? 'size-full flex items-center justify-center' : ''
          }
        >
          <Image
            key={url}
            src={url}
            alt={`Preview of ${displayName}`}
            className={
              fullSize
                ? 'max-w-full max-h-[calc(100vh-150px)] object-contain'
                : 'rounded-md size-full object-cover'
            }
            width={fullSize ? 800 : 80}
            height={fullSize ? 600 : 64}
            style={{ width: 'auto', height: 'auto' }}
            priority={fullSize}
          />
        </div>
      );
    } else if (contentType === 'application/pdf') {
      return (
        <PdfPreview
          url={url}
          className={fullSize ? 'size-full' : 'rounded-md'}
        />
      );
    } else {
      return (
        <div
          className={`flex flex-col items-center justify-center ${
            fullSize ? 'size-full' : 'size-full'
          } gap-4`}
          aria-label={`File icon for ${fileType} file`}
        >
          <FileIcon size={fullSize ? 64 : 32} />
          {fullSize && (
            <a
              href={url}
              download={name}
              className="px-4 py-2 bg-zinc-100 dark:bg-zinc-800 rounded-md hover:bg-zinc-200 dark:hover:bg-zinc-700 transition-colors"
            >
              Télécharger le fichier {fileType.toUpperCase()}
            </a>
          )}
        </div>
      );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <div
        data-testid="input-attachment-preview"
        className="flex flex-col gap-2"
        role="group"
        aria-label={`Preview of ${displayName}`}
      >
        <div className="relative">
          <DialogTrigger asChild>
            <button
              type="button"
              className="w-20 h-16 aspect-video bg-muted rounded-md relative flex flex-col items-center justify-center overflow-hidden group"
              aria-busy={isUploading}
              disabled={isUploading}
              aria-label={`View ${displayName}`}
            >
              {renderPreview()}

              {!isUploading && (
                <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <FullscreenIcon size={16} />
                </div>
              )}

              {isUploading && (
                <div
                  data-testid="input-attachment-loader"
                  className="animate-spin absolute text-zinc-500"
                  aria-label="Loading attachment"
                  role="status"
                  aria-live="polite"
                >
                  <LoaderIcon size={16} />
                  <span className="sr-only">Uploading {displayName}</span>
                </div>
              )}
            </button>
          </DialogTrigger>

          {onRemove && !isUploading && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                onRemove();
              }}
              className="absolute top-0 -right-1 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 shadow-sm transition-colors"
              aria-label={`Remove ${displayName}`}
            >
              <TrashIcon size={12} />
            </button>
          )}
        </div>
        <div
          className="text-xs text-zinc-500 max-w-16 truncate"
          aria-label="File name"
        >
          {name}
        </div>
      </div>

      <DialogContent className="sm:max-w-[90vw] sm:w-[90vw] max-h-[90vh] h-[90vh] flex flex-col p-4">
        <DialogTitle className="text-lg font-medium mb-2">
          {displayName}
        </DialogTitle>
        <div className="flex-1 size-full min-h-0">{renderPreview(true)}</div>
      </DialogContent>
    </Dialog>
  );
};
