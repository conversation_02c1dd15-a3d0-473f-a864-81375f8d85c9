import { LRUCache } from 'lru-cache';
import type { Chat, DBMessage } from '@/lib/db/schema';

// Configuration du cache
const CACHE_MAX_SIZE = 100; // Nombre maximum d'éléments dans le cache
const CACHE_TTL = 1000 * 60 * 5; // Durée de vie du cache en ms (5 minutes)
const MESSAGES_CACHE_TTL = 1000 * 60 * 2; // Durée de vie plus courte pour les messages (2 minutes)

// Type pour les résultats de getChatsByUserId
type ChatsByUserIdResult = {
  chats: Chat[];
  hasMore: boolean;
};

// Création du cache LRU pour les chats par utilisateur
const chatsByUserIdCache = new LRUCache<string, ChatsByUserIdResult>({
  max: CACHE_MAX_SIZE,
  ttl: CACHE_TTL,
});

/**
 * Génère une clé de cache pour getChatsByUserId
 */
export function getChatsByUserIdCacheKey(
  userId: string,
  limit: number,
  startingAfter: string | null,
  endingBefore: string | null,
): string {
  return `chats:${userId}:${limit}:${startingAfter || 'null'}:${
    endingBefore || 'null'
  }`;
}

/**
 * Récupère les chats d'un utilisateur depuis le cache
 */
export function getCachedChatsByUserId(
  userId: string,
  limit: number,
  startingAfter: string | null,
  endingBefore: string | null,
): ChatsByUserIdResult | undefined {
  const cacheKey = getChatsByUserIdCacheKey(
    userId,
    limit,
    startingAfter,
    endingBefore,
  );
  return chatsByUserIdCache.get(cacheKey);
}

/**
 * Met en cache les chats d'un utilisateur
 */
export function cacheChatsByUserId(
  userId: string,
  limit: number,
  startingAfter: string | null,
  endingBefore: string | null,
  result: ChatsByUserIdResult,
): void {
  const cacheKey = getChatsByUserIdCacheKey(
    userId,
    limit,
    startingAfter,
    endingBefore,
  );
  chatsByUserIdCache.set(cacheKey, result);
}

/**
 * Invalide le cache des chats pour un utilisateur spécifique
 */
export function invalidateUserChatsCache(userId: string): void {
  // Parcourir toutes les clés du cache et supprimer celles qui concernent cet utilisateur
  for (const key of chatsByUserIdCache.keys()) {
    if (key.startsWith(`chats:${userId}:`)) {
      chatsByUserIdCache.delete(key);
    }
  }
}

// Cache pour getMessageCountByUserId
const messageCountCache = new LRUCache<string, number>({
  max: CACHE_MAX_SIZE,
  ttl: CACHE_TTL,
});

/**
 * Génère une clé de cache pour getMessageCountByUserId
 */
export function getMessageCountCacheKey(
  userId: string,
  differenceInHours: number,
): string {
  return `messageCount:${userId}:${differenceInHours}`;
}

/**
 * Récupère le nombre de messages d'un utilisateur depuis le cache
 */
export function getCachedMessageCount(
  userId: string,
  differenceInHours: number,
): number | undefined {
  const cacheKey = getMessageCountCacheKey(userId, differenceInHours);
  return messageCountCache.get(cacheKey);
}

/**
 * Met en cache le nombre de messages d'un utilisateur
 */
export function cacheMessageCount(
  userId: string,
  differenceInHours: number,
  count: number,
): void {
  const cacheKey = getMessageCountCacheKey(userId, differenceInHours);
  messageCountCache.set(cacheKey, count);
}

/**
 * Invalide le cache du nombre de messages pour un utilisateur spécifique
 */
export function invalidateMessageCountCache(userId: string): void {
  for (const key of messageCountCache.keys()) {
    if (key.startsWith(`messageCount:${userId}:`)) {
      messageCountCache.delete(key);
    }
  }
}

// Cache pour getMessagesByChatId
const messagesByChatIdCache = new LRUCache<string, DBMessage[]>({
  max: CACHE_MAX_SIZE,
  ttl: MESSAGES_CACHE_TTL,
});

/**
 * Génère une clé de cache pour getMessagesByChatId
 */
export function getMessagesByChatIdCacheKey(chatId: string): string {
  return `messages:${chatId}`;
}

/**
 * Récupère les messages d'un chat depuis le cache
 */
export function getCachedMessagesByChatId(
  chatId: string,
): DBMessage[] | undefined {
  const cacheKey = getMessagesByChatIdCacheKey(chatId);
  return messagesByChatIdCache.get(cacheKey);
}

/**
 * Met en cache les messages d'un chat
 */
export function cacheMessagesByChatId(
  chatId: string,
  messages: DBMessage[],
): void {
  const cacheKey = getMessagesByChatIdCacheKey(chatId);
  messagesByChatIdCache.set(cacheKey, messages);
}

/**
 * Invalide le cache des messages pour un chat spécifique
 */
export function invalidateMessagesByChatIdCache(chatId: string): void {
  const cacheKey = getMessagesByChatIdCacheKey(chatId);
  messagesByChatIdCache.delete(cacheKey);
}
