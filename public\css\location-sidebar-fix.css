/* Styles pour améliorer l'affichage des miniatures dans la sidebar de localisation */

/* Conteneur des miniatures */
.location-sheet-thumbnails {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
  justify-content: center;
}

/* Style de base pour les miniatures */
.location-sheet-thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  border: 2px solid transparent;
  transition: all 0.2s ease;
  background-color: #f0f0f0;
}

/* Miniature active */
.location-sheet-thumbnail.active {
  border-color: #3b82f6;
  transform: scale(1.05);
}

/* Effet de survol */
.location-sheet-thumbnail:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Images dans les miniatures */
.location-sheet-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
  opacity: 0;
}

/* Images chargées */
.location-sheet-thumbnail.thumb-loaded img {
  opacity: 1;
}

/* Indicateur de chargement */
.location-sheet-thumbnail::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #e5e7eb;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: thumbnail-spinner 0.8s linear infinite;
  opacity: 1;
  transition: opacity 0.3s ease;
}

/* Masquer l'indicateur de chargement une fois l'image chargée */
.location-sheet-thumbnail.thumb-loaded::before {
  opacity: 0;
}

/* Animation de l'indicateur de chargement */
@keyframes thumbnail-spinner {
  to {
    transform: rotate(360deg);
  }
}

/* Image principale */
.location-sheet-image {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  background-color: #f0f0f0;
}

.location-sheet-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
  opacity: 0;
}

/* Indicateur de chargement pour l'image principale */
.location-sheet-image::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 30px;
  height: 30px;
  margin: -15px 0 0 -15px;
  border: 3px solid #e5e7eb;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: thumbnail-spinner 0.8s linear infinite;
  opacity: 1;
  transition: opacity 0.3s ease;
}

/* Masquer l'indicateur de chargement une fois l'image principale chargée */
.location-sheet-image.image-loaded::before {
  opacity: 0;
}

/* Amélioration de la sidebar de localisation */
.location-sheet {
  position: fixed;
  top: 0;
  right: 0;
  width: 320px;
  height: 100vh;
  background-color: white;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
  padding: 20px;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.location-sheet.open {
  transform: translateX(0);
}

/* Bouton de fermeture */
.location-sheet-close {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  transition: color 0.2s ease;
}

.location-sheet-close:hover {
  color: #1f2937;
}

/* Titre de la localisation */
.location-sheet-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 10px;
  padding-right: 30px;
}

/* Type de localisation */
.location-sheet-type {
  display: inline-block;
  padding: 4px 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  font-size: 0.875rem;
  margin-bottom: 15px;
}

/* Description de la localisation */
.location-sheet-description {
  margin-bottom: 15px;
  line-height: 1.5;
}

/* Informations supplémentaires */
.location-sheet-info {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e5e7eb;
}

/* Boutons d'action */
.location-sheet-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.location-sheet-button {
  flex: 1;
  padding: 8px 12px;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.location-sheet-button:hover {
  background-color: #e5e7eb;
}

.location-sheet-button.primary {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.location-sheet-button.primary:hover {
  background-color: #2563eb;
  border-color: #2563eb;
}
