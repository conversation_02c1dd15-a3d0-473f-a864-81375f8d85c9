'use client';

import React from 'react';
import MapComponent from './map-component';
import { addSampleImagesToPlaces } from '@/lib/sample-place-images';
import type { Place } from '@/lib/types';

// Sample places data with images for demonstration
const samplePlaces: Place[] = [
  {
    cid: '1',
    title: 'Le Petit Bistro',
    address: '123 Rue de la Paix, Paris, France',
    latitude: 48.8566,
    longitude: 2.3522,
    rating: 4.5,
    category: 'Restaurant',
    phoneNumber: '+33 1 42 60 34 12',
    website: 'https://lepetitbistro.fr'
  },
  {
    cid: '2',
    title: 'Hotel des Arts',
    address: '456 Avenue des Champs-Élysées, Paris, France',
    latitude: 48.8738,
    longitude: 2.2950,
    rating: 4.8,
    category: 'Hotel',
    phoneNumber: '+33 1 53 89 50 00',
    website: 'https://hoteldesarts.com'
  },
  {
    cid: '3',
    title: 'Musée du Louvre',
    address: 'Rue de Rivoli, 75001 Paris, France',
    latitude: 48.8606,
    longitude: 2.3376,
    rating: 4.9,
    category: 'Museum',
    website: 'https://www.louvre.fr'
  },
  {
    cid: '4',
    title: 'Café de Flore',
    address: '172 Boulevard Saint-Germain, Paris, France',
    latitude: 48.8542,
    longitude: 2.3320,
    rating: 4.3,
    category: 'Cafe',
    phoneNumber: '+33 1 45 48 55 26',
    website: 'https://cafedeflore.fr'
  }
];

interface MapWithImageSidebarDemoProps {
  query?: string;
}

const MapWithImageSidebarDemo: React.FC<MapWithImageSidebarDemoProps> = ({ 
  query = "Paris attractions and restaurants" 
}) => {
  // Add sample images to the places
  const placesWithImages = addSampleImagesToPlaces(samplePlaces);

  return (
    <div className="w-full">
      <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
          🖼️ Image Sidebar Demo
        </h2>
        <p className="text-blue-700 dark:text-blue-300 text-sm">
          Click on any location card or map marker to see the new image sidebar in action! 
          The sidebar will slide up from the bottom with images for the selected location.
        </p>
        <div className="mt-2 text-xs text-blue-600 dark:text-blue-400">
          <strong>Features to try:</strong>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Click location cards to open image sidebar</li>
            <li>Navigate through multiple images with arrows</li>
            <li>Use thumbnail strip for quick selection</li>
            <li>Close with X button, Escape key, or backdrop click</li>
            <li>On mobile: swipe down to close</li>
          </ul>
        </div>
      </div>
      
      <MapComponent 
        places={placesWithImages} 
        query={query}
      />
    </div>
  );
};

export default MapWithImageSidebarDemo;
