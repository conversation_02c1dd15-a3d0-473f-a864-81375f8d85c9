/**
 * Script pour améliorer le chargement des images dans la sidebar de localisation
 * des artifacts HTML de voyage
 */

// Fonction pour charger une image avec gestion des erreurs et fallbacks
function loadImageWithFallbacks(imageUrl, onSuccess, onError, attemptCount = 0) {
  console.log(`[LocationSidebar] Tentative ${attemptCount+1} de chargement de l'image:`, imageUrl);

  // Limite le nombre de tentatives
  if (attemptCount > 2) {
    console.error('[LocationSidebar] Trop de tentatives de chargement pour l\'image:', imageUrl);
    if (onError) onError();
    return;
  }

  // Si l'URL est vide ou invalide, passer directement à l'image de placeholder
  if (!imageUrl || imageUrl === 'undefined' || imageUrl === 'null') {
    console.error('[LocationSidebar] URL d\'image invalide:', imageUrl);
    if (onError) onError();
    return;
  }

  // Créer un élément image pour tester le chargement
  const img = new Image();

  // Définir un timeout pour éviter d'attendre indéfiniment
  const timeoutId = setTimeout(() => {
    console.error('[LocationSidebar] Timeout lors du chargement de l\'image:', imageUrl);
    img.src = ''; // Annuler le chargement
    tryNextFallback();
  }, 5000); // 5 secondes de timeout

  // Fonction pour essayer le prochain fallback
  function tryNextFallback() {
    // Première tentative: essayer avec le proxy
    if (attemptCount === 0 && !imageUrl.includes('/api/proxy-image')) {
      // Nettoyer l'URL si elle contient déjà des caractères encodés
      let urlToProxy = imageUrl;
      if (urlToProxy.includes('%')) {
        try {
          urlToProxy = decodeURIComponent(urlToProxy);
        } catch (e) {
          // Ignorer les erreurs de décodage
        }
      }

      const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(urlToProxy)}`;
      console.log('[LocationSidebar] Tentative avec proxy:', proxyUrl);
      loadImageWithFallbacks(proxyUrl, onSuccess, onError, attemptCount + 1);
    }
    // Deuxième tentative: utiliser une image de placeholder Picsum
    else if (attemptCount === 1) {
      const randomId = Math.floor(Math.random() * 1000);
      const picsumUrl = `https://picsum.photos/300/200?random=${randomId}`;
      console.log('[LocationSidebar] Tentative avec Picsum:', picsumUrl);
      loadImageWithFallbacks(picsumUrl, onSuccess, onError, attemptCount + 1);
    }
    // Dernière tentative: utiliser une image de placeholder SVG
    else {
      console.log('[LocationSidebar] Utilisation de l\'image placeholder SVG');
      // Créer une image SVG de placeholder
      const placeholderSvg = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='200' viewBox='0 0 300 200'%3E%3Crect width='300' height='200' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='16' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3EImage%3C/text%3E%3C/svg%3E`;

      if (onSuccess) {
        // Même pour le placeholder, on appelle onSuccess pour avoir un comportement cohérent
        onSuccess(placeholderSvg);
      } else if (onError) {
        onError();
      }
    }
  }

  // Gestionnaire d'événement de chargement réussi
  img.onload = function() {
    clearTimeout(timeoutId);
    console.log('[LocationSidebar] Image chargée avec succès:', imageUrl);
    if (onSuccess) onSuccess(img.src);
  };

  // Gestionnaire d'événement d'erreur
  img.onerror = function() {
    clearTimeout(timeoutId);
    console.error('[LocationSidebar] Échec du chargement de l\'image:', imageUrl);
    tryNextFallback();
  };

  // Commencer le chargement
  img.src = imageUrl;
}

// Fonction améliorée pour ouvrir les détails de localisation
function enhancedOpenLocationDetail(element) {
  try {
    // Vérifier si la fonction originale existe
    if (typeof window.openLocationDetail === 'function') {
      // Appeler la fonction originale
      window.openLocationDetail(element);

      // Ajouter notre amélioration après l'exécution de la fonction originale
      setTimeout(fixLocationImages, 100);
    } else {
      console.error('La fonction openLocationDetail n\'existe pas');
    }
  } catch (error) {
    console.error('Erreur lors de l\'ouverture des détails de localisation:', error);
  }
}

// Fonction pour corriger les images dans la sidebar
function fixLocationImages() {
  try {
    // Trouver le conteneur des miniatures
    const thumbnailsContainer = document.querySelector('.location-sheet-thumbnails');
    if (!thumbnailsContainer) {
      console.log('Conteneur de miniatures non trouvé');
      return;
    }

    // Trouver l'image principale
    const mainImage = document.querySelector('.location-sheet-image img');
    if (!mainImage) {
      console.log('Image principale non trouvée');
      return;
    }

    // Récupérer les données de localisation
    const locationData = JSON.parse(document.querySelector('.location-sheet').getAttribute('data-location') || '{}');
    if (!locationData || !locationData.name) {
      console.error('Données de localisation invalides');
      return;
    }

    console.log('Correction des images pour:', locationData.name);

    // Récupérer les URLs d'images
    const imageUrls = locationData.imageUrls || [];
    if (imageUrls.length === 0) {
      console.log('Aucune URL d\'image trouvée, génération d\'URLs alternatives');
      // Générer des termes de recherche alternatifs
      const searchTerms = [
        `${locationData.name} ${locationData.type || 'attraction'}`,
        `${locationData.name} landmark`,
        `${locationData.name} tourism`
      ];

      // Ajouter des URLs Unsplash
      for (let i = 0; i < 3; i++) {
        const searchTerm = encodeURIComponent(searchTerms[i % searchTerms.length]);
        const randomParam = Math.random();
        imageUrls.push(`https://source.unsplash.com/300x200/?${searchTerm}&random=${randomParam}`);
      }
    }

    // Vider le conteneur des miniatures
    thumbnailsContainer.innerHTML = '';

    // Créer les nouvelles miniatures
    imageUrls.forEach((url, index) => {
      const thumbnail = document.createElement('div');
      thumbnail.className = `location-sheet-thumbnail ${index === 0 ? 'active' : ''}`;

      const img = document.createElement('img');
      img.alt = `${locationData.name} image ${index + 1}`;
      img.style.opacity = '0';

      // Charger l'image avec fallbacks
      loadImageWithFallbacks(
        url,
        (successUrl) => {
          img.src = successUrl;
          img.style.opacity = '1';
          thumbnail.classList.add('thumb-loaded');

          // Si c'est la première image, mettre à jour l'image principale
          if (index === 0) {
            mainImage.src = successUrl;
            mainImage.style.opacity = '1';
          }

          // Ajouter un gestionnaire de clic pour changer l'image principale
          thumbnail.addEventListener('click', () => {
            // Retirer la classe active de toutes les miniatures
            document.querySelectorAll('.location-sheet-thumbnail').forEach(thumb => {
              thumb.classList.remove('active');
            });

            // Ajouter la classe active à cette miniature
            thumbnail.classList.add('active');

            // Mettre à jour l'image principale
            mainImage.style.opacity = '0';
            setTimeout(() => {
              mainImage.src = img.src;
              mainImage.style.opacity = '1';
            }, 200);
          });
        },
        () => {
          // En cas d'échec, utiliser une image de placeholder
          img.src = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='12' fill='%23666' text-anchor='middle' dominant-baseline='middle'%3E${encodeURIComponent(locationData.name || 'Location')}%3C/text%3E%3C/svg%3E`;
          img.style.opacity = '1';
        }
      );

      thumbnail.appendChild(img);
      thumbnailsContainer.appendChild(thumbnail);
    });

  } catch (error) {
    console.error('Erreur lors de la correction des images:', error);
  }
}

// Remplacer la fonction originale par notre version améliorée
window.addEventListener('load', function() {
  // Sauvegarder la fonction originale
  if (typeof window.openLocationDetail === 'function') {
    window.originalOpenLocationDetail = window.openLocationDetail;

    // Remplacer par notre version améliorée
    window.openLocationDetail = enhancedOpenLocationDetail;

    console.log('Fonction openLocationDetail améliorée installée');
  } else {
    console.log('Fonction openLocationDetail non trouvée, installation différée');

    // Vérifier périodiquement si la fonction devient disponible
    const checkInterval = setInterval(function() {
      if (typeof window.openLocationDetail === 'function') {
        window.originalOpenLocationDetail = window.openLocationDetail;
        window.openLocationDetail = enhancedOpenLocationDetail;
        console.log('Fonction openLocationDetail améliorée installée (différée)');
        clearInterval(checkInterval);
      }
    }, 500);

    // Arrêter la vérification après 10 secondes
    setTimeout(function() {
      clearInterval(checkInterval);
    }, 10000);
  }
});
