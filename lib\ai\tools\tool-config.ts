import { z } from 'zod';

export const toolConfigs = {
  showMarketTrending: {
    description:
      'Displays market trending data including top gainers, losers, and most active stocks. Use this tool to show current market trends to the user.',
    parameters: z.object({}),
  },
  showStockPrice: {
    description:
      'Displays price information and chart for a stock. Use this tool to show the current price and price history of a stock.',
    parameters: z.object({
      symbol: z
        .string()
        .describe(
          'The symbol or name of the stock. Example: AAPL, MSFT, GOOGL',
        ),
    }),
  },
  showStockFinancials: {
    description:
      'Displays financial information for a stock. Use this tool to show financial data to the user.',
    parameters: z.object({
      symbol: z
        .string()
        .describe(
          'The symbol or name of the stock. Example: AAPL, MSFT, GOOGL',
        ),
    }),
  },
  showStockNews: {
    description:
      'Displays the latest news about a stock. Use this tool to show financial news to the user.',
    parameters: z.object({
      symbol: z
        .string()
        .describe(
          'The symbol or name of the stock. Example: AAPL, MSFT, GOOGL',
        ),
    }),
  },
  showStockScreener: {
    description:
      'Displays a stock market screener showing the most important stocks. Use this tool to show an overview of the stock market to the user.',
    parameters: z.object({}),
  },
  showStockChart: {
    description:
      'Displays a stock chart for a stock. Use this tool to show the stock price evolution to the user.',
    parameters: z.object({
      symbol: z
        .string()
        .describe(
          'The symbol or name of the stock. Example: AAPL, MSFT, GOOGL',
        ),
      interval: z
        .enum(['1', '5', '15', '60', 'D', 'W', 'M'])
        .optional()
        .describe(
          'The time interval for the chart (1, 5, 15, 60 for minutes, D for daily, W for weekly, M for monthly)',
        ),
    }),
  },
  showHeatmapsMarket: {
    description:
      'Displays a market heatmap showing sector performance and stock visualization. Use this tool to show a visual overview of market sectors and their performance.',
    parameters: z.object({}),
  },
  showCryptoCoinsHeatmap: {
    description:
      'Displays a cryptocurrency heatmap showing crypto market cap visualization and performance. Use this tool to show a visual overview of cryptocurrency market and their performance.',
    parameters: z.object({}),
  },
} as const;
