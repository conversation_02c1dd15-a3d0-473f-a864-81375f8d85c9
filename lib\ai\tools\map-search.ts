import { tool } from 'ai';
import { z } from 'zod';
import type { Session } from 'next-auth';
import type { DataStreamWriter } from 'ai';
import { serperMapSearch } from '../../utils';
import type { Place } from '../../types';

/**
 * Outil de recherche de carte qui utilise l'API Serper pour trouver des lieux
 * et les afficher sur une carte.
 */
export const map_search = ({
  session,
  dataStream,
}: {
  session: Session;
  dataStream: DataStreamWriter;
}) =>
  tool({
    description:
      'Search for locations on a map. Use this tool when the user asks about a specific location, wants to see a place on a map, or asks for directions, addresses, or coordinates.',
    parameters: z.object({
      query: z
        .string()
        .describe('The location or place to search for on the map.'),
      includeDetails: z
        .boolean()
        .describe(
          'Whether to include detailed information about the places found.',
        )
        .optional(),
    }),
    execute: async ({
      query,
      includeDetails = true,
    }: {
      query: string;
      includeDetails?: boolean;
    }) => {
      console.log('Map search tool called with query:', query);

      try {
        // Rechercher des lieux avec l'API Serper
        const places = await serperMapSearch(query);

        console.log(
          `Map search found ${places.length} places for query: ${query}`,
        );

        // Ajouter une annotation pour afficher la carte
        dataStream.writeMessageAnnotation({
          type: 'map_display',
          data: {
            query,
            places: places as any,
          },
        } as any);

        // Préparer la réponse
        if (places.length === 0) {
          return {
            success: false,
            message: `No places found for "${query}".`,
            places: [],
          };
        }

        // Formater les résultats
        const formattedPlaces = places.map((place: Place) => {
          if (includeDetails) {
            return {
              title: place.title,
              address: place.address,
              category: place.category,
              rating: place.rating,
              latitude: place.latitude,
              longitude: place.longitude,
              phoneNumber: place.phoneNumber,
              website: place.website,
            };
          } else {
            return {
              title: place.title,
              address: place.address,
              category: place.category,
            };
          }
        });

        return {
          success: true,
          message: `Found ${places.length} places for "${query}".`,
          places: formattedPlaces,
        };
      } catch (error) {
        console.error('Error in map search tool:', error);
        return {
          success: false,
          message: `Error searching for "${query}": ${error instanceof Error ? error.message : String(error)}`,
          places: [],
        };
      }
    },
  });
