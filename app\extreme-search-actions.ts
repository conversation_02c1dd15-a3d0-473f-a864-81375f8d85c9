'use server';

import { getUserId } from '@/lib/utils';
// Note: extremeSearchTool est importé dans d'autres fichiers qui utilisent ce module

// Stockage en mémoire pour les compteurs d'utilisation (en production, utilisez Redis ou une base de données)
const extremeSearchUsage = new Map<string, number>();
const MAX_EXTREME_SEARCHES = 10; // Limite quotidienne

// Action pour vérifier si l'utilisateur peut utiliser la recherche extrême
export async function canUseExtremeSearch() {
  try {
    // Utiliser un ID utilisateur générique ou basé sur une session
    const userId = getUserId();

    // Récupérer le compteur actuel ou initialiser à 0
    const currentCount = extremeSearchUsage.get(userId) || 0;

    if (currentCount >= MAX_EXTREME_SEARCHES) {
      return {
        canUse: false,
        reason: 'limit_reached',
        count: currentCount,
        limit: MAX_EXTREME_SEARCHES,
      };
    }

    return {
      canUse: true,
      count: currentCount,
      limit: MAX_EXTREME_SEARCHES,
    };
  } catch (error) {
    console.error('Error in canUseExtremeSearch:', error);
    return { canUse: false, reason: 'error', error: String(error) };
  }
}

// Action pour incrémenter le compteur de recherches extrêmes
// Le paramètre chatId est optionnel et peut être utilisé pour la journalisation ou le suivi
export async function incrementExtremeSearchUsage(chatId?: string) {
  try {
    // Utiliser un ID utilisateur générique ou basé sur une session
    const userId = getUserId();

    // Récupérer le compteur actuel ou initialiser à 0
    const currentCount = extremeSearchUsage.get(userId) || 0;

    // Incrémenter le compteur
    extremeSearchUsage.set(userId, currentCount + 1);

    // Si chatId est fourni, on pourrait l'utiliser pour la journalisation
    if (chatId) {
      console.log(`Incremented extreme search usage for chat: ${chatId}`);
    }

    return {
      success: true,
      count: currentCount + 1,
    };
  } catch (error) {
    console.error('Error incrementing extreme search usage:', error);
    return { success: false, reason: 'error', error: String(error) };
  }
}

// Action pour exécuter une recherche extrême
// Le paramètre prompt est utilisé pour identifier la requête de recherche
// Le paramètre chatId est optionnel et peut être utilisé pour la journalisation
export async function executeExtremeSearch(prompt: string, chatId?: string) {
  try {
    // Vérifier si l'utilisateur peut utiliser la recherche extrême
    const canUse = await canUseExtremeSearch();
    if (!canUse.canUse) {
      return {
        success: false,
        reason: canUse.reason,
        count: canUse.count,
        limit: canUse.limit,
      };
    }

    // Incrémenter le compteur d'utilisation
    await incrementExtremeSearchUsage(chatId);

    // Ici, on pourrait utiliser prompt pour des analyses ou des statistiques
    console.log(
      `Executing extreme search for prompt: "${prompt.substring(0, 50)}..."`,
    );

    // Retourner le succès pour permettre l'exécution de la recherche extrême
    return {
      success: true,
      count: (canUse.count || 0) + 1,
      limit: canUse.limit,
    };
  } catch (error) {
    console.error('Error executing extreme search:', error);
    return { success: false, reason: 'error', error: String(error) };
  }
}

// Fonction pour obtenir les statistiques d'utilisation de la recherche extrême
export async function getExtremeSearchStats() {
  try {
    const userId = getUserId();
    const currentCount = extremeSearchUsage.get(userId) || 0;

    return {
      success: true,
      count: currentCount,
      limit: MAX_EXTREME_SEARCHES,
      remaining: Math.max(0, MAX_EXTREME_SEARCHES - currentCount),
    };
  } catch (error) {
    console.error('Error getting extreme search stats:', error);
    return { success: false, reason: 'error', error: String(error) };
  }
}

// Fonction utilitaire pour réinitialiser les compteurs (à appeler quotidiennement)
export function resetExtremeSearchUsage() {
  extremeSearchUsage.clear();
  console.log('Extreme search usage counters reset');
}
