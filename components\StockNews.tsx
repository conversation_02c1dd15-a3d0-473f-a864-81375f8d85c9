'use client';

import React, { useEffect, useRef, useState, memo } from 'react';

interface StockNewsProps {
  ticker: string;
}

function StockNews({ ticker }: StockNewsProps) {
  const container = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasNews, setHasNews] = useState(true);

  useEffect(() => {
    // Vérifier si le ticker est valide
    if (!ticker || typeof ticker !== 'string' || ticker.trim() === '') {
      console.error('Ticker invalide');
      return;
    }

    // Nettoyer le ticker
    const cleanTicker = ticker.trim().toUpperCase();
    
    // Vérifier si le conteneur existe
    if (!container.current) return;

    // Nettoyer le conteneur
    container.current.innerHTML = '';
    setIsLoading(true);

    // Créer le conteneur principal pour l'iframe
    const iframeContainer = document.createElement('div');
    iframeContainer.style.width = '100%';
    iframeContainer.style.height = '600px';
    iframeContainer.style.border = '1px solid #e0e3eb';
    iframeContainer.style.borderRadius = '8px';
    iframeContainer.style.overflow = 'hidden';
    
    // Créer un élément iframe pour afficher les actualités TradingView
    const iframe = document.createElement('iframe');
    const widgetUrl = `https://www.tradingview.com/embed-widget/timeline/?symbol=${encodeURIComponent(cleanTicker)}&colorTheme=light&isTransparent=true&locale=fr`;
    iframe.src = widgetUrl;
    iframe.style.width = '100%';
    iframe.style.height = '100%';
    iframe.style.border = 'none';
    // @ts-ignore - La propriété allowTransparency est obsolète mais nécessaire pour la compatibilité
    iframe.allowTransparency = true;
    iframe.referrerPolicy = 'no-referrer-when-downgrade';
    iframe.allowFullscreen = true;
    iframe.title = `Actualités pour ${cleanTicker}`;

    // Gérer le chargement de l'iframe
    iframe.onload = () => {
      setIsLoading(false);
      
      // Vérifier si l'iframe affiche le message "Pas encore de données ici"
      try {
        // Cette vérification peut être effectuée après un court délai pour laisser le temps au contenu de se charger
        setTimeout(() => {
          if (iframe.contentDocument?.body?.textContent?.includes('Pas encore de données ici') ||
              iframe.contentDocument?.body?.textContent?.includes('No data yet')) {
            setHasNews(false);
          } else {
            setHasNews(true);
          }
        }, 2000);
      } catch (e) {
        console.error('Erreur lors de la vérification du contenu de l\'iframe:', e);
        setHasNews(true); // Par défaut, considérer qu'il y a des actualités
      }
    };

    // Gérer les erreurs de chargement
    iframe.onerror = () => {
      console.error('Erreur lors du chargement des actualités TradingView');
      if (container.current) {
        container.current.innerHTML = `
          <div style="padding: 20px; text-align: center; color: #666;">
            <p>Impossible de charger les actualités pour ${cleanTicker}.</p>
            <p>Veuillez réessayer plus tard ou vérifier le symbole.</p>
            <p><a href="https://www.tradingview.com/symbols/${cleanTicker}/news/" target="_blank" rel="noopener noreferrer">
              Voir les actualités sur TradingView
            </a></p>
          </div>
        `;
        setIsLoading(false);
      }
    };

    // Ajouter l'iframe au conteneur
    iframeContainer.appendChild(iframe);
    container.current.appendChild(iframeContainer);

    // Nettoyage
    return () => {
      if (container.current) {
        container.current.innerHTML = '';
      }
    };
  }, [ticker]);

  return (
    <div style={{ position: 'relative', width: '100%', minHeight: '600px' }}>
      {isLoading && (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          zIndex: 10
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ marginBottom: '10px' }}>Chargement des actualités pour {ticker.toUpperCase()}...</div>
          </div>
        </div>
      )}
      {hasNews ? (
        <div ref={container} style={{ width: '100%', height: '100%' }} />
      ) : (
        <div style={{
          padding: '20px',
          textAlign: 'center',
          color: '#666',
          backgroundColor: '#f8f9fa',
          borderRadius: '8px',
          border: '1px solid #e0e3eb',
          margin: '10px 0'
        }}>
          <p>Aucune actualité disponible pour {ticker.toUpperCase()} sur TradingView.</p>
          <p>
            <a 
              href={`https://www.tradingview.com/symbols/${ticker.trim().toUpperCase()}/news/`} 
              target="_blank" 
              rel="noopener noreferrer"
              style={{
                color: '#3b82f6',
                textDecoration: 'underline'
              }}
            >
              Vérifier sur le site de TradingView
            </a>
          </p>
        </div>
      )}
    </div>
  );
}

export default memo(StockNews);
