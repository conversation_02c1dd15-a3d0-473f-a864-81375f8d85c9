/**
 * Mock AI Provider
 * 
 * This module provides mock implementations of AI services for testing.
 * It returns predefined responses based on the input prompts.
 */

/**
 * Generate a mock text response based on the input prompt
 * @param prompt The input prompt
 * @returns A predefined response based on the prompt content
 */
export function generateMockText(prompt: string): string {
  // Weather-related prompts
  if (prompt.toLowerCase().includes('weather')) {
    return 'The weather in San Francisco is currently 65°F and sunny with light winds. The forecast for the next few days shows continued sunshine with temperatures ranging from 60°F to 70°F.';
  }
  
  // Sky blue prompts
  if (prompt.toLowerCase().includes('sky blue')) {
    return 'The sky appears blue because of a phenomenon called Rayleigh scattering. Sunlight contains all colors of the visible spectrum, but blue light has a shorter wavelength and is scattered more easily by air molecules in the atmosphere. This scattered blue light is what we see when we look at the sky.';
  }
  
  // Grass green prompts
  if (prompt.toLowerCase().includes('grass green')) {
    return 'Grass appears green because it contains a pigment called chlorophyll, which absorbs blue and red light while reflecting green light. Chlorophyll is essential for photosynthesis, the process by which plants convert sunlight into energy.';
  }
  
  // Programming-related prompts
  if (prompt.toLowerCase().includes('javascript') || prompt.toLowerCase().includes('code')) {
    return 'JavaScript is a high-level, interpreted programming language that conforms to the ECMAScript specification. It is a language that is also characterized as dynamic, weakly typed, prototype-based and multi-paradigm. Here\'s a simple example:\n\n```javascript\nfunction greet(name) {\n  return `Hello, ${name}!`;\n}\n\nconsole.log(greet("World"));\n// Output: Hello, World!\n```';
  }
  
  // Recipe-related prompts
  if (prompt.toLowerCase().includes('recipe') || prompt.toLowerCase().includes('cook') || prompt.toLowerCase().includes('food')) {
    return 'Here\'s a simple pasta recipe:\n\n**Ingredients:**\n- 8 oz pasta\n- 2 tbsp olive oil\n- 3 cloves garlic, minced\n- 1/4 tsp red pepper flakes\n- 1/4 cup chopped parsley\n- Salt and pepper to taste\n- Grated Parmesan cheese\n\n**Instructions:**\n1. Cook pasta according to package directions.\n2. While pasta cooks, heat olive oil in a pan over medium heat.\n3. Add garlic and red pepper flakes, cook for 1-2 minutes until fragrant.\n4. Drain pasta, reserving 1/4 cup of pasta water.\n5. Add pasta to the pan with garlic oil, toss to coat.\n6. Add reserved pasta water if needed for moisture.\n7. Stir in parsley, salt, and pepper.\n8. Serve with grated Parmesan cheese.';
  }
  
  // History-related prompts
  if (prompt.toLowerCase().includes('history') || prompt.toLowerCase().includes('historical')) {
    return 'The Renaissance was a period in European history marking the transition from the Middle Ages to modernity and covering the 15th and 16th centuries. It began in Italy and spread to the rest of Europe, characterized by an emphasis on learning, scientific inquiry, and artistic expression. Key figures included Leonardo da Vinci, Michelangelo, and Raphael in art, and Nicolaus Copernicus and Galileo Galilei in science.';
  }
  
  // Quantum computing prompts (for regression tests)
  if (prompt.toLowerCase().includes('quantum computing')) {
    return 'Quantum computing is a type of computation that harnesses the collective properties of quantum states, such as superposition, interference, and entanglement, to perform calculations. The devices that perform quantum computations are known as quantum computers.\n\nUnlike classical computers that use bits (0 or 1), quantum computers use quantum bits or qubits. Qubits can exist in multiple states simultaneously due to superposition, allowing quantum computers to process a vast number of possibilities at once.\n\nEntanglement is another key property where qubits become correlated in such a way that the quantum state of each qubit cannot be described independently. This allows quantum computers to potentially solve certain problems much faster than classical computers.\n\nQuantum computing has promising applications in cryptography, optimization problems, drug discovery, materials science, and artificial intelligence. However, current quantum computers are still in early stages, facing challenges like quantum decoherence and error correction.\n\nMajor companies like IBM, Google, Microsoft, and several startups are actively developing quantum computing technology. In 2019, Google claimed to achieve "quantum supremacy" by performing a calculation that would be practically impossible for classical computers.\n\nDespite the progress, practical, large-scale quantum computers that can solve real-world problems better than classical computers are still years away from widespread implementation.';
  }
  
  // Default response for other prompts
  return 'I\'m a mock AI assistant. This is a predefined response for testing purposes. In a real environment, I would provide a helpful response based on your query.';
}

/**
 * Mock AI Provider
 * 
 * This object mimics the interface of an AI provider but returns predefined responses.
 */
export const mockAIProvider = {
  /**
   * Generate text based on a prompt
   * @param prompt The input prompt
   * @returns A promise that resolves to a predefined response
   */
  generateText: async (prompt: string): Promise<string> => {
    // Add a small delay to simulate network latency
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return generateMockText(prompt);
  },
  
  /**
   * Generate a chat completion
   * @param messages Array of chat messages
   * @returns A promise that resolves to a predefined response
   */
  generateChatCompletion: async (messages: Array<{role: string, content: string}>): Promise<string> => {
    // Add a small delay to simulate network latency
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // Extract the last user message
    const lastUserMessage = messages
      .filter(msg => msg.role === 'user')
      .pop();
    
    if (lastUserMessage) {
      return generateMockText(lastUserMessage.content);
    }
    
    return 'I don\'t have enough context to provide a response.';
  },
  
  /**
   * Generate an embedding for a text
   * @param text The input text
   * @returns A promise that resolves to a mock embedding
   */
  generateEmbedding: async (text: string): Promise<number[]> => {
    // Generate a deterministic but seemingly random embedding based on the text
    const embedding = Array(1536).fill(0).map((_, i) => {
      // Use a simple hash of the text and position to generate a value
      const hash = text.split('').reduce((acc, char, idx) => 
        acc + char.charCodeAt(0) * (idx + 1), 0) * (i + 1);
      
      // Normalize to a value between -1 and 1
      return (hash % 1000) / 500 - 1;
    });
    
    return embedding;
  }
};
