declare module 'mem0ai' {
  export interface MemoryOptions {
    user_id: string;
    org_id: string;
    project_id: string;
    metadata?: Record<string, any>;
    categories?: string[];
  }

  export interface MemorySearchFilters {
    user_id?: string | { in: string[] };
    agent_id?: string | { in: string[] };
    memory_type?: string | { in: string[] };
    [key: string]: any;
  }

  export interface MemorySearchOptions {
    user_id?: string;
    org_id?: string;
    project_id?: string;
    limit?: number;
    filters?: MemorySearchFilters | { OR: MemorySearchFilters[] } | { AND: MemorySearchFilters[] };
    api_version?: string;
    metadata?: Record<string, any>;
    categories?: string[];
    sort_by?: 'relevance' | 'created_at' | 'updated_at';
    sort_order?: 'asc' | 'desc';
  }

  export interface MemoryAddResponse {
    id: string;
    data: {
      memory: string;
    };
    event: 'ADD';
  }

  export interface MemorySearchResponse {
    id: string;
    memory: string;
    user_id: string;
    metadata: Record<string, any> | null;
    categories: string[];
    immutable: boolean;
    created_at: string;
    updated_at: string;
    message: string | null;
  }

  // Ajout d'une interface d'array pour les réponses de recherche
  export interface MemorySearchResponseArray
    extends Array<MemorySearchResponse> {
    length: number;
    sort(
      compareFn?: (a: MemorySearchResponse, b: MemorySearchResponse) => number,
    ): this;
    find(
      predicate: (value: MemorySearchResponse) => boolean,
    ): MemorySearchResponse | undefined;
    map<U>(callbackfn: (value: MemorySearchResponse) => U): U[];
    [index: number]: MemorySearchResponse;
  }

  export interface MemoryMessage {
    role: string;
    content: string;
    timestamp?: string;
    metadata?: Record<string, any>;
  }

  export class MemoryClient {
    apiKey: string;
    constructor(options: { apiKey: string });

    add(
      memory: string | MemoryMessage[], 
      options: MemoryOptions
    ): Promise<MemoryAddResponse[]>;

    search(
      query: string,
      options: Partial<MemorySearchOptions>,
    ): Promise<MemorySearchResponseArray>;
  }
}
