// Test pour déboguer la progression d'Extreme Search
console.log('🧪 Test de progression Extreme Search');

// Simuler les étapes de progression
const progressSteps = [
  {
    progress: 5,
    message: 'Initializing extreme search...',
    stage: 'initialization',
  },
  {
    progress: 10,
    message: 'Planning comprehensive research strategy...',
    stage: 'planning',
  },
  {
    progress: 15,
    message: 'Generating detailed research plan...',
    stage: 'plan_generation',
  },
  {
    progress: 20,
    message: 'Research plan created successfully!',
    stage: 'plan_complete',
  },
  {
    progress: 25,
    message: 'Starting research execution...',
    stage: 'research_start',
  },
  {
    progress: 30,
    message: 'Researching: Technical Analysis',
    stage: 'section_research',
  },
  {
    progress: 35,
    message: 'Searching: machine learning algorithms',
    stage: 'query_search',
  },
  { progress: 40, message: 'Completed 3/16 searches', stage: 'query_search' },
  {
    progress: 45,
    message: 'Searching: data visualization techniques',
    stage: 'query_search',
  },
  { progress: 50, message: 'Completed 6/16 searches', stage: 'query_search' },
  {
    progress: 55,
    message: 'Researching: Data Processing',
    stage: 'section_research',
  },
  { progress: 60, message: 'Completed 8/16 searches', stage: 'query_search' },
  { progress: 65, message: 'Completed 12/16 searches', stage: 'query_search' },
  { progress: 70, message: 'Completed 14/16 searches', stage: 'query_search' },
  {
    progress: 75,
    message: 'Analyzing research data...',
    stage: 'data_analysis',
  },
  {
    progress: 85,
    message: 'Generating comprehensive research report...',
    stage: 'report_generation',
  },
  {
    progress: 90,
    message: 'Performing quality review...',
    stage: 'quality_review',
  },
  {
    progress: 100,
    message: 'Research completed successfully!',
    stage: 'complete',
  },
];

// Fonction pour simuler l'envoi d'annotations
function simulateProgressAnnotation(step) {
  const annotation = {
    type: 'search-progress',
    message: step.message,
    progress: step.progress,
    stage: step.stage,
    timestamp: Date.now(),
    data: {
      totalQueries: 16,
      sectionsCompleted: Math.floor(step.progress / 25),
      sourcesFound: Math.floor(step.progress / 5),
    },
  };

  console.log(`📊 Progress: ${step.progress}% - ${step.message}`);
  console.log(`   Stage: ${step.stage}`);
  console.log(`   Data:`, annotation.data);

  return annotation;
}

// Simuler la progression
console.log('\n🚀 Simulation de la progression:');
progressSteps.forEach((step, index) => {
  setTimeout(() => {
    simulateProgressAnnotation(step);

    if (index === progressSteps.length - 1) {
      console.log('\n✅ Simulation terminée!');
      console.log('\n💡 Points clés pour le débogage:');
      console.log(
        '1. Vérifier que les annotations sont bien envoyées avec writeMessageAnnotation',
      );
      console.log(
        '2. Vérifier que le composant React détecte les annotations de type "search-progress"',
      );
      console.log(
        '3. Vérifier que le throttling (500ms) ne bloque pas les mises à jour',
      );
      console.log('4. Vérifier les logs dans la console du navigateur');
    }
  }, index * 1000);
});
