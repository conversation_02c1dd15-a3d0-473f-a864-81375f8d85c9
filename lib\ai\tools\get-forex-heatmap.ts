import { z } from 'zod';
import { tool } from 'ai';

// Define and export the tool configuration
export const getForexHeatmap = tool({
  description:
    'Display a forex heatmap showing currency strength and performance visualization',
  parameters: z.object({
    // No parameters needed for the heatmap as it shows a predefined view
  }),
  execute: async () => {
    // Return a simple object that will be used by the frontend to render the component
    return {
      type: 'forex_heatmap',
      data: {},
    };
  },
});
