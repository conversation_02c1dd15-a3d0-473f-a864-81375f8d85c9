'use client';

import { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
// Nous n'avons plus besoin d'importer l'icône X

interface ImageType {
  url: string;
  description?: string;
  searchQuery?: string; // Ajout du champ searchQuery pour identifier la requête d'origine
}

interface ImageGalleryProps {
  images: (ImageType | string)[];
  query?: string;
  maxPreviewImages?: number;
  initiallyExpanded?: boolean;
}

export function ImageGallery({
  images,
  query,
  maxPreviewImages = 4,
  initiallyExpanded = true,
}: ImageGalleryProps) {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [imageStatuses, setImageStatuses] = useState<
    Record<string, 'loading' | 'loaded' | 'error'>
  >({});
  const [showAllSources, setShowAllSources] = useState(false);
  const [filteredImages, setFilteredImages] = useState<ImageType[]>([]);
  const [imagesLoaded, setImagesLoaded] = useState(false);
  const [isExpanded, setIsExpanded] = useState(initiallyExpanded);
  // Nous n'avons plus besoin de ces états car nous ouvrons directement les liens

  // Convertir toutes les images en format uniforme avec useMemo pour éviter les re-calculs
  const normalizedImages: ImageType[] = useMemo(
    () => images.map((img) => (typeof img === 'string' ? { url: img } : img)),
    [images],
  );

  // Mémoriser les URLs d'images pour éviter les rechargements inutiles
  const imageUrlsRef = useRef<string[]>([]);

  // Fonction pour générer une clé unique à partir d'une URL d'image
  const generateImageKey = (url: string | undefined, index: number) => {
    // Vérifier si l'URL est définie
    if (!url) {
      return `img-undefined-${index}`;
    }

    try {
      // Extraire le nom du fichier ou la dernière partie de l'URL
      const urlParts = url.split('/');
      const fileName = urlParts[urlParts.length - 1].split('?')[0];
      // Utiliser une combinaison du nom de fichier et de l'index pour plus de sécurité
      return `img-${fileName.replace(/[^a-zA-Z0-9]/g, '')}-${index}`;
    } catch (error) {
      // En cas d'erreur, retourner une clé de secours
      console.error("Erreur lors de la génération de la clé d'image:", error);
      return `img-fallback-${index}`;
    }
  };

  // Mémoriser les URLs d'images pour éviter les comparaisons inutiles
  const currentImageUrls = useMemo(
    () => normalizedImages.map((img) => img.url),
    [normalizedImages],
  );

  // Callbacks mémorisés pour éviter les re-rendus inutiles
  const handleImageLoad = useCallback((imageUrl: string, index: number) => {
    const key = generateImageKey(imageUrl, index);
    setImageStatuses((prev) => {
      if (prev[key] === 'loaded') return prev; // Éviter les mises à jour inutiles
      return { ...prev, [key]: 'loaded' };
    });
  }, []);

  const handleImageError = useCallback((imageUrl: string, index: number) => {
    const key = generateImageKey(imageUrl, index);
    setImageStatuses((prev) => {
      if (prev[key] === 'error') return prev; // Éviter les mises à jour inutiles
      return { ...prev, [key]: 'error' };
    });
  }, []);

  // Précharger les images pour éviter les sursauts et filtrer celles qui ne peuvent pas être chargées
  useEffect(() => {
    // Vérifier si les images ont changé
    const previousImageUrls = imageUrlsRef.current;

    // Si les URLs sont identiques, ne rien faire
    if (
      previousImageUrls.length === currentImageUrls.length &&
      previousImageUrls.every(
        (url: string, i: number) => url === currentImageUrls[i],
      )
    ) {
      return;
    }

    // Mettre à jour la référence des URLs
    imageUrlsRef.current = currentImageUrls;

    // Créer un objet pour suivre les statuts initiaux
    const initialStatuses: Record<string, 'loading' | 'loaded' | 'error'> = {};

    // Initialiser tous les statuts à 'loading'
    normalizedImages.forEach((image, index) => {
      const key = generateImageKey(image.url, index);
      initialStatuses[key] = 'loading';
    });

    // Mettre à jour l'état une seule fois avec tous les statuts initiaux
    setImageStatuses(initialStatuses);

    // Initialiser les images filtrées avec toutes les images
    // Cela évite que les images disparaissent pendant le chargement
    setFilteredImages(normalizedImages);
    setImagesLoaded(false);

    // Tableau pour collecter les images chargées avec succès
    const successfullyLoadedImages: ImageType[] = [];
    let loadedCount = 0;
    const totalImages = normalizedImages.length;

    // Fonction pour précharger une image et suivre son statut
    const preloadImage = (image: ImageType, index: number): void => {
      const url = image.url;
      const key = generateImageKey(url, index);
      const img = new Image();

      const markAsLoaded = (loadedUrl: string) => {
        setImageStatuses(
          (prev: Record<string, 'loading' | 'loaded' | 'error'>) => ({
            ...prev,
            [key]: 'loaded',
          }),
        );

        // Ajouter l'image au tableau des images chargées avec succès
        successfullyLoadedImages.push({
          ...image,
          url: loadedUrl, // Utiliser l'URL qui a fonctionné (originale ou proxy)
        });

        // Vérifier si toutes les images ont été traitées
        loadedCount++;
        if (loadedCount === totalImages) {
          // Ne mettre à jour l'état que si des images ont été chargées avec succès
          if (successfullyLoadedImages.length > 0) {
            setFilteredImages(successfullyLoadedImages);
          }
          setImagesLoaded(true);
        }
      };

      img.onload = (): void => {
        markAsLoaded(url);
      };

      img.onerror = (): void => {
        // Essayer avec le proxy si l'image ne se charge pas
        if (!url.includes('/api/proxy-image')) {
          // Nettoyer l'URL si elle contient déjà des caractères encodés
          let urlToProxy = url;
          if (urlToProxy.includes('%')) {
            try {
              urlToProxy = decodeURIComponent(urlToProxy);
            } catch (e) {
              // Ignorer les erreurs de décodage
            }
          }

          const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(urlToProxy)}`;
          const proxyImg = new Image();

          proxyImg.onload = (): void => {
            markAsLoaded(proxyUrl);
          };

          proxyImg.onerror = (): void => {
            setImageStatuses(
              (prev: Record<string, 'loading' | 'loaded' | 'error'>) => ({
                ...prev,
                [key]: 'error',
              }),
            );

            // Incrémenter le compteur même pour les images qui ont échoué
            loadedCount++;
            if (loadedCount === totalImages) {
              // Ne mettre à jour l'état que si des images ont été chargées avec succès
              if (successfullyLoadedImages.length > 0) {
                setFilteredImages(successfullyLoadedImages);
              }
              setImagesLoaded(true);
            }
          };

          proxyImg.src = proxyUrl;
        } else {
          setImageStatuses(
            (prev: Record<string, 'loading' | 'loaded' | 'error'>) => ({
              ...prev,
              [key]: 'error',
            }),
          );

          // Incrémenter le compteur même pour les images qui ont échoué
          loadedCount++;
          if (loadedCount === totalImages) {
            // Ne mettre à jour l'état que si des images ont été chargées avec succès
            if (successfullyLoadedImages.length > 0) {
              setFilteredImages(successfullyLoadedImages);
            }
            setImagesLoaded(true);
          }
        }
      };

      img.src = url;
    };

    // Précharger toutes les images
    normalizedImages.forEach((image, index) => {
      preloadImage(image, index);
    });

    // Nettoyer les événements lors du démontage
    return () => {
      // Rien à nettoyer ici car les objets Image seront garbage-collectés
    };
  }, [currentImageUrls, normalizedImages]);

  // Pas d'effets liés au Carousel car nous n'utilisons plus ce composant

  // Si aucune image n'est fournie, afficher un message
  if (!images || images.length === 0) {
    return <div className="text-muted-foreground">Aucune image trouvée</div>;
  }

  // Si aucune image n'est disponible après le chargement, afficher un message
  if (imagesLoaded && filteredImages.length === 0) {
    return (
      <div className="text-muted-foreground">
        Aucune image n&apos;a pu être chargée
      </div>
    );
  }

  // Générer des sources basées sur les images ou utiliser le query
  const generateSources = () => {
    // Créer des sources qui correspondent à la capture d'écran
    const searchTerm = query || 'San Diego';

    return [
      {
        name: `${searchTerm} Photos | Photos by Ron Niebrugge`,
        url: `https://wildnatureimages.com/${encodeURIComponent(searchTerm.toLowerCase())}`,
        icon: 'W',
      },
      {
        name: `${searchTerm} Pictures, Images and Stock Photos`,
        url: `https://istockphoto.com/search?q=${encodeURIComponent(searchTerm)}`,
        icon: 'iS',
      },
      {
        name: `Historical Photo Gallery | City of ${searchTerm} Official Website`,
        url: `https://sandiego.gov/photos?q=${encodeURIComponent(searchTerm)}`,
        icon: '🔥',
      },
      // Sources supplémentaires qui seront affichées lors du clic sur "View more"
      {
        name: `${searchTerm} Travel Images | Unsplash`,
        url: `https://unsplash.com/s/photos/${encodeURIComponent(searchTerm.toLowerCase())}`,
        icon: 'U',
      },
      {
        name: `${searchTerm} Photography | Flickr`,
        url: `https://www.flickr.com/search/?text=${encodeURIComponent(searchTerm)}`,
        icon: 'F',
      },
      {
        name: `${searchTerm} Stock Photos | Shutterstock`,
        url: `https://www.shutterstock.com/search/${encodeURIComponent(searchTerm)}`,
        icon: 'S',
      },
      {
        name: `${searchTerm} Images | Getty Images`,
        url: `https://www.gettyimages.com/photos/${encodeURIComponent(searchTerm.toLowerCase())}`,
        icon: 'G',
      },
      {
        name: `${searchTerm} Free Photos | Pexels`,
        url: `https://www.pexels.com/search/${encodeURIComponent(searchTerm.toLowerCase())}`,
        icon: 'P',
      },
      {
        name: `${searchTerm} Images | Adobe Stock`,
        url: `https://stock.adobe.com/search?k=${encodeURIComponent(searchTerm)}`,
        icon: 'A',
      },
      {
        name: `${searchTerm} Free Images | Pixabay`,
        url: `https://pixabay.com/images/search/${encodeURIComponent(searchTerm.toLowerCase())}`,
        icon: 'Px',
      },
    ];
  };

  const sources = generateSources();

  return (
    <div className="rounded-lg bg-gray-950 text-white p-2 sm:p-4 mb-4 w-full max-w-full overflow-hidden">
      {/* Titre de la recherche avec nombre de résultats - responsive */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-2 gap-1 sm:gap-0">
        <div className="flex items-center">
          <svg
            className="size-3 mr-1 shrink-0"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M21 21L16.65 16.65"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="text-xs sm:text-sm">Images</span>
        </div>
        <div className="flex items-center">
          <svg
            className="size-3 mr-1 text-green-500 shrink-0"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M20 6L9 17L4 12"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="text-xs">
            {imagesLoaded ? filteredImages.length : normalizedImages.length}{' '}
            résultats
          </span>
          <button
            type="button"
            onClick={() => setIsExpanded(!isExpanded)}
            className="ml-1 p-0 bg-transparent border-0 cursor-pointer flex items-center justify-center"
            aria-label={
              isExpanded ? 'Rétracter les images' : 'Déployer les images'
            }
          >
            <svg
              className={`size-3 shrink-0 transition-transform duration-200 ${isExpanded ? '' : 'rotate-180'}`}
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 9L12 15L18 9"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Grille d'images - conditionnelle en fonction de isExpanded */}
      {isExpanded && (
        <div className="grid grid-cols-2 gap-1 sm:gap-2 mb-2 pb-1 max-w-full overflow-hidden">
          {/* Utiliser les images filtrées */}
          {filteredImages.slice(0, maxPreviewImages).map((image, index) => (
            <button
              key={generateImageKey(image.url, index)}
              type="button"
              className="w-full aspect-video cursor-pointer relative p-0 border-0 bg-transparent text-left rounded-md overflow-hidden"
              onClick={() => {
                setSelectedIndex(index);
                setDialogOpen(true);
              }}
              aria-label={`Voir l'image ${index + 1}`}
            >
              {/* Afficher un placeholder pendant le chargement */}
              {image.url &&
                imageStatuses[generateImageKey(image.url, index)] ===
                  'loading' && (
                  <div className="size-full flex items-center justify-center bg-gray-800">
                    <div className="animate-pulse size-6 rounded-full bg-gray-700" />
                  </div>
                )}

              {/* Afficher l'image si elle est chargée ou en cours de chargement */}
              <img
                src={
                  image.url
                    ? !image.url.includes('/api/proxy-image')
                      ? image.url
                      : `/api/proxy-image?url=${encodeURIComponent(image.url)}`
                    : ''
                }
                alt={image.description || `Contenu ${index + 1}`}
                className={`size-full object-cover ${
                  image.url &&
                  imageStatuses[generateImageKey(image.url, index)] ===
                    'loading'
                    ? 'opacity-0'
                    : 'opacity-100'
                }`}
                loading="lazy"
                onLoad={() => {
                  if (image.url) {
                    handleImageLoad(image.url, index);
                  }
                }}
                onError={() => {
                  if (image.url) {
                    handleImageError(image.url, index);
                  }
                }}
              />

              {/* Afficher le bouton "+" pour voir plus d'images */}
              {index === maxPreviewImages - 1 &&
                filteredImages.length > maxPreviewImages && (
                  <div className="absolute inset-0 bg-black/50 flex items-center justify-center text-white">
                    <div className="flex flex-col items-center">
                      <div className="text-xl sm:text-3xl mb-0 sm:mb-1">+</div>
                      <div className="text-xs sm:text-sm">
                        Voir {filteredImages.length - maxPreviewImages} de plus
                      </div>
                    </div>
                  </div>
                )}
            </button>
          ))}
        </div>
      )}

      {/* Dialog pour afficher l'image sélectionnée */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="w-[calc(100vw-2rem)] sm:max-w-3xl max-h-[90vh] overflow-hidden bg-gray-900 text-white border-gray-700 p-2 sm:p-4">
          <DialogHeader className="py-1 px-0">
            <DialogTitle className="text-sm sm:text-base">
              {query || 'Images'}
            </DialogTitle>
            {query && (
              <DialogDescription className="text-xs text-gray-300">
                {query}
              </DialogDescription>
            )}
          </DialogHeader>
          <div className="py-1 flex flex-col h-full overflow-hidden">
            {/* Affichage simple de l'image sélectionnée */}
            <div className="relative grow">
              {/* Image principale */}
              <div className="flex items-center justify-center h-full max-h-[50vh] bg-gray-800 overflow-hidden relative">
                {filteredImages[selectedIndex] &&
                  imageStatuses[
                    generateImageKey(
                      filteredImages[selectedIndex]?.url,
                      selectedIndex,
                    )
                  ] === 'loading' && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="animate-pulse size-8 rounded-full bg-gray-700" />
                    </div>
                  )}

                <img
                  src={
                    filteredImages[selectedIndex]?.url
                      ? !filteredImages[selectedIndex].url.includes(
                          '/api/proxy-image',
                        )
                        ? filteredImages[selectedIndex].url
                        : `/api/proxy-image?url=${encodeURIComponent(filteredImages[selectedIndex].url)}`
                      : ''
                  }
                  alt={
                    filteredImages[selectedIndex]?.description ||
                    `Contenu ${selectedIndex + 1}`
                  }
                  className="max-h-[50vh] max-w-full size-auto object-contain"
                  style={{ maxWidth: 'min(calc(100vw - 4rem), 100%)' }}
                  loading="lazy"
                />
              </div>

              {/* Boutons de navigation */}
              <div className="absolute inset-0 flex items-center justify-between p-1 sm:p-2 z-10">
                <button
                  type="button"
                  onClick={() =>
                    setSelectedIndex((prev) =>
                      prev > 0 ? prev - 1 : filteredImages.length - 1,
                    )
                  }
                  className="size-8 sm:size-10 rounded-full shadow focus:outline-none bg-black/70 hover:bg-black/90 text-white border-0 flex items-center justify-center"
                  aria-label="Image précédente"
                >
                  &#10094;
                </button>
                <button
                  type="button"
                  onClick={() =>
                    setSelectedIndex((prev) =>
                      prev < filteredImages.length - 1 ? prev + 1 : 0,
                    )
                  }
                  className="size-8 sm:size-10 rounded-full shadow focus:outline-none bg-black/70 hover:bg-black/90 text-white border-0 flex items-center justify-center"
                  aria-label="Image suivante"
                >
                  &#10095;
                </button>
              </div>
            </div>

            {/* Compteur d'images */}
            <div className="py-1 text-center text-xs text-gray-300">
              {selectedIndex + 1} sur {filteredImages.length}
            </div>

            {/* Description de l'image */}
            {filteredImages[selectedIndex]?.description && (
              <div className="mt-1 p-1 sm:p-2 text-xs bg-gray-800 rounded-md max-w-full overflow-hidden text-ellipsis line-clamp-2 sm:line-clamp-none">
                {filteredImages[selectedIndex].description}
              </div>
            )}

            {/* Miniatures des images (optionnel) */}
            <div className="mt-1 flex flex-wrap justify-center gap-1 pb-1 max-w-full overflow-hidden">
              {filteredImages.map((img, idx) => (
                <button
                  type="button"
                  key={generateImageKey(img.url, idx)}
                  onClick={() => setSelectedIndex(idx)}
                  className={`shrink-0 w-10 h-8 sm:w-14 sm:h-10 overflow-hidden rounded-md border-2 ${
                    selectedIndex === idx
                      ? 'border-blue-500'
                      : 'border-transparent'
                  }`}
                >
                  <img
                    src={
                      img.url
                        ? !img.url.includes('/api/proxy-image')
                          ? img.url
                          : `/api/proxy-image?url=${encodeURIComponent(img.url)}`
                        : ''
                    }
                    alt={`Miniature ${idx + 1}`}
                    className="size-full object-cover"
                    loading="lazy"
                  />
                </button>
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Nous avons supprimé le modal car nous ouvrons directement les liens */}

      {/* Section Sources - conditionnelle en fonction de isExpanded */}
      {isExpanded && (
        <div className="mt-2 max-w-full overflow-hidden">
          <div className="flex items-center mb-2">
            <svg
              className="size-4 mr-2 shrink-0"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M8 10H16"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M8 14H16"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M8 18H12"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <span className="text-sm font-medium">Sources</span>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-1.5 max-w-full">
            {/* Afficher les 3 premières sources ou toutes les sources si showAllSources est true */}
            {sources
              .slice(0, showAllSources ? sources.length : 3)
              .map((source) => (
                <div
                  key={`source-${source.name}-${source.url}`}
                  className="bg-gray-800 rounded-md p-1.5 text-xs max-w-full overflow-hidden"
                >
                  <a
                    href={source.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs font-medium truncate max-w-full cursor-pointer hover:text-blue-400 text-left bg-transparent border-0 p-0 w-full block"
                    title="Ouvrir la source dans un nouvel onglet"
                  >
                    {source.name}
                  </a>
                  <div className="flex items-center text-[10px] text-gray-400 max-w-full overflow-hidden">
                    {source.icon === 'W' ? (
                      <span className="size-3 mr-1 flex items-center justify-center">
                        <svg
                          width="10"
                          height="10"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <circle
                            cx="12"
                            cy="12"
                            r="10"
                            fill="#FFFFFF"
                            fillOpacity="0.2"
                          />
                          <path
                            d="M4.5 12.5L10 18L19.5 8.5"
                            stroke="white"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </span>
                    ) : source.icon === 'iS' ? (
                      <span className="size-3 mr-1 flex items-center justify-center text-[10px] font-bold">
                        iS
                      </span>
                    ) : source.icon.length > 1 ? (
                      <span className="size-3 mr-1 flex items-center justify-center text-[10px] font-bold">
                        {source.icon}
                      </span>
                    ) : (
                      <span className="size-3 mr-1 flex items-center justify-center">
                        <svg
                          width="10"
                          height="10"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <circle
                            cx="12"
                            cy="12"
                            r="10"
                            fill="#FFA500"
                            fillOpacity="0.2"
                          />
                          <path
                            d="M12 6V12L16 14"
                            stroke="#FFA500"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </span>
                    )}
                    <a
                      href={source.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="truncate hover:underline text-[10px] max-w-full overflow-hidden text-left cursor-pointer bg-transparent border-0 p-0 text-gray-400 hover:text-white block"
                      title={source.url}
                    >
                      {source.url}
                    </a>
                  </div>
                </div>
              ))}

            {/* Bouton "View more" qui n'apparaît que s'il y a plus de 3 sources et que toutes ne sont pas affichées */}
            {sources.length > 3 && !showAllSources && (
              <div className="bg-gray-800 rounded-md p-1.5 flex items-center justify-center">
                <button
                  type="button"
                  className="text-gray-300 hover:text-white text-xs"
                  onClick={() => setShowAllSources(true)}
                >
                  View {sources.length - 3} more
                </button>
              </div>
            )}

            {/* Bouton "View less" qui n'apparaît que si toutes les sources sont affichées */}
            {showAllSources && (
              <div className="bg-gray-800 rounded-md p-1.5 flex items-center justify-center">
                <button
                  type="button"
                  className="text-gray-300 hover:text-white text-xs"
                  onClick={() => setShowAllSources(false)}
                >
                  View less
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
