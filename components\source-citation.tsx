import React from 'react';
import { PanelRight } from 'lucide-react';
import { MagnifyingGlass } from '@phosphor-icons/react';
import { Button } from '@/components/ui/button';
import { useSourcesPanel, type Source } from '../hooks/use-sources-panel';

interface SourceCitationProps {
  sources: Source[];
}

export function SourceCitation({ sources }: SourceCitationProps) {
  const { setSources, setIsOpen } = useSourcesPanel();

  if (!sources || sources.length === 0) {
    return null;
  }

  // Deduplicate sources by URL
  const uniqueSources = sources.reduce((acc: Source[], current) => {
    const x = acc.find((item) => item.url === current.url);
    if (!x) {
      return acc.concat([current]);
    } else {
      return acc;
    }
  }, []);

  const handleOpenSourcesPanel = () => {
    setSources(uniqueSources);
    setIsOpen(true);
  };

  return (
    <div className="mt-4 border-t border-gray-200 dark:border-gray-800 pt-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="size-5 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
            <MagnifyingGlass className="size-3 text-blue-500" weight="bold" />
          </div>
          <span className="text-sm text-gray-700 dark:text-gray-300">
            {uniqueSources.length}{' '}
            {uniqueSources.length > 1 ? 'sources' : 'source'} disponible
            {uniqueSources.length > 1 ? 's' : ''}
          </span>
        </div>
        <Button
          variant="outline"
          size="sm"
          className="h-8 gap-1 text-xs"
          onClick={handleOpenSourcesPanel}
        >
          <PanelRight className="size-3.5" />
          Voir les sources
        </Button>
      </div>
    </div>
  );
}
