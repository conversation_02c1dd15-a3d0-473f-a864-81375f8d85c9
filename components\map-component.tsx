'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import type { Place, PlaceImage } from '@/lib/types';
import LocationSidebar from './LocationSidebar';
import ImageModal from './ImageModal';
import { useArtifactSelector } from '@/hooks/use-artifact';
import { useIsMobile } from '@/hooks/use-mobile';

interface MapComponentProps {
  places: Place[];
  query: string;
}

const MapComponent: React.FC<MapComponentProps> = ({ places, query }) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const markersRef = useRef<any[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [isMapReady, setIsMapReady] = useState(false);
  const [selectedPlaceId, setSelectedPlaceId] = useState<string | null>(null);

  // États pour le modal d'image
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [modalImages, setModalImages] = useState<PlaceImage[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [modalPlaceName, setModalPlaceName] = useState('');

  // Utiliser uniquement les places avec images Serper (pas de fallback)

  // Vérifier si un artifact est visible
  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

  // Détecter si on est sur mobile
  const isMobile = useIsMobile();

  // Générer un ID unique pour cette instance de carte
  const mapId = useRef(
    `map-${Math.random().toString(36).substring(2, 9)}`,
  ).current;

  // Fonction pour gérer le redimensionnement de la carte avec debounce
  const handleMapResize = useCallback(() => {
    if (resizeTimeoutRef.current) {
      clearTimeout(resizeTimeoutRef.current);
    }

    resizeTimeoutRef.current = setTimeout(() => {
      try {
        if (
          mapInstanceRef.current?.getContainer() &&
          document.contains(mapInstanceRef.current.getContainer())
        ) {
          console.log(`Map ${mapId}: Handling resize`);
          mapInstanceRef.current.invalidateSize();
        }
      } catch (resizeError) {
        console.warn(`Map ${mapId}: Error during resize:`, resizeError);
      }
    }, 150);
  }, [mapId]);

  // Fonctions pour gérer le modal d'image
  const openImageModal = useCallback(
    (images: PlaceImage[], index: number, placeName: string) => {
      setModalImages(images);
      setCurrentImageIndex(index);
      setModalPlaceName(placeName);
      setIsImageModalOpen(true);
    },
    [],
  );

  const closeImageModal = useCallback(() => {
    setIsImageModalOpen(false);
  }, []);

  const goToPreviousImage = useCallback(() => {
    setCurrentImageIndex((prev) =>
      prev > 0 ? prev - 1 : modalImages.length - 1,
    );
  }, [modalImages.length]);

  const goToNextImage = useCallback(() => {
    setCurrentImageIndex((prev) =>
      prev < modalImages.length - 1 ? prev + 1 : 0,
    );
  }, [modalImages.length]);

  const selectImage = useCallback((index: number) => {
    setCurrentImageIndex(index);
  }, []);

  // Fonction pour centrer la carte sur un lieu spécifique
  const handleLocationClick = useCallback(
    (place: Place) => {
      if (!mapInstanceRef.current) return;

      console.log(`Centering map on: ${place.title}`);
      setSelectedPlaceId(place.cid);

      // Centrer la carte sur le lieu sélectionné
      mapInstanceRef.current.setView([place.latitude, place.longitude], 15, {
        animate: false, // Désactiver l'animation pour éviter les erreurs
      });

      // Trouver et ouvrir le popup du marker correspondant
      const markerIndex = places.findIndex((p) => p.cid === place.cid);
      if (markerIndex !== -1 && markersRef.current[markerIndex]) {
        markersRef.current[markerIndex].openPopup();
      }
    },
    [places],
  );

  // Fonction pour gérer le clic sur un marker
  const handleMarkerClick = useCallback((place: Place) => {
    console.log(`Marker clicked: ${place.title}`);
    setSelectedPlaceId(place.cid);
  }, []);

  // Fonction pour initialiser la carte avec useCallback pour éviter les dépendances circulaires
  const initializeMap = useCallback(() => {
    console.log('Initializing map...');

    const L = window.L;
    if (!L) {
      console.error('Leaflet not loaded');
      return;
    }

    if (!mapRef.current) {
      console.error('Map container ref is not available');
      return;
    }

    try {
      // Cleanup any existing map instance
      if (mapInstanceRef.current) {
        console.log('Cleaning up existing map instance');
        try {
          mapInstanceRef.current.remove();
        } catch (cleanupError) {
          console.warn('Error during map cleanup:', cleanupError);
        }
        mapInstanceRef.current = null;
      }

      // Vérifier que le conteneur est toujours dans le DOM
      if (!document.contains(mapRef.current)) {
        console.error('Map container is not in the DOM');
        return;
      }

      // Get the first place coordinates or default to Paris
      const firstPlace = places[0];
      const initialLat = firstPlace?.latitude || 48.8566;
      const initialLng = firstPlace?.longitude || 2.3522;

      console.log(`Setting initial view to: [${initialLat}, ${initialLng}]`);

      // Create the map with mobile-responsive options
      const map = L.map(mapRef.current, {
        // Améliorer l'expérience mobile
        tap: true,
        touchZoom: true,
        zoomControl: !isMobile, // Masquer les contrôles de zoom sur mobile pour économiser l'espace
        attributionControl: !isMobile, // Masquer l'attribution sur mobile pour économiser l'espace
        // Désactiver les animations pour éviter les erreurs de position
        zoomAnimation: false,
        fadeAnimation: false,
        markerZoomAnimation: false,
      }).setView([initialLat, initialLng], isMobile ? 12 : 13);
      mapInstanceRef.current = map;

      // Ajouter les contrôles de zoom personnalisés pour mobile
      if (isMobile) {
        L.control
          .zoom({
            position: 'bottomright',
          })
          .addTo(map);

        // Attribution compacte pour mobile
        L.control
          .attribution({
            position: 'bottomleft',
            prefix: false,
          })
          .addTo(map);
      }

      // Add the OpenStreetMap tiles
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution:
          '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19,
      }).addTo(map);

      // Add markers for all places
      markersRef.current = places.map((place, placeIndex) => {
        // Créer le contenu des images si disponibles
        const imagesContent =
          place.images && place.images.length > 0
            ? `
            <div style="margin: 12px 0;">
              <div style="display: flex; gap: 8px; overflow-x: auto; max-width: 100%; padding: 4px 0;">
                ${place.images
                  .slice(0, 3)
                  .map(
                    (image, imageIndex) => `
                  <div style="flex-shrink: 0;">
                    <img
                      src="${image.thumbnail || image.url}"
                      alt="${image.description || place.title}"
                      style="
                        width: 80px;
                        height: 60px;
                        object-fit: cover;
                        border-radius: 6px;
                        border: 1px solid #ddd;
                        cursor: pointer;
                      "
                      data-place-index="${placeIndex}"
                      data-image-index="${imageIndex}"
                      class="popup-image-thumbnail"
                      title="${image.description || place.title}"
                    />
                  </div>
                `,
                  )
                  .join('')}
                ${
                  place.images.length > 3
                    ? `
                  <div
                    style="
                      flex-shrink: 0;
                      width: 80px;
                      height: 60px;
                      background: #f0f0f0;
                      border-radius: 6px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      font-size: 12px;
                      color: #666;
                      border: 1px solid #ddd;
                      cursor: pointer;
                    "
                    data-place-index="${placeIndex}"
                    data-image-index="0"
                    class="popup-image-more"
                    title="Voir toutes les images"
                  >
                    +${place.images.length - 3}
                  </div>
                `
                    : ''
                }
              </div>
            </div>
          `
            : '';

        // Créer un contenu de popup adaptatif pour mobile
        const popupContent = isMobile
          ? `
          <div style="min-width: 200px; max-width: 280px;">
            <strong style="font-size: 16px; line-height: 1.4;">${place.title}</strong><br>
            <div style="font-size: 14px; line-height: 1.3; margin: 8px 0;">
              ${place.address}
            </div>
            <div style="font-size: 13px; color: #666; margin: 4px 0;">
              ${place.category}
            </div>
            ${place.rating ? `<div style="font-size: 13px; margin: 4px 0;">⭐ ${place.rating}/5</div>` : ''}
            ${imagesContent}
            ${place.phoneNumber ? `<div style="margin: 8px 0;"><a href="tel:${place.phoneNumber}" style="color: #007AFF; text-decoration: none; font-size: 14px;">📞 Call</a></div>` : ''}
            ${place.website ? `<div style="margin: 8px 0;"><a href="${place.website}" target="_blank" style="color: #007AFF; text-decoration: none; font-size: 14px;">🌐 Website</a></div>` : ''}
          </div>
        `
          : `
          <div style="min-width: 250px; max-width: 350px;">
            <strong style="font-size: 18px;">${place.title}</strong><br>
            <div style="margin: 8px 0;">${place.address}</div>
            <div style="color: #666; margin: 4px 0;">${place.category}</div>
            ${place.rating ? `<div style="margin: 4px 0;">⭐ ${place.rating}/5</div>` : ''}
            ${imagesContent}
            ${place.phoneNumber ? `<div style="margin: 8px 0;">📞 ${place.phoneNumber}</div>` : ''}
            ${place.website ? `<div style="margin: 8px 0;"><a href="${place.website}" target="_blank" style="color: #007AFF;">🌐 Website</a></div>` : ''}
          </div>
        `;

        const marker = L.marker([place.latitude, place.longitude])
          .addTo(map)
          .bindPopup(popupContent, {
            maxWidth: isMobile ? 250 : 300,
            className: isMobile ? 'mobile-popup' : '',
          });

        // Ajouter un gestionnaire d'événement pour le clic sur le marker
        marker.on('click', () => {
          handleMarkerClick(place);
        });

        // Ajouter des event listeners pour les images dans les popups
        marker.on('popupopen', () => {
          // Attendre que le popup soit rendu
          setTimeout(() => {
            const popupElement = marker.getPopup()?.getElement();
            if (popupElement) {
              // Event listeners pour les miniatures d'images
              const imageThumbnails = popupElement.querySelectorAll(
                '.popup-image-thumbnail',
              );
              imageThumbnails.forEach((img: Element) => {
                img.addEventListener('click', (e: Event) => {
                  e.stopPropagation();
                  const placeIndex = Number.parseInt(
                    (e.target as HTMLElement).getAttribute(
                      'data-place-index',
                    ) || '0',
                  );
                  const imageIndex = Number.parseInt(
                    (e.target as HTMLElement).getAttribute(
                      'data-image-index',
                    ) || '0',
                  );
                  const targetPlace = places[placeIndex];
                  if (targetPlace?.images) {
                    openImageModal(
                      targetPlace.images,
                      imageIndex,
                      targetPlace.title,
                    );
                  }
                });
              });

              // Event listener pour le bouton "plus d'images"
              const moreButton =
                popupElement.querySelector('.popup-image-more');
              if (moreButton) {
                moreButton.addEventListener('click', (e: Event) => {
                  e.stopPropagation();
                  const placeIndex = Number.parseInt(
                    (e.target as HTMLElement).getAttribute(
                      'data-place-index',
                    ) || '0',
                  );
                  const targetPlace = places[placeIndex];
                  if (targetPlace?.images) {
                    openImageModal(targetPlace.images, 0, targetPlace.title);
                  }
                });
              }
            }
          }, 100);
        });

        return marker;
      });

      // If we have multiple markers, fit the map to show all of them
      if (markersRef.current.length > 1) {
        const group = L.featureGroup(markersRef.current);
        // Ajuster le padding selon la taille de l'écran
        const padding = isMobile ? 0.2 : 0.1;
        map.fitBounds(group.getBounds().pad(padding));
      }

      // Ajouter un gestionnaire de redimensionnement pour la fenêtre
      const resizeHandler = () => handleMapResize();
      window.addEventListener('resize', resizeHandler);
      window.addEventListener('orientationchange', resizeHandler);

      // Force a resize to ensure the map renders correctly
      setTimeout(() => {
        try {
          if (map?.getContainer() && document.contains(map.getContainer())) {
            map.invalidateSize();
          }
        } catch (resizeError) {
          console.warn('Error during map resize:', resizeError);
        }
      }, 100);

      // Nettoyer les gestionnaires d'événements lors de la destruction de la carte
      map.on('unload', () => {
        window.removeEventListener('resize', resizeHandler);
        window.removeEventListener('orientationchange', resizeHandler);
      });
    } catch (error) {
      console.error('Error initializing map:', error);
    }
  }, [places, isMobile, handleMapResize, handleMarkerClick]);

  // Effet pour charger Leaflet et initialiser la carte
  useEffect(() => {
    // Attendre que le DOM soit prêt
    const waitForDOM = () => {
      if (typeof window === 'undefined') {
        console.log('Window not defined yet, waiting...');
        setTimeout(waitForDOM, 100);
        return;
      }

      console.log('Window is defined, proceeding with map initialization');
      loadLeafletResources();
    };

    // Charger les ressources Leaflet
    const loadLeafletResources = () => {
      console.log('Loading Leaflet resources...');

      // Load Leaflet CSS
      if (!document.querySelector('link[href*="leaflet.css"]')) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
        link.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=';
        link.crossOrigin = '';
        document.head.appendChild(link);
      }

      // Load Leaflet JS
      if (typeof window !== 'undefined' && !window.L) {
        console.log('Loading Leaflet script...');
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
        script.crossOrigin = '';
        script.onload = () => {
          console.log('Leaflet script loaded successfully');
          setIsMapReady(true);
        };
        script.onerror = (e) => {
          console.error('Error loading Leaflet script:', e);
        };
        document.head.appendChild(script);
      } else {
        console.log('Leaflet already loaded');
        setIsMapReady(true);
      }
    };

    waitForDOM();

    // Cleanup function
    return () => {
      // Nettoyer le timeout de redimensionnement
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
        resizeTimeoutRef.current = null;
      }

      if (mapInstanceRef.current) {
        console.log(`Map ${mapId}: Cleaning up map instance on unmount`);
        try {
          // Supprimer tous les event listeners avant de détruire la carte
          mapInstanceRef.current.off();
          // Supprimer tous les layers et markers
          mapInstanceRef.current.eachLayer((layer: any) => {
            mapInstanceRef.current.removeLayer(layer);
          });
          // Détruire la carte
          mapInstanceRef.current.remove();
        } catch (cleanupError) {
          console.warn(`Map ${mapId}: Error during cleanup:`, cleanupError);
        } finally {
          mapInstanceRef.current = null;
        }
      }

      // Supprimer les styles spécifiques à cette carte
      const styleElement = document.getElementById(`map-style-${mapId}`);
      if (styleElement) {
        console.log(`Map ${mapId}: Removing style element on unmount`);
        styleElement.remove();
      }
    };
  }, [mapId]);

  // Effet pour gérer la carte lorsqu'un artifact est ouvert
  useEffect(() => {
    if (containerRef.current) {
      // Toujours afficher la carte, qu'un artifact soit visible ou non
      containerRef.current.style.display = 'block';

      // Ajouter une classe spéciale lorsqu'un artifact est visible
      if (isArtifactVisible) {
        console.log(`Map ${mapId}: Artifact is visible, adding special class`);
        containerRef.current.classList.add('map-with-artifact');

        // Ajouter un attribut data-map-id pour identifier cette carte
        containerRef.current.setAttribute('data-map-id', mapId);

        // Ajouter un style pour éviter la duplication visuelle
        const style = document.createElement('style');
        style.id = `map-style-${mapId}`;
        style.textContent = `
          /* Style pour éviter la duplication visuelle des cartes */
          .map-with-artifact[data-map-id="${mapId}"] {
            position: relative;
            z-index: 10;
          }

          /* Assurer que la carte reste visible mais n'interfère pas avec l'artifact */
          .map-with-artifact[data-map-id="${mapId}"] .leaflet-container {
            opacity: 1;
            pointer-events: none; /* Désactiver les interactions avec la carte lorsqu'un artifact est ouvert */
          }

          /* Empêcher la duplication des contrôles de zoom */
          .map-with-artifact[data-map-id="${mapId}"] .leaflet-control-container {
            display: none;
          }

          /* Assurer que la carte ne se superpose pas à l'artifact */
          .map-with-artifact[data-map-id="${mapId}"] {
            transform: translateZ(0);
            will-change: transform;
          }

          /* Styles responsifs pour les popups mobiles */
          .mobile-popup .leaflet-popup-content-wrapper {
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          }

          .mobile-popup .leaflet-popup-content {
            margin: 12px 16px;
            line-height: 1.4;
          }

          /* Améliorer les contrôles sur mobile */
          @media (max-width: 768px) {
            .leaflet-control-zoom {
              border: none;
              box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }

            .leaflet-control-zoom a {
              width: 36px;
              height: 36px;
              line-height: 36px;
              font-size: 18px;
              border-radius: 6px;
              margin: 2px;
            }

            .leaflet-control-attribution {
              font-size: 10px;
              background: rgba(255,255,255,0.8);
              border-radius: 4px;
              padding: 2px 4px;
            }
          }
        `;
        document.head.appendChild(style);
      } else {
        console.log(
          `Map ${mapId}: Artifact is not visible, removing special class`,
        );
        containerRef.current.classList.remove('map-with-artifact');

        // Supprimer le style spécifique si l'artifact est fermé
        const styleElement = document.getElementById(`map-style-${mapId}`);
        if (styleElement) {
          styleElement.remove();
        }
      }

      // Si la carte n'est pas initialisée et que les conditions sont réunies, l'initialiser
      if (isMapReady && places.length > 0 && !mapInstanceRef.current) {
        console.log(`Map ${mapId}: Initializing map`);
        initializeMap();
      }

      // Si la carte est déjà initialisée, s'assurer qu'elle est correctement dimensionnée
      if (mapInstanceRef.current) {
        // Forcer un redimensionnement de la carte pour s'assurer qu'elle s'affiche correctement
        setTimeout(() => {
          try {
            if (
              mapInstanceRef.current?.getContainer() &&
              document.contains(mapInstanceRef.current.getContainer())
            ) {
              console.log(
                `Map ${mapId}: Invalidating map size to ensure proper display`,
              );
              mapInstanceRef.current.invalidateSize();
            }
          } catch (resizeError) {
            console.warn(
              `Map ${mapId}: Error during size invalidation:`,
              resizeError,
            );
          }
        }, 100);
      }
    }
  }, [isMapReady, places, initializeMap, isArtifactVisible, mapId]);

  // Effet pour initialiser la carte une fois que Leaflet est chargé
  useEffect(() => {
    if (!isMapReady || places.length === 0) {
      return;
    }

    console.log('Leaflet is ready and places are available, initializing map');

    // Attendre que la référence du conteneur soit disponible
    const waitForRef = () => {
      if (!mapRef.current) {
        console.log('Map container ref not available yet, waiting...');
        setTimeout(waitForRef, 100);
        return;
      }

      console.log('Map container ref is available, initializing map');
      initializeMap();
    };

    waitForRef();
  }, [isMapReady, places, initializeMap]);

  if (places.length === 0) {
    return null;
  }

  return (
    <div
      ref={containerRef}
      className="mt-4 rounded-lg overflow-hidden shadow-lg chat-map"
      id={mapId}
      data-map-id={mapId}
    >
      <div className="bg-white dark:bg-gray-800 p-2 sm:p-4">
        <h2 className="text-base sm:text-lg font-semibold mb-2 text-black dark:text-white">
          Map Results for: {query}
        </h2>
        <div
          ref={mapRef}
          className={`
            h-[250px] sm:h-[300px] md:h-[400px] lg:h-[450px]
            w-full rounded-lg border border-gray-200 dark:border-gray-700
            map-container-${mapId}
            transition-all duration-300 ease-in-out
          `}
          data-map-container-id={mapId}
        />
      </div>
      <LocationSidebar
        places={places}
        selectedPlaceId={selectedPlaceId}
        onLocationClick={handleLocationClick}
      />

      {/* Modal d'image */}
      <ImageModal
        images={modalImages}
        currentIndex={currentImageIndex}
        isOpen={isImageModalOpen}
        onClose={closeImageModal}
        onPrevious={goToPreviousImage}
        onNext={goToNextImage}
        onImageSelect={selectImage}
        placeName={modalPlaceName}
      />
    </div>
  );
};

export default MapComponent;
