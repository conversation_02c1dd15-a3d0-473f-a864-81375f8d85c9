/**
 * Core types for trip planning workflows
 * These types are shared between different workflow implementations
 */

/**
 * Interface for currency denomination (bills and coins)
 */
export interface CurrencyDenomination {
  value: number;
  type: 'bill' | 'coin';
  description?: string;
  color?: string;
  material?: string;
  size?: string;
  imageUrl?: string;
}

/**
 * Interface for currency information
 */
export interface CurrencyInfo {
  code: string;
  symbol: string;
  name: string;
  country: string;
  denominations?: {
    bills: CurrencyDenomination[];
    coins: CurrencyDenomination[];
  };
}

/**
 * Interface for destination information
 */
export interface DestinationInfo {
  destination: string;
  country: string;
  duration: number | null;
  coordinates: {
    lat: string;
    lng: string;
  };
}

/**
 * Interface for a single activity in a day
 */
export interface Activity {
  time: string;
  activity: string;
  location: string;
  description: string;
  poiIndex?: number;
}

/**
 * Interface for a day's itinerary
 */
export interface DayItinerary {
  day: number;
  activities: Activity[];
}

/**
 * Interface for a point of interest
 */
export interface PointOfInterest {
  name: string;
  lat: string;
  lng: string;
  day: string;
  type: 'attraction' | 'restaurant' | 'hotel';
  description: string;
}

/**
 * Interface for local phrases
 */
export interface LocalPhrase {
  phrase: string;
  translation: string;
  pronunciation: string;
}

/**
 * Interface for travel tips
 */
export interface TravelTip {
  category: string;
  tips: string[];
}

/**
 * Interface for budget information
 */
export interface BudgetItem {
  category: string;
  estimatedCost: string;
  notes: string;
}

/**
 * Base interface for trip plan
 */
export interface TripPlan {
  destination: DestinationInfo;
  days: DayItinerary[];
  pois: PointOfInterest[];
  localPhrases: LocalPhrase[];
  travelTips: TravelTip[];
  budget: BudgetItem[];
  heroImage?: string;
  specializedActivityData?: any;
  tripClassification?: any;
}
