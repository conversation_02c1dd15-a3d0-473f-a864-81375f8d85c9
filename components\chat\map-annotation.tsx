'use client';

import React from 'react';
import type { MessageAnnotation } from '@/types/ai-extensions';
import MapComponent from '../map-component';
import type { Place } from '@/lib/types';

interface MapAnnotationProps {
  annotation: MessageAnnotation;
}

const MapAnnotation: React.FC<MapAnnotationProps> = ({ annotation }) => {
  if (annotation.type !== 'map_display' || !annotation.data) {
    return null;
  }

  const { query, places } = annotation.data as {
    query: string;
    places: Place[];
  };

  if (!places || places.length === 0) {
    return (
      <div className="mt-4 p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-800 rounded-lg">
        <p className="text-red-700 dark:text-red-200">
          No places found for &quot;{query}&quot;.
        </p>
      </div>
    );
  }

  return <MapComponent places={places} query={query} />;
};

export default MapAnnotation;
