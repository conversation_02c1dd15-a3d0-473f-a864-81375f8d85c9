import { z } from 'zod';
import { generateObject, type LanguageModelV1 } from 'ai';
import type { DestinationInfo, DayItinerary } from '../types';

/**
 * ItineraryAgent is responsible for generating a day-by-day itinerary
 * for the trip, including activities, local phrases, travel tips, and budget information.
 */
export class ItineraryAgent {
  private model: LanguageModelV1;

  constructor(model: LanguageModelV1) {
    this.model = model;
  }

  /**
   * Generate a complete itinerary for the trip
   */
  async generateItinerary(destinationInfo: DestinationInfo) {
    try {
      const { destination, country, duration } = destinationInfo;

      // Check if duration is null or undefined
      if (duration === null || duration === undefined) {
        console.error(
          'Duration is null or undefined. Cannot generate itinerary.',
        );
        return this.generateFallbackItinerary(destinationInfo);
      }

      // Generate the day-by-day itinerary
      const { object: itineraryData } = await generateObject({
        model: this.model,
        system: `You are an expert travel planner with deep knowledge of destinations worldwide.
        Create a detailed day-by-day itinerary for the specified destination and duration.
        Include a variety of activities for each day, with specific times, locations, and descriptions.
        Also include local phrases, travel tips, and budget information.
        Be specific, realistic, and consider the logistics of the destination.
        Ensure activities are properly timed with reasonable travel times between locations.`,
        prompt: `Create a detailed travel itinerary for ${destination}, ${country} for ${duration} days.
        Include:
        1. A day-by-day schedule with specific times and activities
        2. Local phrases with translations and pronunciations
        3. Travel tips for the destination
        4. Budget information with estimated costs`,
        schema: z.object({
          days: z
            .array(
              z.object({
                day: z.number().describe('Day number'),
                activities: z
                  .array(
                    z.object({
                      time: z
                        .string()
                        .describe('Time of the activity (e.g., "09:00")'),
                      activity: z.string().describe('Name of the activity'),
                      location: z.string().describe('Location of the activity'),
                      description: z
                        .string()
                        .describe('Description of the activity'),
                    }),
                  )
                  .min(4)
                  .describe('Activities for the day'),
              }),
            )
            .length(duration)
            .describe('Day-by-day itinerary'),
          localPhrases: z
            .array(
              z.object({
                phrase: z.string().describe('Phrase in local language'),
                translation: z.string().describe('English translation'),
                pronunciation: z.string().describe('Pronunciation guide'),
              }),
            )
            .min(5)
            .describe('Useful local phrases'),
          travelTips: z
            .array(
              z.object({
                category: z
                  .string()
                  .describe(
                    'Category of the tip (e.g., "Transportation", "Safety")',
                  ),
                tips: z
                  .array(z.string())
                  .min(2)
                  .describe('Tips in this category'),
              }),
            )
            .min(3)
            .describe('Travel tips for the destination'),
          budget: z
            .array(
              z.object({
                category: z
                  .string()
                  .describe('Expense category (e.g., "Accommodation", "Food")'),
                estimatedCost: z.string().describe('Estimated cost range'),
                notes: z
                  .string()
                  .describe('Additional notes about this expense category'),
              }),
            )
            .min(4)
            .describe('Budget information'),
        }),
      });

      return itineraryData;
    } catch (error) {
      console.error('Error generating itinerary:', error);

      // Return a fallback itinerary if generation fails
      return this.generateFallbackItinerary(destinationInfo);
    }
  }

  /**
   * Generate a fallback itinerary in case the main generation fails
   */
  private generateFallbackItinerary(destinationInfo: DestinationInfo) {
    const { destination, duration } = destinationInfo;

    // Use a default duration of 3 days if duration is null
    const actualDuration = duration !== null ? duration : 3;
    console.log(
      `Using fallback duration of ${actualDuration} days for itinerary generation`,
    );

    // Create a basic itinerary structure
    const days: DayItinerary[] = [];

    for (let i = 1; i <= actualDuration; i++) {
      days.push({
        day: i,
        activities: [
          {
            time: '09:00',
            activity: 'Breakfast',
            location: 'Local café',
            description: 'Start your day with a delicious local breakfast',
          },
          {
            time: '10:30',
            activity: 'Sightseeing',
            location: `${destination} city center`,
            description: 'Explore the main attractions in the city center',
          },
          {
            time: '13:00',
            activity: 'Lunch',
            location: 'Local restaurant',
            description: 'Enjoy local cuisine for lunch',
          },
          {
            time: '15:00',
            activity: 'Cultural visit',
            location: 'Museum or historical site',
            description: 'Visit a cultural or historical landmark',
          },
          {
            time: '19:00',
            activity: 'Dinner',
            location: 'Restaurant',
            description: 'Dinner at a recommended restaurant',
          },
        ],
      });
    }

    return {
      days,
      localPhrases: [
        {
          phrase: 'Hello',
          translation: 'Hello',
          pronunciation: 'Hello',
        },
        {
          phrase: 'Thank you',
          translation: 'Thank you',
          pronunciation: 'Thank you',
        },
        {
          phrase: 'Yes',
          translation: 'Yes',
          pronunciation: 'Yes',
        },
        {
          phrase: 'No',
          translation: 'No',
          pronunciation: 'No',
        },
        {
          phrase: 'Excuse me',
          translation: 'Excuse me',
          pronunciation: 'Excuse me',
        },
      ],
      travelTips: [
        {
          category: 'Transportation',
          tips: [
            'Use public transportation when possible',
            'Taxis are available throughout the city',
          ],
        },
        {
          category: 'Safety',
          tips: [
            'Keep your belongings secure',
            'Be aware of your surroundings',
          ],
        },
        {
          category: 'Weather',
          tips: [
            'Check the weather forecast before heading out',
            'Bring appropriate clothing',
          ],
        },
      ],
      budget: [
        {
          category: 'Accommodation',
          estimatedCost: '$100-200 per night',
          notes: 'Prices vary depending on location and season',
        },
        {
          category: 'Food',
          estimatedCost: '$30-50 per day',
          notes: 'Street food is cheaper than restaurants',
        },
        {
          category: 'Transportation',
          estimatedCost: '$10-20 per day',
          notes: 'Public transportation is the most economical option',
        },
        {
          category: 'Activities',
          estimatedCost: '$20-50 per day',
          notes: 'Many attractions offer discounts for students and seniors',
        },
      ],
    };
  }
}
