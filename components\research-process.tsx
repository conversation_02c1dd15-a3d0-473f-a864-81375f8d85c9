'use client';

import { useStreamingAnnotations } from '@/lib/hooks/use-streaming-annotations';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Search, ChevronDown, ChevronRight, ExternalLink, MapPin } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useState, useMemo, useEffect } from 'react';

// Custom hook for geolocation
function useLocation() {
  const [location, setLocation] = useState<{
    latitude: number | null;
    longitude: number | null;
    error: string | null;
    isLoading: boolean;
  }>({
    latitude: null,
    longitude: null,
    error: null,
    isLoading: false,
  });

  useEffect(() => {
    if (!navigator.geolocation) {
      setLocation(prev => ({
        ...prev,
        error: 'Geolocation is not supported by your browser',
      }));
      return;
    }

    setLocation(prev => ({ ...prev, isLoading: true }));

    const handleSuccess = (position: GeolocationPosition) => {
      setLocation({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        error: null,
        isLoading: false,
      });
    };

    const handleError = (error: GeolocationPositionError) => {
      setLocation(prev => ({
        ...prev,
        error: error.message,
        isLoading: false,
      }));
    };

    const options = {
      enableHighAccuracy: true,
      timeout: 5000,
      maximumAge: 0,
    };

    const watchId = navigator.geolocation.watchPosition(
      handleSuccess,
      handleError,
      options
    );

    return () => {
      navigator.geolocation.clearWatch(watchId);
    };
  }, []);

  return location;
}

interface ResearchProcessProps {
  toolCallId: string;
  className?: string;
}

export function ResearchProcess({ toolCallId, className = '' }: ResearchProcessProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const { annotations, isLoading, error } = useStreamingAnnotations({
    toolCallId,
    enabled: true,
  });

  // Memoize filtered annotations for better performance
  const { searchQueries, sources, contents } = useMemo(() => ({
    searchQueries: annotations.filter(a => a.type === 'search_query'),
    sources: annotations.filter(a => a.type === 'source'),
    contents: annotations.filter(a => a.type === 'content')
  }), [annotations]);

  // Get user's location
  const { latitude, longitude, error: locationError, isLoading: isLoadingLocation } = useLocation();

  // Don't show anything if no annotations and not loading
  if (!isLoading && annotations.length === 0) {
    return null;
  }

  return (
    <div className={`border rounded-lg overflow-hidden bg-white dark:bg-neutral-900 shadow-sm ${className}`}>
      <button 
        type="button"
        className="w-full flex items-center justify-between p-3 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg"
        onClick={() => setIsExpanded(!isExpanded)}
        aria-expanded={isExpanded}
        aria-controls="research-process-content"
      >
        <div className="flex items-center space-x-2">
          <Search className="h-4 w-4 text-neutral-500" />
          <h3 className="font-medium text-sm">Research Process</h3>
          <Badge variant="outline" className="ml-2 h-5 px-1.5 text-xs">
            {annotations.length} {annotations.length === 1 ? 'event' : 'events'}
          </Badge>
        </div>
        {isExpanded ? (
          <ChevronDown className="h-4 w-4 text-neutral-500" />
        ) : (
          <ChevronRight className="h-4 w-4 text-neutral-500" />
        )}
      </button>
      
      <AnimatePresence>
        {isExpanded && (
          <div id="research-process-content">
            {/* Location information */}
            {(latitude || locationError) && (
              <div className="px-4 pb-3 -mt-1">
                <div className="text-xs text-neutral-500 dark:text-neutral-400 flex items-center">
                  <MapPin className="h-3.5 w-3.5 mr-1.5 flex-shrink-0" />
                  {isLoadingLocation ? (
                    <span>Detecting your location...</span>
                  ) : locationError ? (
                    <span>Location: {locationError}</span>
                  ) : (
                    <span>
                      Location: {latitude?.toFixed(4)}, {longitude?.toFixed(4)}
                    </span>
                  )}
                </div>
              </div>
            )}
            
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="p-3 pt-0 space-y-4">
                {isLoading && annotations.length === 0 ? (
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Search Queries Section */}
                    {searchQueries.length > 0 && (
                      <div>
                        <h4 className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider mb-2">
                          Search Queries
                        </h4>
                        <div className="space-y-1.5">
                          {searchQueries.map((query) => (
                            <div 
                              key={query.id} 
                              className="p-2 bg-neutral-50 dark:bg-neutral-800/50 rounded text-sm flex items-start"
                            >
                              <span className="text-neutral-400 mr-2">🔍</span>
                              <span className="flex-1">{query.query}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Sources Section */}
                    {sources.length > 0 && (
                      <div>
                        <h4 className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider mb-2">
                          Sources Found
                        </h4>
                        <div className="space-y-1.5">
                          {sources.map((source) => (
                            <a
                              key={source.id}
                              href={source.source?.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="block p-2 bg-neutral-50 dark:bg-neutral-800/50 rounded text-sm hover:bg-neutral-100 dark:hover:bg-neutral-700/70 transition-colors group"
                            >
                              <div className="flex items-start">
                                <span className="text-neutral-400 mr-2 mt-0.5">
                                  <ExternalLink className="h-3.5 w-3.5" />
                                </span>
                                <span className="flex-1 group-hover:underline underline-offset-2">
                                  {source.source?.title || source.source?.url}
                                </span>
                              </div>
                              {source.source?.url && (
                                <div className="text-xs text-neutral-400 truncate mt-1 ml-5">
                                  {new URL(source.source.url).hostname.replace('www.', '')}
                                </div>
                              )}
                            </a>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Contents Section */}
                    {contents.length > 0 && (
                      <div>
                        <h4 className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider mb-2">
                          Content Processed
                        </h4>
                        <div className="space-y-2">
                          {contents.map((content) => (
                            <div 
                              key={content.id} 
                              className="p-2 bg-neutral-50 dark:bg-neutral-800/50 rounded text-sm"
                            >
                              <div className="flex items-start">
                                <span className="text-neutral-400 mr-2 mt-0.5">📄</span>
                                <div className="flex-1">
                                  <div className="font-medium">{content.content?.title || 'Untitled'}</div>
                                  {content.content?.text && (
                                    <div className="text-xs text-neutral-500 dark:text-neutral-400 mt-1 line-clamp-2">
                                      {content.content.text}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>
    </div>
  );
}
