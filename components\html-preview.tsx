'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import { LoaderIcon } from './icons';
import { useHtmlCache } from '@/hooks/use-html-cache';

interface HtmlContent {
  htmlContent: string;
  cssContent: string;
  jsContent: string;
}

interface HtmlPreviewProps {
  content: string;
  title: string;
  documentId: string; // Ajout de l'ID du document pour une meilleure traçabilité
}

export function HtmlPreview({ content, title, documentId }: HtmlPreviewProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [refreshKey, setRefreshKey] = useState(0);

  // Utiliser notre hook de cache pour préserver le contenu entre les rendus
  const { cachedContent, updateCache } = useHtmlCache(documentId, content);
  const contentRef = useRef<string>(cachedContent);

  // Log pour déboguer
  console.log('HtmlPreview - rendering with:', {
    documentId,
    title,
    contentLength: content?.length || 0,
    cachedContentLength: cachedContent?.length || 0,
    isSameContent: content === cachedContent,
  });

  // Fonction pour parser le contenu HTML
  const parseHtmlContent = useCallback(() => {
    try {
      // Log pour déboguer
      console.log('HtmlPreview - parsing content:', {
        contentRefLength: contentRef.current?.length || 0,
        contentRefSample: `${contentRef.current?.substring(0, 50)}...`,
      });

      // Parser le contenu JSON
      let parsedContent: HtmlContent;

      try {
        parsedContent = JSON.parse(contentRef.current || '{}') as HtmlContent;
      } catch (e) {
        console.error('HtmlPreview - Failed to parse JSON content', e);

        // Si le contenu n'est pas un JSON valide mais ressemble à du HTML, essayons de le convertir
        if (contentRef.current?.includes('<')) {
          console.log(
            'HtmlPreview - Content looks like HTML, creating HTML artifact',
          );
          parsedContent = {
            htmlContent: contentRef.current,
            cssContent: '',
            jsContent: '',
          };
        } else {
          throw new Error('Invalid JSON content');
        }
      }

      // Vérifier que le contenu est valide
      if (
        !parsedContent.htmlContent &&
        !parsedContent.cssContent &&
        !parsedContent.jsContent
      ) {
        console.warn('HtmlPreview - parsed content is missing required fields');

        // Si le contenu est vide, créer un contenu par défaut
        parsedContent = {
          htmlContent: '<div id="app">No content available</div>',
          cssContent: 'body { font-family: system-ui; }',
          jsContent: 'console.log("Default HTML content loaded");',
        };

        console.log('HtmlPreview - Created default HTML content');
      }

      // Créer un document HTML complet
      return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${title}</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }
    ${parsedContent.cssContent || ''}
  </style>
</head>
<body>
  ${parsedContent.htmlContent || '<div id="app">No HTML content</div>'}
  <script>
    try {
      ${parsedContent.jsContent || ''}
    } catch (e) {
      console.error('Error executing JavaScript:', e);
    }
  </script>
</body>
</html>`;
    } catch (e) {
      console.error('Failed to parse HTML content', e, contentRef.current);
      setError(
        `Failed to parse HTML content: ${e instanceof Error ? e.message : 'Unknown error'}`,
      );

      // Retourner un HTML d'erreur
      return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Error</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      color: #ff0000;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
    }
  </style>
</head>
<body>
  <div>
    <h2>Error loading HTML content</h2>
    <p>Failed to parse the HTML artifact content.</p>
    <p>Error: ${e instanceof Error ? e.message : 'Unknown error'}</p>
    <p>Document ID: ${documentId}</p>
  </div>
</body>
</html>`;
    }
  }, [title, documentId]);

  // Fonction pour rafraîchir le contenu
  const refreshContent = useCallback(() => {
    setIsLoading(true);
    setError(null);

    try {
      const html = parseHtmlContent();
      setHtmlContent(html);

      // Mettre à jour le cache avec le contenu actuel
      updateCache(contentRef.current);
    } catch (e) {
      console.error('Error refreshing content', e);
      setError('Error refreshing content');
    } finally {
      setIsLoading(false);
    }
  }, [parseHtmlContent, updateCache]);

  // Mettre à jour le contenu lorsque les props changent
  useEffect(() => {
    // Vérifier si le contenu a changé
    if (cachedContent !== contentRef.current) {
      contentRef.current = cachedContent;
      refreshContent();
    }
  }, [cachedContent, refreshContent]);

  // Initialiser le contenu au montage du composant
  useEffect(() => {
    refreshContent();
  }, [refreshKey, refreshContent]);

  // Charger le contenu du document depuis l'API si nécessaire
  useEffect(() => {
    // Ne charger que si on a un ID de document et que le contenu est vide ou minimal
    if (documentId && (!contentRef.current || contentRef.current === '{}')) {
      const fetchDocument = async () => {
        setIsLoading(true);
        setError(null);

        try {
          console.log(
            `HtmlPreview - Fetching document from API: /api/document?id=${documentId}`,
          );

          // Créer un contenu HTML par défaut au cas où le document ne serait pas trouvé
          const defaultHtmlContent = {
            htmlContent: `<div id="app" style="display: flex; justify-content: center; align-items: center; height: 100vh; flex-direction: column;">
              <h1 style="font-size: 24px; margin-bottom: 16px;">HTML Preview</h1>
              <p>Document ID: ${documentId}</p>
            </div>`,
            cssContent: 'body { font-family: system-ui; }',
            jsContent: 'console.log("Default HTML content loaded");',
          };

          try {
            const response = await fetch(`/api/document?id=${documentId}`);

            if (!response.ok) {
              console.warn(
                `HtmlPreview - Failed to fetch document: ${response.statusText}`,
              );

              // Utiliser le contenu par défaut si le document n'est pas trouvé
              console.log(
                `HtmlPreview - Using default content for document ${documentId}`,
              );
              contentRef.current = JSON.stringify(defaultHtmlContent);
              updateCache(contentRef.current);
              refreshContent();
              return;
            }

            // L'API existante renvoie un tableau de documents
            const documents = await response.json();

            // Vérifier si nous avons reçu un tableau ou un objet unique
            const document = Array.isArray(documents)
              ? documents[0]
              : documents;

            if (!document) {
              console.warn(
                `HtmlPreview - Document not found or empty response`,
              );

              // Utiliser le contenu par défaut si le document n'est pas trouvé
              console.log(
                `HtmlPreview - Using default content for document ${documentId}`,
              );
              contentRef.current = JSON.stringify(defaultHtmlContent);
              updateCache(contentRef.current);
              refreshContent();
              return;
            }

            console.log(`HtmlPreview - Document fetched:`, {
              id: document.id,
              kind: document.kind || document.text, // Utiliser text si kind n'existe pas
              contentLength: document.content?.length || 0,
              title: document.title,
            });

            if (document.content) {
              // Vérifier que le document est bien un HTML artifact
              const documentKind = document.kind || document.text || 'unknown';
              if (documentKind !== 'html') {
                console.warn(
                  `HtmlPreview - Document ${documentId} has kind=${documentKind}, expected 'html'`,
                );
              }

              // Vérifier et convertir le contenu si nécessaire
              let finalContent = document.content;
              try {
                // Vérifier que le contenu est un JSON valide
                const parsedContent = JSON.parse(document.content);
                if (
                  !parsedContent ||
                  typeof parsedContent !== 'object' ||
                  (!parsedContent.htmlContent &&
                    !parsedContent.cssContent &&
                    !parsedContent.jsContent)
                ) {
                  console.warn(
                    `HtmlPreview - Document ${documentId} content is not a valid HTML artifact`,
                  );

                  // Si le contenu n'est pas un HTML artifact valide, essayons de le convertir
                  if (
                    typeof document.content === 'string' &&
                    document.content.includes('<')
                  ) {
                    // Le contenu semble être du HTML brut, créons un HTML artifact
                    const htmlArtifact = {
                      htmlContent: document.content,
                      cssContent: '',
                      jsContent: '',
                    };
                    console.log(
                      `HtmlPreview - Converting raw HTML to HTML artifact`,
                    );
                    finalContent = JSON.stringify(htmlArtifact);
                  }
                }
              } catch (e) {
                console.error(
                  `HtmlPreview - Document ${documentId} content is not valid JSON`,
                  e,
                );

                // Si le contenu n'est pas un JSON valide, essayons de le convertir
                if (
                  typeof document.content === 'string' &&
                  document.content.includes('<')
                ) {
                  // Le contenu semble être du HTML brut, créons un HTML artifact
                  const htmlArtifact = {
                    htmlContent: document.content,
                    cssContent: '',
                    jsContent: '',
                  };
                  console.log(
                    `HtmlPreview - Converting raw HTML to HTML artifact`,
                  );
                  finalContent = JSON.stringify(htmlArtifact);
                }
              }

              // Mettre à jour le contenu
              console.log(
                `HtmlPreview - Updating cache with content of length ${finalContent.length}`,
              );
              contentRef.current = finalContent;
              updateCache(finalContent);
              refreshContent();
            } else {
              console.error(
                `HtmlPreview - Document ${documentId} has no content`,
              );
              setError('Document has no content');
            }
          } catch (e) {
            console.error('Error fetching document:', e);

            // Utiliser le contenu par défaut en cas d'erreur
            console.log(
              `HtmlPreview - Using default content due to error for document ${documentId}`,
            );
            contentRef.current = JSON.stringify(defaultHtmlContent);
            updateCache(contentRef.current);
            refreshContent();

            setError(
              `Failed to load document content: ${e instanceof Error ? e.message : 'Unknown error'}`,
            );
          }
        } catch (e) {
          console.error('Error in fetchDocument:', e);
          setError(
            `Failed to load document: ${e instanceof Error ? e.message : 'Unknown error'}`,
          );
        } finally {
          setIsLoading(false);
        }
      };

      fetchDocument();
    }
  }, [documentId, updateCache, refreshContent]);

  // Gérer le chargement de l'iframe
  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  return (
    <div className="flex flex-1 relative size-full">
      <div className="absolute inset-0">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/80 dark:bg-zinc-800/80 z-10">
            <div className="animate-spin text-primary">
              <LoaderIcon size={32} />
            </div>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-white/90 dark:bg-zinc-800/90 z-10 p-4 text-red-500">
            <p className="mb-2">{error}</p>
            <button
              type="button"
              onClick={() => setRefreshKey((prev) => prev + 1)}
              className="px-3 py-1 bg-primary text-white rounded hover:bg-primary/90"
            >
              Retry
            </button>
          </div>
        )}

        <iframe
          ref={iframeRef}
          srcDoc={htmlContent}
          className="size-full border-none"
          sandbox="allow-scripts allow-same-origin"
          title={title}
          onLoad={handleIframeLoad}
          key={`${documentId}-${refreshKey}`} // Utiliser l'ID du document et la clé de rafraîchissement comme clé
        />

        <button
          type="button"
          onClick={() => setRefreshKey((prev) => prev + 1)}
          className="absolute top-2 right-2 z-10 p-1 bg-white/80 dark:bg-zinc-800/80 rounded-full hover:bg-white dark:hover:bg-zinc-700 transition-colors"
          title="Refresh HTML preview"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="size-4"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>
    </div>
  );
}
