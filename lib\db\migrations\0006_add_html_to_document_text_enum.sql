-- Migration pour ajouter 'html' à l'enum de la colonne 'text' dans la table Document
-- Cette migration vérifie d'abord si la valeur 'html' existe déjà dans la contrainte

DO $$
BEGIN
    -- Vérifier si la contrainte existe
    IF EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'Document_text_check'
    ) THEN
        -- Supprimer la contrainte existante
        ALTER TABLE "Document" DROP CONSTRAINT "Document_text_check";
        
        -- Recréer la contrainte avec la valeur 'html'
        ALTER TABLE "Document" ADD CONSTRAINT "Document_text_check" 
            CHECK (("text" = 'text'::varchar OR "text" = 'code'::varchar OR "text" = 'image'::varchar OR "text" = 'sheet'::varchar OR "text" = 'html'::varchar));
        
        RAISE NOTICE 'Contrainte Document_text_check mise à jour avec succès pour inclure la valeur html';
    ELSE
        RAISE NOTICE 'La contrainte Document_text_check n''existe pas, aucune action nécessaire';
    END IF;
END
$$;
