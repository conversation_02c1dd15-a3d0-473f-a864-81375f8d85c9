import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { query } = await request.json();
    
    if (!query) {
      return NextResponse.json({ error: 'Query is required' }, { status: 400 });
    }

    const apiKey = process.env.SERPER_API;
    if (!apiKey) {
      console.warn('Serper API key not found');
      return NextResponse.json({ error: 'API key not configured' }, { status: 500 });
    }

    const url = `https://google.serper.dev/images?q=${encodeURIComponent(query)}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-API-KEY': apiKey,
        'Content-Type': 'application/json',
      },
      redirect: 'follow',
    });

    if (!response.ok) {
      throw new Error(`Serper API error: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.images && data.images.length > 0) {
      // Filter for high-quality images
      const goodImages = data.images.filter((img: any) => 
        img.imageUrl && 
        !img.imageUrl.includes('icon') && 
        !img.imageUrl.includes('thumb') &&
        !img.imageUrl.includes('logo')
      );
      
      const imageUrl = goodImages.length > 0 ? goodImages[0].imageUrl : data.images[0].imageUrl;
      
      return NextResponse.json({ imageUrl });
    }
    
    return NextResponse.json({ error: 'No images found' }, { status: 404 });
  } catch (error) {
    console.error('Error fetching Serper image:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
