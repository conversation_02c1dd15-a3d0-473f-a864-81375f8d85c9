'use client';

// Import de l'icône Map avec un alias pour éviter le conflit avec l'objet global Map
import { Map as MapIcon } from 'lucide-react';
import { BorderBeam } from '@/components/magicui/border-beam';
import { cn } from '@/lib/utils';

// Composants de base
const Card = ({
  children,
  className = '',
}: {
  children: React.ReactNode;
  className?: string;
}) => (
  <div
    className={cn(
      'rounded-lg border bg-white dark:bg-gray-950 overflow-hidden',
      className,
    )}
  >
    {children}
  </div>
);

const CardContent = ({
  children,
  className = '',
}: {
  children: React.ReactNode;
  className?: string;
}) => <div className={cn('p-6', className)}>{children}</div>;

const TextShimmer = ({
  children,
  className = '',
  duration = 2,
}: {
  children: React.ReactNode;
  className?: string;
  duration?: number;
}) => <div className={cn('animate-pulse', className)}>{children}</div>;

// Props du composant MapLoadingState
interface MapLoadingStateProps {
  /**
   * Icône à afficher (par défaut: icône Map)
   */
  icon?: React.ComponentType<{ className?: string }>;
  /**
   * Texte à afficher pendant le chargement
   */
  text?: string;
  /**
   * Couleur du thème (par défaut: vert pour les cartes)
   */
  color?: 'red' | 'blue' | 'green' | 'purple';
  /**
   * Classes CSS supplémentaires
   */
  className?: string;
}

export const MapLoadingState = ({
  icon: Icon = MapIcon,
  text = 'Recherche de lieux sur la carte...',
  color = 'green',
  className = '',
}: MapLoadingStateProps) => {
  // Définition des variantes de couleurs
  const colorVariants = {
    green: {
      background: 'bg-green-50 dark:bg-green-950/50',
      text: 'text-green-600 dark:text-green-400',
      icon: 'text-green-600 dark:text-green-400',
      border: 'border-green-200 dark:border-green-900',
      variant: 'green' as const,
    },
    blue: {
      background: 'bg-blue-50 dark:bg-blue-950/50',
      text: 'text-blue-600 dark:text-blue-400',
      icon: 'text-blue-600 dark:text-blue-400',
      border: 'border-blue-200 dark:border-blue-900',
      variant: 'blue' as const,
    },
    red: {
      background: 'bg-red-50 dark:bg-red-950/50',
      text: 'text-red-600 dark:text-red-400',
      icon: 'text-red-600 dark:text-red-400',
      border: 'border-red-200 dark:border-red-900',
      variant: 'red' as const,
    },
    purple: {
      background: 'bg-purple-50 dark:bg-purple-950/50',
      text: 'text-purple-600 dark:text-purple-400',
      icon: 'text-purple-600 dark:text-purple-400',
      border: 'border-purple-200 dark:border-purple-900',
      variant: 'blue' as const, // Utilisation de blue comme fallback pour purple
    },
  };

  const colorVariant = colorVariants[color] || colorVariants.green;

  return (
    <Card
      className={cn(
        'w-full h-[100px] my-4 shadow-none border-0 relative overflow-hidden',
        className,
      )}
    >
      <BorderBeam duration={4} variant={colorVariant.variant} className="z-0" />
      <CardContent className="px-6 relative z-10">
        <div className="relative flex items-center justify-between h-full">
          <div className="flex items-center gap-3">
            <div
              className={cn(
                'relative h-10 w-10 rounded-full flex items-center justify-center',
                colorVariant.background,
                'border',
                colorVariant.border,
              )}
            >
              <BorderBeam
                duration={6}
                size={50}
                variant="green"
                className="absolute inset-0 m-auto"
              />
              <Icon
                className={cn('h-5 w-5 relative z-10', colorVariant.icon)}
              />
            </div>
            <div className="space-y-2">
              <TextShimmer
                className={cn('text-base font-medium', colorVariant.text)}
                duration={2}
              >
                {text}
              </TextShimmer>
              <div className="flex gap-2">
                {[60, 45, 30].map((width) => (
                  <div
                    key={`pulse-${width}`}
                    className="h-1.5 rounded-full bg-neutral-200 dark:bg-neutral-700 animate-pulse"
                    style={{
                      width: `${width}px`,
                      animationDelay: `${(width % 3) * 0.2}s`,
                    }}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MapLoadingState;
