import { tool } from 'ai';
import { z } from 'zod';
import { MemoryClient } from 'mem0ai';
import { getDocumentById, getDocumentsById } from '@/lib/db/queries';

export const memoryRetrieval = tool({
  description: 'Retrieve document content and history from memory',
  parameters: z.object({
    documentId: z
      .string()
      .optional()
      .describe('The ID of the document to retrieve information about'),
    documentTitle: z
      .string()
      .optional()
      .describe('The title of the document to retrieve information about'),
  }),
  execute: async ({ documentId, documentTitle }) => {
    // Essayer d'abord de récupérer le document depuis la base de données
    try {
      if (documentId) {
        // Récupérer le document le plus récent
        const latestDoc = await getDocumentById({ id: documentId });
        if (latestDoc) {
          // Récupérer l'historique du document
          const docs = await getDocumentsById({ id: documentId });

          return {
            id: latestDoc.id,
            title: latestDoc.title,
            kind: latestDoc.kind || 'text',
            content: latestDoc.content || '',
            history: docs.map((doc) => ({
              action: 'update',
              timestamp: doc.createdAt.toISOString(),
              content: doc.content || '',
            })),
          };
        }
      }
      // Note: La recherche par titre n'est pas implémentée dans les fonctions de requête existantes
    } catch (dbError) {
      console.error('Failed to retrieve from database:', dbError);
      // Continue to try memory if DB fails
    }

    // Si la base de données n'a pas fonctionné, essayer la mémoire
    const apiKey = process.env.MEM0_API_KEY;
    const orgId = process.env.MEM0_ORG_ID;
    const projectId = process.env.MEM0_PROJECT_ID;

    if (!apiKey || !orgId || !projectId) {
      // Au lieu de retourner null, retourner un message d'erreur explicite
      return {
        error:
          "Configuration de mémoire manquante. Veuillez configurer les variables d'environnement MEM0_API_KEY, MEM0_ORG_ID et MEM0_PROJECT_ID.",
      };
    }

    try {
      const memoryClient = new MemoryClient({ apiKey });

      let query = '';

      if (documentId) {
        query = `documentId:${documentId}`;
      } else if (documentTitle) {
        query = `documentTitle:"${documentTitle}"`;
      } else {
        return null;
      }

      const memories = await memoryClient.search(query, {
        org_id: orgId,
        project_id: projectId,
        user_id: 'system',
        limit: 10,
        api_version: 'v2',
      });

      if (!memories || memories.length === 0) {
        return null;
      }

      // Trier les souvenirs par horodatage
      memories.sort((a, b) => {
        const timestampA = a.metadata?.timestamp
          ? new Date(a.metadata.timestamp).getTime()
          : 0;
        const timestampB = b.metadata?.timestamp
          ? new Date(b.metadata.timestamp).getTime()
          : 0;
        return timestampA - timestampB;
      });

      // Trouver le contenu du document (celui avec isDocumentContent: true)
      const documentContentMemory = memories.find(
        (memory) => memory.metadata?.isDocumentContent === true,
      );

      if (!documentContentMemory || !documentContentMemory.memory) {
        return null;
      }

      // Extraire les informations pertinentes
      return {
        id: memories[0].metadata?.documentId || documentId || 'unknown',
        title:
          memories[0].metadata?.documentTitle ||
          documentTitle ||
          'Unknown Document',
        kind: memories[0].metadata?.documentKind || 'text',
        content: documentContentMemory.memory,
        history: memories.map((memory) => ({
          action: memory.metadata?.action,
          timestamp: memory.metadata?.timestamp,
          content: memory.memory,
        })),
      };
    } catch (error) {
      console.error('Failed to retrieve from memory:', error);
      return null;
    }
  },
});
