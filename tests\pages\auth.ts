import type { Page } from '@playwright/test';
import { expect } from '../fixtures';

export class AuthPage {
  constructor(private page: Page) {}

  async gotoLogin() {
    await this.page.goto('/login');
    await expect(this.page.getByRole('heading')).toContainText('Sign In');
  }

  async gotoRegister() {
    await this.page.goto('/register');
    await expect(this.page.getByRole('heading')).toContainText('Sign Up');
  }

  async register(email: string, password: string) {
    await this.gotoRegister();
    await this.page.getByPlaceholder('<EMAIL>').click();
    await this.page.getByPlaceholder('<EMAIL>').fill(email);
    await this.page.getByLabel('Password').click();
    await this.page.getByLabel('Password').fill(password);
    await this.page.getByRole('button', { name: 'Sign Up' }).click();
  }

  async login(email: string, password: string) {
    console.log('Starting login process with email:', email);
    await this.gotoLogin();
    console.log('Navigated to login page');

    await this.page.getByPlaceholder('<EMAIL>').click();
    await this.page.getByPlaceholder('<EMAIL>').fill(email);
    console.log('Filled email field');

    await this.page.getByLabel('Password').click();
    await this.page.getByLabel('Password').fill(password);
    console.log('Filled password field');

    // Compter combien de boutons correspondent à "Sign in"
    const signInButtons = await this.page
      .getByRole('button', { name: /sign in/i })
      .count();
    console.log(`Found ${signInButtons} buttons matching "Sign in"`);

    // Compter combien de boutons correspondent exactement à "Sign in"
    const exactSignInButtons = await this.page
      .getByRole('button', { name: 'Sign in', exact: true })
      .count();
    console.log(
      `Found ${exactSignInButtons} buttons matching exactly "Sign in"`,
    );

    // Prendre une capture d'écran pour déboguer
    try {
      await this.page.screenshot({ path: 'login-page.png' });
      console.log('Screenshot saved as login-page.png');
    } catch (screenshotError) {
      console.log('Failed to take screenshot:', screenshotError);
    }

    // Essayer plusieurs approches pour cliquer sur le bouton de connexion
    console.log('Listing all buttons on the page:');
    const allButtons = await this.page.locator('button').all();
    for (let i = 0; i < allButtons.length; i++) {
      const buttonText = await allButtons[i].textContent();
      console.log(`Button ${i}: "${buttonText?.trim()}"`);
    }

    // Approche 1: utiliser le sélecteur exact
    try {
      console.log('Trying to click on the first exact "Sign in" button');
      await this.page
        .getByRole('button', { name: 'Sign in', exact: true })
        .first()
        .click();
      console.log('Approach 1 succeeded');
      return;
    } catch (error) {
      console.log('Approach 1 failed:', error);
    }

    // Approche 2: utiliser le type submit
    try {
      console.log('Trying to click on submit button');
      await this.page.locator('button[type="submit"]').click();
      console.log('Approach 2 succeeded');
      return;
    } catch (submitError) {
      console.log('Approach 2 failed:', submitError);
    }

    // Approche 3: utiliser un sélecteur CSS plus spécifique
    try {
      console.log('Trying to click using CSS selector');
      await this.page.locator('.bg-primary.text-primary-foreground').click();
      console.log('Approach 3 succeeded');
      return;
    } catch (cssError) {
      console.log('Approach 3 failed:', cssError);
    }

    // Approche 4: cliquer sur le bouton par son texte partiel
    try {
      console.log('Trying to click on button containing "Sign in"');
      await this.page
        .locator('button', { hasText: /sign in/i })
        .first()
        .click();
      console.log('Approach 4 succeeded');
      return;
    } catch (textError) {
      console.log('Approach 4 failed:', textError);
    }

    // Si toutes les approches échouent, lancer une erreur
    console.log('All approaches failed, throwing error');
    throw new Error('All approaches to click the login button failed');
  }

  async logout(email: string, password: string) {
    await this.login(email, password);
    await this.page.waitForURL('/');

    await this.openSidebar();

    const userNavButton = this.page.getByTestId('user-nav-button');
    await expect(userNavButton).toBeVisible();

    await userNavButton.click();
    const userNavMenu = this.page.getByTestId('user-nav-menu');
    await expect(userNavMenu).toBeVisible();

    const authMenuItem = this.page.getByTestId('user-nav-item-auth');
    await expect(authMenuItem).toContainText('Sign out');

    await authMenuItem.click();

    const userEmail = this.page.getByTestId('user-email');
    await expect(userEmail).toContainText('Guest');
  }

  async expectToastToContain(text: string) {
    // Utiliser une approche qui fonctionne avec plusieurs toasts
    const toasts = this.page.getByTestId('toast');

    // Vérifier si au moins un toast contient le texte attendu
    const count = await toasts.count();
    let found = false;

    for (let i = 0; i < count; i++) {
      const toastText = await toasts.nth(i).textContent();
      if (toastText?.includes(text)) {
        found = true;
        break;
      }
    }

    // Échouer si aucun toast ne contient le texte attendu
    expect(found).toBeTruthy();
  }

  async openSidebar() {
    const sidebarToggleButton = this.page.getByTestId('sidebar-toggle-button');
    await sidebarToggleButton.click();
  }
}
