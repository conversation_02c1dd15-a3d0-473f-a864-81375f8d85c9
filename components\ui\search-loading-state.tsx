'use client';

import { BorderBeam } from '@/components/magicui/border-beam';
import type { Icon } from '@phosphor-icons/react';

// Utility function to merge class names
const cn = (...classes: (string | undefined)[]) => {
  return classes.filter(Boolean).join(' ');
};

// Base Components
const Card = ({
  children,
  className = '',
}: {
  children: React.ReactNode;
  className?: string;
}) => (
  <div
    className={cn(
      'rounded-lg border bg-white dark:bg-gray-950 relative overflow-hidden',
      className,
    )}
  >
    {children}
  </div>
);

const CardContent = ({
  children,
  className = '',
}: {
  children: React.ReactNode;
  className?: string;
}) => <div className={cn('p-6', className)}>{children}</div>;

const TextShimmer = ({
  children,
  className = '',
  duration = 2,
}: {
  children: React.ReactNode;
  className?: string;
  duration?: number;
}) => <div className={cn('animate-pulse', className)}>{children}</div>;

// Main Component
interface SearchLoadingStateProps {
  icon?: React.ComponentType<{ className?: string; weight?: string }> | Icon;
  text?: string;
  color?: 'blue' | 'green' | 'purple' | 'red'; // Added 'red' as it's used in the component
  className?: string;
}

export const SearchLoadingState = ({
  icon: Icon,
  text = 'Searching the web...',
  color = 'blue',
  className = '',
}: SearchLoadingStateProps) => {
  // Si aucune icône n'est fournie, on utilise une div vide
  const IconComponent = Icon || (() => <div className="w-5 h-5" />);
  const colorVariants = {
    blue: {
      background: 'bg-blue-50 dark:bg-blue-950/50',
      text: 'text-blue-600 dark:text-blue-400',
      icon: 'text-blue-600 dark:text-blue-400',
      border: 'border-blue-200 dark:border-blue-900',
      variant: 'blue' as const,
    },
    green: {
      background: 'bg-green-50 dark:bg-green-950/50',
      text: 'text-green-600 dark:text-green-400',
      icon: 'text-green-600 dark:text-green-400',
      border: 'border-green-200 dark:border-green-900',
      variant: 'green' as const,
    },
    purple: {
      background: 'bg-purple-50 dark:bg-purple-950/50',
      text: 'text-purple-600 dark:text-purple-400',
      icon: 'text-purple-600 dark:text-purple-400',
      border: 'border-purple-200 dark:border-purple-900',
      variant: 'blue' as const, // Utilisation de blue comme fallback pour purple
    },
    red: {
      background: 'bg-red-50 dark:bg-red-950/50',
      text: 'text-red-600 dark:text-red-400',
      icon: 'text-red-600 dark:text-red-400',
      border: 'border-red-200 dark:border-red-900',
      variant: 'red' as const,
    },
  };

  const colorVariant = colorVariants[color] || colorVariants.blue;

  return (
    <Card
      className={cn('w-full h-[100px] my-4 shadow-none border-0', className)}
    >
      <BorderBeam duration={4} variant={colorVariant.variant} className="z-0" />
      <CardContent className="px-6 relative z-10">
        <div className="relative flex items-center justify-between h-full">
          <div className="flex items-center gap-3">
            <div
              className={cn(
                'relative h-10 w-10 rounded-full flex items-center justify-center',
                colorVariant.background,
                'border',
                colorVariant.border,
              )}
            >
              <BorderBeam
                duration={6}
                size={50}
                variant="blue"
                className="absolute inset-0 m-auto"
              />
              <IconComponent
                className={cn('h-5 w-5 relative z-10', colorVariant.icon)}
                weight="duotone"
              />
            </div>
            <div className="space-y-2">
              <TextShimmer
                className={cn('text-base font-medium', colorVariant.text)}
                duration={2}
              >
                {text}
              </TextShimmer>
              <div className="flex gap-2">
                {[60, 45, 30].map((width) => (
                  <div
                    key={`pulse-${width}`}
                    className="h-1.5 rounded-full bg-neutral-200 dark:bg-neutral-700 animate-pulse"
                    style={{
                      width: `${width}px`,
                      animationDelay: `${(width % 3) * 0.2}s`,
                    }}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SearchLoadingState;
