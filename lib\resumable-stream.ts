import { createClient } from 'redis';
import { generateUUID } from './utils';
import { isTestEnvironment } from './constants';

// Define a simplified interface for our Redis client usage
interface CommonRedisInterface {
  connect: () => Promise<unknown>;
  get: (key: string) => Promise<string | null>;
  set: (key: string, value: string) => Promise<unknown>;
  incr: (key: string) => Promise<number>;
  del: (key: string) => Promise<number>;
  publish: (channel: string, message: string) => Promise<number>;
  subscribe: (
    channel: string,
    callback: (message: string) => void,
  ) => Promise<void>;
  unsubscribe: (channel: string) => Promise<void>;
}

// Create mock Redis client for test environments
const createMockRedisClient = (): CommonRedisInterface => {
  const storage = new Map<string, string>();
  const subscribers = new Map<string, Array<(message: string) => void>>();

  return {
    connect: async () => Promise.resolve(),
    get: async (key: string) => storage.get(key) || null,
    set: async (key: string, value: string) => {
      storage.set(key, value);
      return 'OK';
    },
    incr: async (key: string) => {
      const value = storage.get(key)
        ? Number.parseInt(storage.get(key) as string, 10) + 1
        : 1;
      storage.set(key, value.toString());
      return value;
    },
    del: async (key: string) => {
      storage.delete(key);
      return 1;
    },
    publish: async (channel: string, message: string) => {
      const channelSubscribers = subscribers.get(channel) || [];
      channelSubscribers.forEach((callback) => callback(message));
      return channelSubscribers.length;
    },
    subscribe: async (channel: string, callback: (message: string) => void) => {
      if (!subscribers.has(channel)) {
        subscribers.set(channel, []);
      }
      subscribers.get(channel)?.push(callback);
    },
    unsubscribe: async (channel: string) => {
      subscribers.delete(channel);
    },
  };
};

let redisPublisher: CommonRedisInterface;
let redisSubscriber: CommonRedisInterface;
let connectPromise: Promise<unknown[]>;

// Check if we're in a test environment
if (isTestEnvironment) {
  // Use mock Redis clients for tests
  redisPublisher = createMockRedisClient();
  redisSubscriber = createMockRedisClient();
  connectPromise = Promise.all([
    redisPublisher.connect(),
    redisSubscriber.connect(),
  ]);
} else {
  // Use real Redis clients for production/development
  const redisUrl = process.env.REDIS_KV_URL;

  if (!redisUrl) {
    throw new Error('REDIS_KV_URL environment variable is not set');
  }

  redisPublisher = createClient({
    url: redisUrl,
  });

  redisSubscriber = createClient({
    url: redisUrl,
  });

  connectPromise = Promise.all([
    redisSubscriber.connect(),
    redisPublisher.connect(),
  ]);
}

interface ResumeStreamMessage {
  listenerId: string;
}

const DONE_MESSAGE = 'STREAM_DONE';

export async function createResumableStream({
  chatId,
  stream,
}: {
  chatId: string;
  stream?: ReadableStream<string>;
}) {
  const lines: Array<string> = [];
  const listenerChannels = new Set<string>();

  await connectPromise;

  const currentListenerCount = await redisPublisher.get(
    `stream:room:${chatId}`,
  );

  if (!currentListenerCount && !stream) {
    return null;
  }

  if (!currentListenerCount && stream) {
    await redisPublisher.incr(`stream:room:${chatId}`);
  }

  if (Number.parseInt(currentListenerCount ?? '0') > 0 && !stream) {
    return resumeStream({ chatId });
  }

  await redisSubscriber.subscribe(
    `stream:join:${chatId}`,
    async (message: string) => {
      const { listenerId } = JSON.parse(message) as ResumeStreamMessage;
      listenerChannels.add(listenerId);

      redisPublisher.publish(`stream:content:${listenerId}`, lines.join(''));
    },
  );

  if (!stream) {
    return null;
  }

  const reader = stream.getReader();

  const responseStream = new ReadableStream<string>({
    async start(controller) {
      try {
        while (true) {
          const { done, value } = await reader.read();

          const promises: Array<Promise<unknown>> = [];

          if (done) {
            promises.push(redisPublisher.del(`stream:room:${chatId}`));
            promises.push(redisSubscriber.unsubscribe(`stream:join:${chatId}`));

            for (const listenerId of listenerChannels) {
              promises.push(
                redisPublisher.publish(
                  `stream:content:${listenerId}`,
                  DONE_MESSAGE,
                ),
              );
            }

            await Promise.all(promises);

            controller.close();
            break;
          } else {
            lines.push(value);
            controller.enqueue(value);

            for (const listenerId of listenerChannels) {
              promises.push(
                redisPublisher.publish(`stream:content:${listenerId}`, value),
              );
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    },
  });

  return responseStream;
}

export async function resumeStream({ chatId }: { chatId: string }) {
  const listenerId = generateUUID();

  await redisPublisher.incr(`stream:room:${chatId}`);

  const resumedStream = new ReadableStream({
    async start(controller) {
      await redisSubscriber.subscribe(
        `stream:content:${listenerId}`,
        async (message) => {
          if (message === DONE_MESSAGE) {
            controller.close();
          } else {
            controller.enqueue(message);
          }
        },
      );

      await redisPublisher.publish(
        `stream:join:${chatId}`,
        JSON.stringify({
          listenerId,
        }),
      );
    },
  });

  return resumedStream;
}
